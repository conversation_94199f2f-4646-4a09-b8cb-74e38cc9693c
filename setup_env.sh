#!/bin/bash
# Setup script for FlowRAG Library environment

# Get absolute path of the script directory
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )"

# Define color codes for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# Print banner
echo -e "${GREEN}================================================${NC}"
echo -e "${GREEN}        FlowRAG Library Environment Setup         ${NC}"
echo -e "${GREEN}================================================${NC}"
echo ""

# Check for virtual environment
if [ -d "$SCRIPT_DIR/venv" ]; then
    echo -e "${GREEN}✓${NC} Found virtual environment at $SCRIPT_DIR/venv"
    
    # Activate virtual environment
    echo "Activating virtual environment..."
    source "$SCRIPT_DIR/venv/bin/activate"
    echo -e "${GREEN}✓${NC} Virtual environment activated"
else
    echo -e "${YELLOW}!${NC} No virtual environment found at $SCRIPT_DIR/venv"
    read -p "Do you want to create a new virtual environment? (y/n) " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        echo "Creating new virtual environment..."
        python3 -m venv "$SCRIPT_DIR/venv"
        if [ $? -ne 0 ]; then
            echo -e "${RED}✗${NC} Failed to create virtual environment"
            exit 1
        fi
        echo -e "${GREEN}✓${NC} Created virtual environment"
        
        # Activate virtual environment
        echo "Activating virtual environment..."
        source "$SCRIPT_DIR/venv/bin/activate"
        echo -e "${GREEN}✓${NC} Virtual environment activated"

        # Install requirements
        if [ -f "$SCRIPT_DIR/requirements.txt" ]; then
            echo "Installing requirements from requirements.txt..."
            pip install -r "$SCRIPT_DIR/requirements.txt"
            if [ $? -ne 0 ]; then
                echo -e "${RED}✗${NC} Failed to install requirements"
                exit 1
            fi
            echo -e "${GREEN}✓${NC} Installed requirements"
        else
            echo -e "${YELLOW}!${NC} No requirements.txt file found"
        fi
    else
        echo "Skipping virtual environment creation"
    fi
fi

# Add the current directory to PYTHONPATH
export PYTHONPATH="$SCRIPT_DIR:$PYTHONPATH"
echo -e "${GREEN}✓${NC} Added $SCRIPT_DIR to PYTHONPATH"

# Check for .env existence and required variables
ENV_FILE="$SCRIPT_DIR/.env"
ENV_EXAMPLE_FILE="$SCRIPT_DIR/.env.example"
REQUIRED_VARS=(CLIENT_ID CLIENT_SECRET APP_TO_ACCESS FLOW_TENANT)

if [ ! -f "$ENV_FILE" ]; then
    cp "$ENV_EXAMPLE_FILE" "$ENV_FILE"
    echo -e "${RED}✗${NC} .env file not found! A template has been created for you."
    echo "Please fill in the required variables in .env before continuing."
    exit 1
fi

# Load .env variables
export $(grep -v '^#' "$ENV_FILE" | xargs)

# Check required variables are set and not placeholders
MISSING_VARS=()
for var in "${REQUIRED_VARS[@]}"; do
    value=$(eval echo \$$var)
    if [ -z "$value" ] || [[ "$value" == "your-*" ]] || [[ "$value" == "app-to-access" ]] || [[ "$value" == "your-tenant" ]]; then
        MISSING_VARS+=("$var")
    fi
done

if [ ${#MISSING_VARS[@]} -gt 0 ]; then
    echo -e "${RED}✗${NC} The following required variables are missing or not properly set in .env: ${MISSING_VARS[*]}"
    echo "** Please edit .env and fill in the required values before continuing."
    exit 1
fi

echo -e "${GREEN}✓${NC} All required environment variables are set."

# Load .env variables if exists
if [ -f "$SCRIPT_DIR/.env" ]; then
    export $(grep -v '^#' "$SCRIPT_DIR/.env" | xargs)
    echo -e "${GREEN}✓${NC} Loaded environment variables from .env"
fi

# Check for Flow API keys (mandatory)
if [ -n "$CLIENT_ID" ] && [ -n "$CLIENT_SECRET" ] && [ -n "$APP_TO_ACCESS" ] && [ -n "$FLOW_TENANT" ]; then
    echo -e "${GREEN}✓${NC} Flow API keys are set"
else
    echo -e "${YELLOW}!${NC} Flow API keys are not set. Some features may not work."
    echo "You need to set them with:"
    echo "  export CLIENT_ID=\"your-client-id\""
    echo "  export CLIENT_SECRET=\"your-client-secret\""
    echo "  export APP_TO_ACCESS=\"your-app-to-access\""
    echo "  export FLOW_TENANT=\"your-flow-tenant\""
fi

echo ""
echo -e "${GREEN}================================================${NC}"
echo -e "${GREEN}         Environment Setup Complete!!           ${NC}"
echo -e "${GREEN}================================================${NC}"
echo ""
echo "To use the FlowRAG Library:"
echo ""
echo "1. Index a codebase and start the web interface:"
echo "   ./index.sh /path/to/your/codebase"
echo ""
echo "2. Or run each step manually:"
echo "   - Indexing codebase:"
echo "     python -m flow_rag.infrastructure.web.entrypoints.index_code /path/to/your/codebase"
echo "   - Starting web server:"
echo "     python -m flow_rag.infrastructure.web.entrypoints.run_web /path/to/your/codebase"
echo "   - Querying codebase:"
echo "     python -m flow_rag.infrastructure.web.entrypoints.query_code /path/to/your/codebase"
echo ""
