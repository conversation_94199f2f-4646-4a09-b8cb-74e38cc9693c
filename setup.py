from setuptools import setup, find_packages

setup(
    name="flow_rag",
    version="1.0.0",
    description="A powerful library for semantic code search and natural language querying of codebases with hybrid search capabilities",
    long_description=open("README.md").read(),
    long_description_content_type="text/markdown",
    author="<PERSON><PERSON><PERSON>",
    author_email="<EMAIL>",
    url="https://bitbucket.org/ciandt_it/pyflow_rag",
    packages=find_packages(include=["flow_rag", "flow_rag.*"]),
    classifiers=[
        "Development Status :: 3 - Alpha",
        "Intended Audience :: Developers",
        "License :: OSI Approved :: MIT License",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.10",
    ],
    python_requires=">=3.10",
    install_requires=[
        "flask>=2.0.0",
        "markdown>=3.0.0",
        "numpy<2.0.0",
        "openai>=1.0.0",
        "python-dotenv>=1.0.0",
        "requests>=2.28.0",
        "tenacity>=8.0.0",
        "tiktoken>=0.5.0",
        "tree-sitter==0.21.3",
        "tree_sitter_languages>=1.0.0",
        "tqdm>=4.64.0",
    ],
    extras_require={
        "dev": [
            "pytest>=8.3.5",
            "black",
            "isort",
            "mypy",
            "flake8",
        ],
    },
    entry_points={
        "console_scripts": [
            "flowrag-index=flow_rag.infrastructure.web.entrypoints.index_code:main",
            "flowrag-query=flow_rag.infrastructure.web.entrypoints.query_code:main",
            "flowrag-web=flow_rag.infrastructure.web.entrypoints.run_web:main",
        ],
    },
)