#!/usr/bin/env python3
"""
Context-Only Example

Shows how to get relevant code context without AI processing.
Perfect for custom prompts or when you want just the relevant code.
"""

import os
import flow_rag

def main():
    # Configure codebase path - change this to your actual codebase
    codebase_path = "/path/to/your/codebase"

    if not os.path.exists(codebase_path):
        print("❌ Please update codebase_path to a valid directory")
        return

    print(f"📁 Analyzing: {os.path.basename(codebase_path)}")

    # Initialize codebase
    flow_rag.initialize(codebase_path)

    # Get context without AI processing (no API calls)
    query = "authentication functions"
    context = flow_rag.get_context(query, codebase_path)

    print(f"🔍 Context found: {len(context)} characters")

    # Save context to file
    with open("context_example_output.txt", "w", encoding="utf-8") as f:
        f.write(f"Query: {query}\n\n")
        f.write(f"Relevant Code Context:\n{context}")

    print("✅ Context saved to 'context_example_output.txt'")
    print("💡 Use this context with any LLM for custom prompts")

if __name__ == "__main__":
    main()
