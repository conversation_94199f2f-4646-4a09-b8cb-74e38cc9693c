#!/usr/bin/env python3
"""
Progress Monitoring Example

Shows how to monitor indexing progress in real-time.
"""

import os
import flow_rag

def progress_callback(percentage):
    """Simple progress bar display."""
    bar_length = 20
    filled = int(bar_length * percentage // 100)
    bar = '█' * filled + '-' * (bar_length - filled)
    print(f'\r|{bar}| {percentage}%', end='', flush=True)
    if percentage == 100:
        print()  # New line when complete

def main():
    # Configure codebase path - change this to your actual codebase
    codebase_path = "/path/to/your/codebase"

    if not os.path.exists(codebase_path):
        print("❌ Please update codebase_path to a valid directory")
        return

    print(f"📁 Indexing: {os.path.basename(codebase_path)}")

    # Initialize with progress monitoring
    flow_rag.initialize(
        codebase_path,
        progress_callback=progress_callback
    )

    print("✅ Indexing complete!")

    # Test query
    result = flow_rag.query("What does this project do?", codebase_path)

    # Save result
    with open("progress_example_output.txt", "w", encoding="utf-8") as f:
        f.write(f"Result: {result}")

    print("✅ Result saved to 'progress_example_output.txt'")

if __name__ == "__main__":
    main()
