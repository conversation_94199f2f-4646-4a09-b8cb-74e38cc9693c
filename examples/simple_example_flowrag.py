#!/usr/bin/env python3
"""
Simple FlowRAG Example

Basic usage of FlowRAG v2.0 library.
"""

import os
import flow_rag

def main():
    # Configure codebase path - change this to your actual codebase
    codebase_path = "/path/to/your/codebase"

    if not os.path.exists(codebase_path):
        print("❌ Please update codebase_path to a valid directory")
        return

    print(f"📁 Analyzing: {os.path.basename(codebase_path)}")

    # Initialize codebase (indexes if needed)
    flow_rag.initialize(codebase_path)

    # Ask a simple question
    query = "What does this project do?"
    result = flow_rag.query(query, codebase_path)

    # Save result to file
    with open("simple_example_output.txt", "w", encoding="utf-8") as f:
        f.write(f"Query: {query}\n\n")
        f.write(f"Answer: {result}")

    print("✅ Result saved to 'simple_example_output.txt'")

if __name__ == "__main__":
    main()
