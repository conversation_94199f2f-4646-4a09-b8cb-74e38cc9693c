# FlowRAG v2.0 Examples

Simple, minimal examples demonstrating FlowRAG v2.0 features.

## Examples Overview

### 1. `simple_example_flowrag.py`

**Basic Usage**

- Initialize and query a codebase
- Simple AI-powered analysis
- Minimal code, maximum clarity

### 2. `simple_context_example.py`

**Context-Only Mode**

- Get relevant code without AI processing
- No API costs for context retrieval
- Perfect for custom prompts

### 3. `simple_progress_example.py`

**Progress Monitoring**

- Real-time indexing progress
- Progress bar implementation
- Useful for large codebases

### 4. `full_example_flowrag.py`

**Complete Features**

- All FlowRAG v2.0 capabilities
- Multiple query types
- Custom output formats
- Context and AI queries

## Quick Start

1. **Update codebase path** in any example:

   ```python
   codebase_path = "/path/to/your/codebase"  # Change this!
   ```

2. **Run example**:

   ```bash
   python examples/simple_example_flowrag.py
   ```

3. **Check output file** for results.

## Key Features Demonstrated

- **v2.0 API**: `flow_rag.initialize()` and `flow_rag.query()`
- **Context-only**: `flow_rag.get_context()` for no AI processing
- **Custom formats**: Use `user_prompt` for specific output formats
- **Progress monitoring**: Real-time indexing progress
- **File output**: All examples save results to text files

## Output Files Generated

- `simple_example_output.txt` - Basic query results
- `context_example_output.txt` - Context-only results
- `progress_example_output.txt` - Progress monitoring results
- `complete_example_output.txt` - All features demonstration

## Notes

- Examples require valid codebase paths (no fallback to current directory)
- All examples are minimal and focused on single features
- Output saved to files for easy review and understanding
