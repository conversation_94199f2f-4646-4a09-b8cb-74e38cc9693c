#!/bin/bash
# FlowRAG Library - Indexing and Web Interface Script
# This script indexes a codebase and launches the web interface

# Read environment variables from .env file
set -a
[ -f .env ] && . .env
set +a

# If codebase path is not provided, ask for the path
if [ ! -d "$1" ]; then
    read -p "Please, provide the path to the codebase: " folder_path
    if [ ! -d "$folder_path" ]; then
        echo -e "\033[1;31mError: The directory '$folder_path' does not exist.\033[0m"
        exit 1
    fi
else
    folder_path=$1
fi

# Set codebase path
CODEBASE_PATH=$folder_path

# Check if codebase path exists
if [ ! -d "$CODEBASE_PATH" ]; then
    echo -e "\033[1;31mError: Codebase path does not exist.\033[0m"
    exit 1
fi

# Check if codebase path is writable
if [ ! -w "$CODEBASE_PATH" ]; then
    echo -e "\033[1;31mError: Codebase path is not writable.\033[0m"
    exit 1
fi

# Checagem de API key
if [ -z "$CLIENT_ID" ] && [ -z "$CLIENT_SECRET" ]; then
    echo -e "\033[1;31mError: Flow API keys not found. Please set the environment variables.\033[0m"; exit 1;
fi

# Display banner
echo "================================================"
echo "           FlowRAG Library Indexer               "
echo "================================================"
echo "Codebase: $CODEBASE_PATH"
echo ""

# Step 1: Run indexing
echo -e "\033[1;34m## Step 1: Indexing codebase\033[0m"
python -m flow_rag.infrastructure.web.entrypoints.index_code "$CODEBASE_PATH"

# Check if indexing was successful
if [ $? -ne 0 ]; then
    echo -e "\033[1;31m## ERROR: Indexing failed. Check the logs for details.\033[0m"
    exit 1
fi

echo ""
echo -e "\033[1;32m## Indexing completed successfully!\033[0m"
echo ""

# Step 2: Launch web server
echo -e "\033[1;34m## Step 2: Starting web interface\033[0m"
echo "Starting web server for codebase: \"$CODEBASE_PATH\""
echo ""

# Launch web server
python -m flow_rag.infrastructure.web.entrypoints.run_web "$CODEBASE_PATH"

# Exit gracefully if server is stopped
echo ""
echo -e "\033[1;32m## Web server stopped.\033[0m"
echo "To restart the web server without re-indexing, run:"
echo "python -m flow_rag.infrastructure.web.entrypoints.run_web $CODEBASE_PATH"