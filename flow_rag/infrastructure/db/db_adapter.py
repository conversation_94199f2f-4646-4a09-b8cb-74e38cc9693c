import os
import sqlite3
import threading
from typing import List, Dict, Any, Optional
import logging
from flow_rag.infrastructure.log_utils import log_message
import traceback

class DatabaseAdapter:
    """
    Adapter for SQLite database operations.
    Handles chat context and history storage.
    
    Thread-safe implementation that creates connections per thread.
    """

    def __init__(self, cache_dir: str = None, logger: Optional[logging.Logger] = None):
        """
        Initialize the database adapter.
        
        Args:
            cache_dir: Directory where the cache database is stored
            logger: Optional logger instance
        """
        if cache_dir is None:
            # Default path within the infrastructure folder
            current_dir = os.path.dirname(os.path.abspath(__file__))
            cache_dir = os.path.join(current_dir, "cache")
        
        self.cache_dir = cache_dir
        self.db_path = os.path.join(cache_dir, "cache.db")
        self.logger = logger or logging.getLogger(__name__)
        
        # Thread-local storage for connections
        self.local = threading.local()
        
        # Make sure directory exists
        os.makedirs(cache_dir, exist_ok=True)
        
        # Initialize database
        self._init_db()
    
    def _init_db(self) -> None:
        """Initialize the database with required tables."""
        conn = self._get_connection()
        try:
            schema_path = os.path.join(self.cache_dir, "schema.sql")
            if os.path.exists(schema_path):
                with open(schema_path, "r") as f:
                    schema_sql = f.read()
                conn.executescript(schema_sql)
                conn.commit()
                log_message("Database initialized from schema file", logger=self.logger)
            else:
                # Fallback if schema file doesn't exist
                self._create_tables(conn)
                log_message("Database initialized with default schema", logger=self.logger)
        except Exception as e:
            log_message(f"Error initializing database: {e}", logger=self.logger, level="error")
            log_message(traceback.format_exc(), logger=self.logger, level="error")
        finally:
            self.close_connection()
    
    def _create_tables(self, conn: sqlite3.Connection) -> None:
        """Create required tables if they don't exist."""
        conn.execute('''
            CREATE TABLE IF NOT EXISTS chat_context (
                user_id TEXT PRIMARY KEY,
                context TEXT
            )
        ''')
        
        conn.execute('''
            CREATE TABLE IF NOT EXISTS chat_history (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id TEXT,
                query TEXT,
                response TEXT,
                timestamp DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        conn.commit()
    
    def _get_connection(self) -> sqlite3.Connection:
        """Get a thread-local connection to the SQLite database."""
        if not hasattr(self.local, 'connection') or self.local.connection is None:
            self.local.connection = sqlite3.connect(self.db_path)
            self.local.connection.row_factory = sqlite3.Row
        return self.local.connection
    
    def close_connection(self) -> None:
        """Close the current thread's database connection."""
        if hasattr(self.local, 'connection') and self.local.connection:
            self.local.connection.close()
            self.local.connection = None
    
    def store_context(self, user_id: str, context: str) -> bool:
        """
        Store context for a user.
        
        Args:
            user_id: Unique identifier for the user
            context: RAG context string to store
            
        Returns:
            True if successful, False otherwise
        """
        conn = None
        try:
            conn = self._get_connection()
            conn.execute(
                "INSERT OR REPLACE INTO chat_context (user_id, context) VALUES (?, ?)",
                (user_id, context)
            )
            conn.commit()
            return True
        except Exception as e:
            log_message(f"Error storing context: {e}", logger=self.logger, level="error")
            log_message(traceback.format_exc(), logger=self.logger, level="error")
            if conn:
                conn.rollback()
            return False
    
    def get_context(self, user_id: str) -> Optional[str]:
        """
        Retrieve context for a user.
        
        Args:
            user_id: Unique identifier for the user
            
        Returns:
            Stored context string or None if not found
        """
        try:
            conn = self._get_connection()
            result = conn.execute(
                "SELECT context FROM chat_context WHERE user_id = ?",
                (user_id,)
            ).fetchone()
            
            if result:
                return result["context"]
            return None
        except Exception as e:
            log_message(f"Error retrieving context: {e}", logger=self.logger, level="error")
            log_message(traceback.format_exc(), logger=self.logger, level="error")
            return None
    
    def add_chat_history(self, user_id: str, query: str, response: str) -> bool:
        """
        Add an entry to the chat history.
        
        Args:
            user_id: Unique identifier for the user
            query: User's query
            response: System's response
            
        Returns:
            True if successful, False otherwise
        """
        conn = None
        try:
            conn = self._get_connection()
            conn.execute(
                "INSERT INTO chat_history (user_id, query, response) VALUES (?, ?, ?)",
                (user_id, query, response)
            )
            conn.commit()
            return True
        except Exception as e:
            log_message(f"Error adding chat history: {e}", logger=self.logger, level="error")
            log_message(traceback.format_exc(), logger=self.logger, level="error")
            if conn:
                conn.rollback()
            return False
    
    def get_chat_history(self, user_id: str, limit: int = 5) -> List[Dict[str, Any]]:
        """
        Retrieve chat history for a user.
        
        Args:
            user_id: Unique identifier for the user
            limit: Maximum number of history items to retrieve
            
        Returns:
            List of chat history entries (newest first)
        """
        try:
            conn = self._get_connection()
            result = conn.execute(
                "SELECT query, response, timestamp FROM chat_history WHERE user_id = ? ORDER BY timestamp DESC LIMIT ?",
                (user_id, limit)
            ).fetchall()
            
            return [{"query": row["query"], "response": row["response"], "timestamp": row["timestamp"]} 
                   for row in result]
        except Exception as e:
            log_message(f"Error retrieving chat history: {e}", logger=self.logger, level="error")
            log_message(traceback.format_exc(), logger=self.logger, level="error")
            return []
    
    def clear_user_data(self, user_id: str) -> bool:
        """
        Clear all data associated with a user.
        
        Args:
            user_id: Unique identifier for the user
            
        Returns:
            True if successful, False otherwise
        """
        conn = None
        try:
            conn = self._get_connection()
            conn.execute("DELETE FROM chat_context WHERE user_id = ?", (user_id,))
            conn.execute("DELETE FROM chat_history WHERE user_id = ?", (user_id,))
            conn.commit()
            return True
        except Exception as e:
            log_message(f"Error clearing user data: {e}", logger=self.logger, level="error")
            log_message(traceback.format_exc(), logger=self.logger, level="error")
            if conn:
                conn.rollback()
            return False