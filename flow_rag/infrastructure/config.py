import os
from typing import Dict, Any
from pathlib import Path

# Get the root directory of the project
PROJECT_ROOT = Path(__file__).parent.parent.parent.resolve()
class ConfigManager:
    """
    Configuration manager for the FlowRAG library.

    Provides a centralized configuration system that can be loaded from
    environment variables, configuration files, or passed directly.
    """

    # Default configuration values
    DEFAULT_CONFIG = {
        # Embedding configuration
        "embedding": {
            "provider": 'jina', 
            "model_name": 'jina-embeddings-v3', 
            "api_key": os.getenv("JINA_API_KEY"),
            "dimension": 1024, 
            "max_tokens": 8192, 
            "batch_size": 32,
        },

        # LLM configuration
        "llm": {
            "provider": "flow", 
            "model_name": 'gpt-4o-mini', 
            "max_tokens": 118000,
            "temperature": 0.7,
        },

        # Vector store configuration
        "vector_store": {
            "provider": "hybrid",  # HybridStore for optimal performance
            "base_path": os.getenv("STORE_BASE_PATH") or str(PROJECT_ROOT / "rag_data"),
            # HybridStore configurations (optimized for production)
            "hybrid": {
                "db_path": os.getenv("STORE_BASE_PATH") or str(PROJECT_ROOT / "rag_data"),
                "enable_semantic_reranking": True,  # Enable intelligent reranking
                "semantic_reranking_alpha": 0.7,  # Optimal balance: 70% BM25, 30% semantic
                "embedding_timeout": 5,  # Timeout for embedding generation (seconds)
                "cache_max_age_days": 30,  # Max age for cached embeddings
                "bm25_threshold": 0.1,  # Minimum BM25 score to trigger semantic reranking
                "max_cache_size": 10000,  # Maximum number of cached embeddings
            }
        },

        # Query processing configuration (optimized for production)
        "query_processing": {
            "hyde_enabled": False,  # Disabled for better performance (direct queries work better)
            "reranking_enabled": True,  # Enable intelligent reranking
            "hybrid_search_enabled": True,  # Enable hybrid search (BM25 + embeddings)
            "hybrid_alpha": 0.5,  # Balance between vector (0.0) and text (1.0) search
            "max_results": 20,  # Maximum results from initial search
            "reranking_results": 7,  # Increase for better quality
        },

        # Web interface configuration
        "web": {
            "host": "127.0.0.1",
            "port": 5001,
            "debug": False,
            "cache_enabled": True,
        },

        # Logging configuration
        "logging": {
            "base_path": os.getenv("LOGS_BASE_PATH") or str(PROJECT_ROOT / "rag_logs"),
            "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        }
    }

    def __init__(self) -> None:
        """
        Initialize the configuration manager.
        """
        self.config = dict(self.DEFAULT_CONFIG)

    def get(self, key_path: str, default: Any = None) -> Any:
        """
        Get a configuration value by dot-separated path.

        Args:
            key_path: Dot-separated path to the configuration value (e.g., "embedding.provider")
            default: Default value to return if the path doesn't exist

        Returns:
            Configuration value or default
        """
        parts = key_path.split('.')
        current: Any = self.config

        for part in parts:
            if not isinstance(current, dict) or part not in current:
                return default
            current = current[part]

        return current

    def get_embedding_config(self) -> Dict[str, Any]:
        """Get embedding configuration."""
        config = self.config.get("embedding", {})
        return dict(config) if isinstance(config, dict) else {}

    def get_llm_config(self) -> Dict[str, Any]:
        """Get LLM configuration."""
        config = self.config.get("llm", {})
        return dict(config) if isinstance(config, dict) else {}

    def get_vector_store_config(self) -> Dict[str, Any]:
        """Get vector store configuration."""
        config = self.config.get("vector_store", {})
        return dict(config) if isinstance(config, dict) else {}

    def get_query_processing_config(self) -> Dict[str, Any]:
        """Get query processing configuration."""
        config = self.config.get("query_processing", {})
        return dict(config) if isinstance(config, dict) else {}

    def get_web_config(self) -> Dict[str, Any]:
        """Get web interface configuration."""
        config = self.config.get("web", {})
        return dict(config) if isinstance(config, dict) else {}

    def get_logging_config(self) -> Dict[str, Any]:
        """Get logging configuration."""
        config = self.config.get("logging", {})
        return dict(config) if isinstance(config, dict) else {}

    def set(self, key_path: str, value: Any) -> None:
        """
        Set a configuration value by dot-separated path.

        Args:
            key_path: Dot-separated path to the configuration value
            value: Value to set
        """
        parts = key_path.split('.')
        current = self.config

        for i, part in enumerate(parts[:-1]):
            if part not in current:
                current[part] = {}
            current = current[part]  # type: ignore

        current[parts[-1]] = value

    def as_dict(self) -> Dict[str, Any]:
        """Get the entire configuration as a dictionary."""
        return dict(self.config)


# Global configuration instance
_config_instance = None


def get_config() -> ConfigManager:
    """
    Get the global configuration instance.

    Returns:
        Configuration manager instance
    """
    global _config_instance

    if _config_instance is None:
        _config_instance = ConfigManager()

    return _config_instance
