<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <title>FlowRAG Library</title>
    <style>
      body {
        font-family: "Inter", -apple-system, BlinkMacSystemFont, sans-serif;
        margin: 0;
        padding: 0;
        background-color: #f8f9fa;
        background-image: linear-gradient(90deg, #e9ecef 1px, transparent 1px),
          linear-gradient(#e9ecef 1px, transparent 1px);
        background-size: 24px 24px;
        color: #212529;
        display: flex;
        flex-direction: column;
        align-items: center;
        height: calc(100vh - 120px);
        position: relative;
        padding-bottom: 80px;
      }

      h1 {
        color: #1a73e8;
        text-align: center;
        margin: 24px 0;
        font-size: 2.5rem;
        font-weight: 700;
        letter-spacing: -0.5px;
        padding: 16px 32px;
        background: #fff;
        border-radius: 12px;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1),
          0 2px 4px -1px rgba(0, 0, 0, 0.06);
      }

      .container {
        flex: 1;
        display: flex;
        flex-direction: column;
        width: 100%;
        max-width: 1200px;
        padding: 0 24px;
        position: relative;
      }

      .chat-container {
        flex: 1;
        overflow-y: auto;
        display: flex;
        flex-direction: column;
        background-color: #fff;
        padding: 24px;
        border-radius: 16px;
        margin-bottom: 140px;
        box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1),
          0 4px 6px -2px rgba(0, 0, 0, 0.05);
        height: calc(100vh - 150px);
      }

      .message {
        max-width: 75%;
        padding: 16px 20px;
        margin: 12px 0;
        border-radius: 12px;
        word-wrap: break-word;
        line-height: 1.6;
        font-size: 15px;
      }

      .user-message {
        align-self: flex-end;
        background-color: #1a73e8;
        color: #fff;
        border-bottom-right-radius: 4px;
      }

      .bot-message {
        align-self: flex-start;
        background-color: #f8f9fa;
        color: #212529;
        border-bottom-left-radius: 4px;
        border: 1px solid #e9ecef;
        position: relative;
      }
      .elapsed-time {
        position: absolute;
        right: 10px;
        bottom: 4px;
        font-size: 11px;
        color: #888;
        font-style: italic;
        background: rgba(255, 255, 255, 0.7);
        padding: 1px 6px;
        border-radius: 8px;
        pointer-events: none;
      }

      .typing-indicator {
        align-self: flex-start;
        background-color: #f8f9fa;
        border: 1px solid #e9ecef;
        padding: 16px 20px;
        margin: 12px 0;
        font-style: italic;
        color: #6c757d;
        border-radius: 12px;
        border-bottom-left-radius: 4px;
      }

      .input-area {
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        display: flex;
        align-items: center;
        gap: 12px;
        padding: 16px;
        background-color: #fff;
        border-top: 1px solid #e9ecef;
        z-index: 1000;
        box-shadow: 0 -4px 6px -1px rgba(0, 0, 0, 0.1);
      }

      .input-area form {
        display: flex;
        width: 100%;
        max-width: 1200px;
        margin: 0 auto;
        padding: 0 24px;
        gap: 12px;
        align-items: center;
      }

      input[type="text"] {
        flex: 1;
        padding: 16px;
        border-radius: 12px;
        border: 1px solid #e9ecef;
        font-size: 15px;
        font-family: inherit;
        background-color: #fff;
        transition: all 0.2s ease;
      }

      textarea {
        flex: 1;
        padding: 16px;
        border-radius: 12px;
        border: 1px solid #e9ecef;
        font-size: 15px;
        font-family: inherit;
        background-color: #fff;
        transition: all 0.2s ease;
        resize: none;
        min-height: 52px;
        max-height: 200px;
        line-height: 1.6;
      }

      textarea:focus {
        outline: none;
        border-color: #1a73e8;
        box-shadow: 0 0 0 3px rgba(26, 115, 232, 0.1);
      }

      textarea::placeholder {
        color: #adb5bd;
      }

      .options-container {
        display: flex;
        align-items: center;
        gap: 16px;
        color: #495057;
        font-size: 14px;
      }

      .option-item {
        display: flex;
        align-items: center;
        gap: 8px;
      }

      input[type="checkbox"] {
        width: 16px;
        height: 16px;
        accent-color: #1a73e8;
      }

      button[type="submit"] {
        padding: 16px;
        cursor: pointer;
        background-color: #1a73e8;
        color: white;
        border: none;
        border-radius: 12px;
        font-size: 18px;
        width: 52px;
        height: 52px;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.2s ease;
      }

      button[type="submit"]:hover {
        background-color: #1557b0;
        transform: translateY(-1px);
      }

      .chat-container::-webkit-scrollbar {
        width: 8px;
      }

      .chat-container::-webkit-scrollbar-thumb {
        background-color: #ced4da;
        border-radius: 4px;
      }

      .chat-container::-webkit-scrollbar-track {
        background-color: #f8f9fa;
      }

      @media (max-width: 1240px) {
        .container {
          max-width: 100%;
          padding: 0 16px;
        }

        h1 {
          font-size: 2rem;
          padding: 12px 24px;
        }

        .message {
          max-width: 85%;
        }

        .input-area form {
          padding: 0 16px;
        }

        .options-container {
          flex-direction: column;
          align-items: flex-start;
          gap: 8px;
        }
      }
    </style>
  </head>
  <body>
    <h1>FlowRAG Library</h1>
    <div class="container">
      <div class="chat-container" id="chat-container">
        {% if results %} {% for interaction in results.responses %}
        <div class="message user-message">
          <p>{{ interaction.query | markdown | safe }}</p>
        </div>
        <div class="message bot-message">
          <p>{{ interaction.response | markdown | safe }}</p>
        </div>
        {% endfor %} {% else %}
        <div class="message bot-message">
          <p>
            Welcome to FlowRAG Library! I can help you understand and explore
            your codebase. Ask me any questions about your code.
          </p>
        </div>
        {% endif %}
      </div>
      <div class="input-area">
        <form id="chat-form">
          <textarea
            id="query"
            name="query"
            placeholder="Type your message..."
            required
          ></textarea>
          <div class="options-container">
            <div
              class="option-item"
              title="Disable it if you don't want do search on the codebase. Only the current conversation context will be used."
            >
              <input
                type="checkbox"
                id="codebase"
                name="codebase"
                value="yes"
                checked
              />
              <label for="codebase">Use Codebase</label>
            </div>
          </div>
          <button type="submit" name="action" value="Chat">&#10148;</button>
        </form>
      </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
    <script>
      document.addEventListener("DOMContentLoaded", function () {
        const chatContainer = document.getElementById("chat-container");
        chatContainer.scrollTop = chatContainer.scrollHeight;

        // Parse markdown in bot messages
        document.querySelectorAll(".bot-message p").forEach((el) => {
          el.innerHTML = marked.parse(el.textContent);
        });
      });

      document
        .getElementById("chat-form")
        .addEventListener("submit", function (event) {
          event.preventDefault();
          const queryInput = document.getElementById("query");
          const query = queryInput.value;
          if (query.trim() === "") {
            return;
          }

          const codebaseCheckbox = document.getElementById("codebase");
          const codebase = codebaseCheckbox.checked;

          const chatContainer = document.getElementById("chat-container");
          const userMessageDiv = document.createElement("div");
          userMessageDiv.classList.add("message", "user-message");
          userMessageDiv.innerHTML =
            "<p>" + query.replace(/\n/g, "<br>") + "</p>";
          chatContainer.appendChild(userMessageDiv);

          chatContainer.scrollTop = chatContainer.scrollHeight;
          queryInput.value = "";

          const typingIndicator = document.createElement("div");
          typingIndicator.classList.add("typing-indicator");
          typingIndicator.innerText = "Thinking...";
          chatContainer.appendChild(typingIndicator);
          chatContainer.scrollTop = chatContainer.scrollHeight;

          // Start timer
          const startTime = new Date();
          const timerInterval = setInterval(() => {
            const elapsedTime = new Date() - startTime;
            const seconds = Math.floor(elapsedTime / 1000);
            const minutes = Math.floor(seconds / 60);
            const formattedTime =
              (minutes < 10 ? "0" + minutes : minutes) +
              ":" +
              (seconds % 60 < 10 ? "0" + (seconds % 60) : seconds % 60);
            typingIndicator.innerText = `Thinking... (${formattedTime})`;
          }, 1000);

          fetch("/", {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
              "X-Requested-With": "XMLHttpRequest",
            },
            body: JSON.stringify({
              query: query,
              codebase: codebase,
            }),
          })
            .then((response) => response.json())
            .then((data) => {
              clearInterval(timerInterval);
              chatContainer.removeChild(typingIndicator);

              const botMessageDiv = document.createElement("div");
              botMessageDiv.classList.add("message", "bot-message");
              botMessageDiv.innerHTML =
                "<p>" + marked.parse(data.response) + "</p>";

              // Add elapsed time to bottom of message
              const elapsedTime = new Date() - startTime;
              const seconds = Math.floor(elapsedTime / 1000);
              const minutes = Math.floor(seconds / 60);
              const formattedTime =
                (minutes < 10 ? "0" + minutes : minutes) +
                ":" +
                (seconds % 60 < 10 ? "0" + (seconds % 60) : seconds % 60);
              const timerDiv = document.createElement("div");
              timerDiv.classList.add("elapsed-time");
              timerDiv.innerText = formattedTime;
              botMessageDiv.appendChild(timerDiv);

              chatContainer.appendChild(botMessageDiv);

              chatContainer.scrollTop = chatContainer.scrollHeight;
            })
            .catch((error) => {
              clearInterval(timerInterval);
              console.error("Error:", error);
              chatContainer.removeChild(typingIndicator);

              const errorMessageDiv = document.createElement("div");
              errorMessageDiv.classList.add("message", "bot-message");
              errorMessageDiv.innerHTML =
                "<p>Sorry, there was an error processing your request. Please try again.</p>";
              chatContainer.appendChild(errorMessageDiv);

              chatContainer.scrollTop = chatContainer.scrollHeight;
            });
        });

      // Enable Shift + Enter for line breaks
      document
        .getElementById("query")
        .addEventListener("keydown", function (event) {
          if (event.key === "Enter") {
            if (event.shiftKey) {
              // Shift + Enter: add line break
              event.preventDefault();
              const start = this.selectionStart;
              const end = this.selectionEnd;
              this.value =
                this.value.substring(0, start) +
                "\n" +
                this.value.substring(end);
              this.selectionStart = this.selectionEnd = start + 1;
            } else {
              // Enter: submit form
              event.preventDefault();
              document
                .getElementById("chat-form")
                .dispatchEvent(new Event("submit"));
            }
          }
        });
    </script>
  </body>
</html>
