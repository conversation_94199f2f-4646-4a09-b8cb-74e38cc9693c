#!/usr/bin/env python3
"""
Web application for FlowRAG library.

Provides a web interface for interacting with the FlowRAG functionality,
including querying, context generation, and chat history management.
"""
import os
import sys
from flow_rag.infrastructure.log_utils import setup_logger, log_message
import uuid
from flask import Flask, render_template, request, session, jsonify
import time
import traceback
import markdown

from ...adapters.embedding_providers import EmbeddingProviderFactory
from ...adapters.vector_stores import VectorStoreFactory
from ...adapters.llm_providers import FlowLLMClientProvider
from ...application.query_processing import QueryProcessingService
from ..db import DatabaseAdapter
from ..config import get_config

class WebApp:
    
    def __init__(self, codebase_path: str, config: dict = None):
        """
        Initialize the web application.

        Args:
            codebase_path: Path to the indexed codebase
            config: Optional configuration dictionary
        """
        self.codebase_path = os.path.normpath(os.path.abspath(codebase_path))
        self.codebase_name = os.path.basename(self.codebase_path)

        # Initialize global configuration once
        self.config_manager = get_config()

        # Get configurations once and store them
        self.web_config = self.config_manager.get_web_config()
        self.vector_store_config = self.config_manager.get_vector_store_config()
        self.logs_config = self.config_manager.get_logging_config()

        # Default configuration
        self.config = {
            'SECRET_KEY': os.urandom(24),
            'DEBUG': self.web_config.get('debug'),
            'PORT': self.web_config.get('port'),
            'HOST': self.web_config.get('host'),
            'CACHE_ENABLED': self.web_config.get('cache_enabled')
        }

        # Override with provided config
        if config:
            self.config.update(config)

        # Set up logging
        self.logger = self._setup_logging()

        # Initialize Flask app
        self.app = Flask(
            __name__,
            template_folder=os.path.join(os.path.dirname(__file__), 'templates'),
            static_folder=os.path.join(os.path.dirname(__file__), 'static')
        )
        self.app.secret_key = self.config['SECRET_KEY']

        # Initialize database with a thread-safe adapter
        temp_data_dir = self.vector_store_config.get('base_path')
        cache_dir = os.path.join(temp_data_dir, "temp_cache")
        os.makedirs(cache_dir, exist_ok=True)
        self.db_adapter = DatabaseAdapter(cache_dir=cache_dir, logger=self.logger)
        
        # Set up the components
        self._setup_components()
        
        # Set up routes
        self._setup_routes()

        # Register template filters
        self._setup_template_filters()
        
    def _setup_logging(self):
        logs_dir = self.logs_config.get('base_path')
        os.makedirs(logs_dir, exist_ok=True)
        return setup_logger(name="flowrag_web", log_file=os.path.join(logs_dir, 'flowrag_web.log'))

    def _setup_components(self) -> None:
        """Set up the components needed for RAG."""
        log_message(f"Setting up components for codebase: {self.codebase_name}", logger=self.logger)

        # Create embedding provider using factory
        embedding_provider = EmbeddingProviderFactory.create_provider(logger=self.logger)

        # Set up LLM provider using factory
        self.llm_provider = FlowLLMClientProvider(logger=self.logger)

        # Set up vector store and connect to database
        self.vector_store = VectorStoreFactory.create_store(logger=self.logger)
        db_uri = os.path.join(self.vector_store_config.get('base_path'), self.codebase_name)
        self.vector_store.connect(db_uri)
        
        # Create query service
        self.query_service = QueryProcessingService(
            embeddings_provider=embedding_provider,
            vector_store=self.vector_store,
            llm_provider=self.llm_provider,
            logger=self.logger
        )
        
        log_message("Server running, access the chat interface to interact with the codebase.", level="success", logger=self.logger, to_terminal=True)
        log_message("http://localhost:5001", logger=self.logger, to_terminal=True)
        log_message("Press Ctrl+C to stop the server", logger=self.logger, to_terminal=True)

    def _get_or_create_user_id(self):
        """Get user ID from session or create a new one."""
        user_id = session.get('user_id')
        if not user_id:
            user_id = str(uuid.uuid4())
            session['user_id'] = user_id
        return user_id

    def _setup_routes(self) -> None:
        """Set up the routes for the web application."""
        @self.app.route('/', methods=['GET', 'POST'])
        def home():
            # Handle AJAX POST request
            if request.method == 'POST' and request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                return self._handle_ajax_query()

            # Handle GET request
            user_id = self._get_or_create_user_id()

            # Get chat history
            chat_history = self.db_adapter.get_chat_history(user_id)
            results = {'responses': chat_history} if chat_history else None

            # Close DB connection for this request
            self.db_adapter.close_connection()

            return render_template('chat.html', results=results)
        
        @self.app.teardown_appcontext
        def close_db(e=None):
            self.db_adapter.close_connection()
        
        @self.app.teardown_request
        def close_db_after_request(e=None):
            self.db_adapter.close_connection()

    def _setup_template_filters(self) -> None:
        """Set up template filters for the web application."""
        @self.app.template_filter('markdown')
        def markdown_filter(text):
            return markdown.markdown(text, extensions=['fenced_code', 'tables'])
    
    def _handle_ajax_query(self):
        """Handle AJAX query requests."""
        data = request.get_json()
        query = data.get('query', '')
        use_codebase = data.get('codebase', False)

        # Get user ID from session or create new one
        user_id = self._get_or_create_user_id()

        start_time = time.time()
        
        try:
            # Process query
            if use_codebase in [True, 'true', 'True', '1']:
                # Process the query to get context from the codebase
                log_message("\n\n************************* NEW QUERY SESSION ***************************\n", logger=self.logger)
                log_message(f"Processing query with codebase: \"{query[:50]}...", logger=self.logger, to_terminal=True)
                
                # Get context from the codebase using the query service
                result = self.query_service.process_query(query)
                context = result.context
                
                # Store context for future use if caching is enabled
                if self.config['CACHE_ENABLED']:
                    self.db_adapter.store_context(user_id, context)
                
                # Generate response with the obtained context
                response = self.llm_provider.answer_with_context(query, context)
            else:
                # Use existing context from cache
                log_message("\n\n************************* NEW QUERY SESSION ***************************\n", logger=self.logger)
                log_message(f"Processing query with existing context: {query[:50]}...", logger=self.logger, to_terminal=True)
                context = self.db_adapter.get_context(user_id) or ""
                response = self.llm_provider.answer_with_context(query, context)
            
            # Store in chat history
            self.db_adapter.add_chat_history(user_id, query, response)
            
            process_time = time.time() - start_time
            log_message(f"Query processed in {process_time:.2f} seconds", logger=self.logger, to_terminal=True)
            
            return jsonify({'response': response})
        except Exception as e:
            log_message(f"Error processing query: {str(e)}", level="error", logger=self.logger, to_terminal=True)
            log_message(traceback.format_exc(), logger=self.logger, level="error")
            return jsonify({
                'response': f"I encountered an error while processing your request. Please try again.\n\nError: {str(e)}"
            }), 500
        finally:
            # Ensure connection is always closed
            self.db_adapter.close_connection()

    
    def run(self) -> None:
        """Run the web application."""
        # Suppress Flask CLI and Werkzeug startup messages
        import logging
        import warnings
        import click
        
        # Suppress Werkzeug 'WARNING: This is a development server...' message
        logging.getLogger('werkzeug').setLevel(logging.ERROR)
        # Suppress Flask's click echo for 'Serving Flask app'
        click.echo = lambda *args, **kwargs: None
        # Suppress Flask's warning for development server
        warnings.filterwarnings('ignore', category=UserWarning, message='.*development server.*')

        log_message(f"Starting web server at {self.config['HOST']}:{self.config['PORT']}", logger=self.logger)
        self.app.run(
            host=self.config['HOST'],
            port=self.config['PORT'],
            debug=self.config['DEBUG']
        )


def create_app(codebase_path: str, config: dict = None) -> Flask:
    """
    Create and configure a Flask application instance.
    
    Args:
        codebase_path: Path to the indexed codebase
        config: Optional configuration dictionary
        
    Returns:
        Configured Flask application
    """
    web_app = WebApp(codebase_path, config)
    return web_app.app


def main(args=None):
    """Main entry point for running the web application."""
    if args is None:
        if len(sys.argv) != 2:
            args = parse_args()
            codebase_path = args.codebase_path
        else:
            codebase_path = sys.argv[1]
    else:
        codebase_path = args
    
    web_app = WebApp(codebase_path)
    web_app.run()


def parse_args():
    """Parse command line arguments."""
    import argparse
    parser = argparse.ArgumentParser(description="Start the FlowRAG web interface")
    parser.add_argument("codebase_path", help="Path to the indexed codebase")
    return parser.parse_args()

if __name__ == "__main__":
    main()