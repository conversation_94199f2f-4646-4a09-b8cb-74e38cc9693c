#!/usr/bin/env python3
"""
Entrypoint for querying code using the FlowRAG library.

Used only when running by command line.

Steps:
1. Connect to an indexed codebase
2. Process natural language queries
3. Retrieve relevant code and generate answers
"""

import sys
from flow_rag.infrastructure.log_utils import setup_logger, log_message
import argparse
from pathlib import Path
import traceback

from flow_rag.adapters.llm_providers.factory import FlowLLMClientProvider
from flow_rag.adapters.embedding_providers import EmbeddingProviderFactory
from flow_rag.adapters.vector_stores import VectorStoreFactory
from flow_rag.application.query_processing import QueryProcessingService
from flow_rag.infrastructure.config import get_config


# Add project root to path to enable imports
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

def main():
    """Main function to query an indexed codebase."""
    # Parse command-line arguments
    parser = argparse.ArgumentParser(description="Query an indexed codebase")
    parser.add_argument("codebase_path", help="Path to the indexed codebase")
    parser.add_argument("query", help="Natural language query about the codebase")
    parser.add_argument("--rerank", action="store_true", help="Enable result reranking")
    args = parser.parse_args()

    # Extract codebase name from path
    import os
    codebase_name = os.path.basename(os.path.normpath(args.codebase_path))

    # Get configuration instance once
    config = get_config()

    # Setup logging using config
    logs_config = config.get_logging_config()
    logs_dir = logs_config.get('base_path')
    os.makedirs(logs_dir, exist_ok=True)
    logger = setup_logger(name="query_code", log_file=os.path.join(logs_dir, "flowrag_query.log"))

    log_message("\n\n*************************** NEW QUERY SESSION ******************************\n", logger=logger)
    log_message(f"Processing query for codebase: {args.codebase_path}", logger=logger)
    log_message(f"Query: {args.query}", logger=logger)

    try:
        # Create service components using factories consistently
        log_message("Creating embedding provider...", logger=logger)
        embedding_provider = EmbeddingProviderFactory.create_provider(logger=logger)

        log_message("Creating LLM provider...", logger=logger)
        llm_provider = FlowLLMClientProvider(logger=logger)

        log_message("Creating vector store...", logger=logger)
        vector_store_config = config.get_vector_store_config()
        vector_store = VectorStoreFactory.create_store(logger=logger)
        db_uri = os.path.join(vector_store_config.get('base_path'), codebase_name)
        vector_store.connect(db_uri)
        
        # Create query processing service
        log_message("Creating query processing service...", logger=logger)
        query_service = QueryProcessingService(
            embeddings_provider=embedding_provider,
            vector_store=vector_store,
            llm_provider=llm_provider,
            logger=logger
        )
        
        # Process query
        log_message("Processing query...", logger=logger, to_terminal=True)
        result = query_service.process_query(args.query)
        
        # Generate answer
        log_message("Generating answer...", logger=logger, to_terminal=True)
        answer = llm_provider.answer_with_context(
            query=args.query,
            context=result.context
        )
        
        # Print results
        print("\n" + "="*50)
        print(f"QUERY: {args.query}")
        print("="*50)
        print(answer)
        print("="*50)
        
        # Print metadata
        print(f"\nProcessing time: {result.metadata.get('processing_time', 0):.2f} seconds")
        
    except Exception as e:
        log_message(f"Error during query processing: {str(e)}", level="error", logger=logger, to_terminal=True)
        log_message(traceback.format_exc(), logger=logger, level="error")
        sys.exit(1)


if __name__ == "__main__":
    main()