#!/usr/bin/env python3
"""
Entrypoint for indexing code using the FlowRAG library.

Steps:
1. Parse and analyze a codebase
2. Find references between code entities
3. Index special files (README, configs, etc.)
4. Store the indexed data in a SQLite database
"""

import os
import sys
import argparse
from pathlib import Path

from flow_rag.infrastructure.log_utils import setup_logger, log_message
from flow_rag.adapters.code_parsers.factory import CodeParserFactory
from flow_rag.adapters.file_loader import FileLoader
from flow_rag.adapters.embedding_providers import EmbeddingProviderFactory
from flow_rag.adapters.vector_stores import VectorStoreFactory
from flow_rag.application.code_indexing import CodeIndexingService
from flow_rag.infrastructure.config import get_config

import warnings
import traceback
warnings.filterwarnings('ignore', category=FutureWarning)

# Add project root to path to enable imports
# From flow_rag/infrastructure/web/entrypoints/ go up 4 levels to project root
project_root = Path(__file__).parent.parent.parent.parent.parent
sys.path.insert(0, str(project_root))


def main():
    """Main function to index a codebase."""
    # Parse command-line arguments
    parser = argparse.ArgumentParser(description="Index a codebase for code search and QA")
    parser.add_argument("codebase_path", help="Path to the codebase to index")
    args = parser.parse_args()

    # Get configuration instance once
    config = get_config()

    # Setup logging using config
    logs_config = config.get_logging_config()
    logs_dir = logs_config.get('base_path')
    os.makedirs(logs_dir, exist_ok=True)
    logger = setup_logger(name="code_indexing", log_file=os.path.join(logs_dir, "flowrag_indexing.log"))

    log_message("\n\n*************************** NEW INDEXING SESSION ******************************\n", logger=logger)
    log_message(f"Starting indexing of codebase: \"{args.codebase_path}\"", logger=logger, to_terminal=True)
    
    # Check if the path exists
    if not os.path.exists(args.codebase_path):
        log_message(f"Codebase path does not exist: \"{args.codebase_path}\"", level="error", logger=logger, to_terminal=True)
        sys.exit(1)
        
    try:
        # Detect language of the codebase
        log_message("Calling FileLoader...", logger=logger)
        file_loader = FileLoader(logger=logger)
        files = file_loader.load_files(args.codebase_path)
        log_message(f"Found {len(files)} valid files in codebase", logger=logger, to_terminal=True)
        
        if not files:
            log_message("No supported files found in the codebase. Indexing aborted.", 
                      level="error", logger=logger, to_terminal=True)
            sys.exit(1)
            
        # Get the language of majority of files
        languages = [f[1] for f in files]
        language = max(set(languages), key=languages.count)
        log_message(f"Main language detected: '{language.value}'", logger=logger, to_terminal=True)
        
        log_message("Creating parser...", logger=logger)
        try:
            code_parser = CodeParserFactory.create_parser(language, logger=logger)
        except ValueError as e:
            log_message(f"Error creating parser: {str(e)}", level="error", logger=logger, to_terminal=True)
            sys.exit(1)
        
        log_message("Creating embedding provider...", logger=logger)
        embedding_provider = EmbeddingProviderFactory.create_provider(logger=logger)
        
        log_message("Creating vector store...", logger=logger)
        vector_store = VectorStoreFactory.create_store(logger=logger)
        
        log_message("Calling CodeIndexingService...", logger=logger, to_terminal=True)
        indexing_service = CodeIndexingService(
            code_parser=code_parser,
            embeddings_provider=embedding_provider,
            vector_store=vector_store,
            logger=logger
        )
        metadata = indexing_service.index_codebase(args.codebase_path)
        
        # Print indexing summary
        log_message("Indexing finished:", logger=logger, level="success", to_terminal=True)
        log_message(f" Files processed: {metadata.file_count}", logger=logger, to_terminal=True)
        log_message(f" Symbols indexed: {metadata.method_count}", logger=logger, to_terminal=True)
        log_message(f" Languages detected: {', '.join(lang.value for lang in metadata.languages)}", logger=logger, to_terminal=True)
        
    except Exception as e:
        log_message(f"Error during indexing: {str(e)}", level="error", logger=logger, to_terminal=True)
        log_message(traceback.format_exc(), logger=logger, level="error")
        sys.exit(1)


if __name__ == "__main__":
    main()