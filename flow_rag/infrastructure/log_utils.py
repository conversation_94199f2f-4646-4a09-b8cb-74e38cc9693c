import logging
import os
from datetime import datetime
from typing import Optional
from flow_rag.infrastructure.config import get_config, PROJECT_ROOT

def setup_logger(name: str = "flow_rag", log_file: Optional[str] = None, level: int = logging.INFO) -> logging.Logger:
    """
    Configures and returns a standardized logger for the project.
    """
    logger = logging.getLogger(name)
    logger.setLevel(level)
    formatter = logging.Formatter('[%(asctime)s] %(levelname)s - %(message)s')

    config_manager = get_config()
    logs_dir = config_manager.get_logging_config().get('base_path')
    
    # Ensure logs_dir is not None before using it
    if logs_dir is None:
        logs_dir = str(PROJECT_ROOT / "rag_logs")
    
    os.makedirs(logs_dir, exist_ok=True)

    # Avoid multiple handlers
    if not logger.handlers:
        if log_file:
            file_handler = logging.FileHandler(os.path.join(logs_dir, log_file))
            file_handler.setFormatter(formatter)
            logger.addHandler(file_handler)
        else:
            stream_handler = logging.StreamHandler()
            stream_handler.setFormatter(formatter)
            logger.addHandler(stream_handler)
    return logger


def log_message(msg: str, logger: Optional[logging.Logger] = None, level: str = "info", to_terminal: bool = False) -> None:
    """
    Logs a standardized message and, optionally, prints it to the terminal.
    Args:
        msg (str): Message to be logged.
        level (str): Log level ('info', 'warning', 'error', 'debug').
        to_terminal (bool): If True, prints to the terminal.
    """
    if logger:
        if level == "warning":
            logger.warning(msg)
        elif level == "error":
            logger.error(msg)
        elif level == "success":
            logger.info(f"[SUCCESS] {msg}")
        else:
            logger.info(msg)

    if to_terminal:
        now = datetime.now().strftime('%H:%M:%S')
        # ANSI color codes
        COLORS = {
            'info': '',
            'warning': '\033[93m',  # yellow
            'error': '\033[91m',    # red
            'debug': '\033[94m',    # blue
            'success': '\033[92m',  # green
        }
        RESET = '\033[0m'
        color = COLORS.get(level, '')
        # Only show status if not INFO
        status = f" [{level.upper()}]" if level != 'info' else ''
        # Compose message
        term_msg = f"* {now}{status} {msg}"
        if color:
            term_msg = f"{color}{term_msg}{RESET}"
        print(term_msg)