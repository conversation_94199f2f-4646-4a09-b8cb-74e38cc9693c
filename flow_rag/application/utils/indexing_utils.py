"""
Utility functions for code indexing operations.

This module contains helper functions that support the indexing process
but are not directly tied to the core indexing logic.
"""

import os
from typing import Optional, Callable
import logging
from flow_rag.infrastructure.log_utils import log_message


def get_db_uri(codebase_name: str, config_manager=None) -> str:
    """
    Generate the database URI for a given codebase.

    Args:
        codebase_name: Name of the codebase
        config_manager: Optional config manager instance. If None, uses simple path.

    Returns:
        String path to the database directory
    """
    if config_manager is None:
        # Simple fallback for basic usage
        return os.path.join("rag_data", codebase_name)

    # Full logic with config manager
    store_path = config_manager.get_vector_store_config().get('base_path')
    processed_data_dir = os.path.join(os.getcwd(), store_path)
    os.makedirs(processed_data_dir, exist_ok=True)

    # Create a subdirectory for this codebase if it does not exist.
    codebase_dir = os.path.join(processed_data_dir, codebase_name)
    os.makedirs(codebase_dir, exist_ok=True)

    return os.path.join(processed_data_dir, codebase_name)


def update_progress(
    progress_callback: Optional[Callable[[int], None]], 
    percentage: int, 
    last_progress: int,
    logger: Optional[logging.Logger] = None
) -> int:
    """
    Update progress if callback is provided and percentage is greater than last progress.
    
    Args:
        progress_callback: Optional callback function to call with progress percentage
        percentage: Current progress percentage (0-100)
        last_progress: Last progress percentage that was reported
        logger: Optional logger for error reporting
        
    Returns:
        The new last_progress value (either updated or unchanged)
    """
    if progress_callback and percentage > last_progress:
        try:
            progress_callback(percentage)
            return percentage
        except Exception as e:
            log_message(f"[IndexingUtils] Error in progress callback: {e}", 
                       level="warning", logger=logger)
    return last_progress


def calculate_progress(current_step: str, current_item: int = 0, total_items: int = 0) -> int:
    """
    Calculate progress percentage based on current step and item progress.
    
    Args:
        current_step: Current step in the indexing process
        current_item: Current item being processed (for steps with multiple items)
        total_items: Total items to process (for steps with multiple items)
        
    Returns:
        Progress percentage (0-100)
    """
    # Define step weights and base percentages
    step_weights = {
        'start': 0,
        'files_loaded': 5,
        'code_parsed': 10,
        'references_found': 15,
        'special_files_processed': 20,
        'methods_embedding': (20, 50),  # (start, end) range
        'classes_embedding': (50, 70),  # (start, end) range
        'special_embedding': (70, 80),  # (start, end) range
        'parquet_saved': 85,
        'methods_stored': 90,
        'classes_stored': 95,
        'special_stored': 98,
        'complete': 100
    }
    
    if current_step in ['methods_embedding', 'classes_embedding', 'special_embedding']:
        start_pct, end_pct = step_weights[current_step]
        if total_items > 0:
            item_progress = min(current_item / total_items, 1.0)
            percentage = int(start_pct + (end_pct - start_pct) * item_progress)
        else:
            percentage = start_pct
    else:
        percentage = step_weights.get(current_step, 0)
    
    return min(max(percentage, 0), 100)
