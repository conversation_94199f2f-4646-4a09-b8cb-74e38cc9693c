"""
Utility functions for query processing operations.

This module contains helper functions that support the query processing
but are not directly tied to the core query processing logic.
"""

import tiktoken
from typing import Optional
import logging
from flow_rag.infrastructure.log_utils import log_message


def count_tokens(
    text: str,
    encoding_name: str = "cl100k_base"
) -> int:
    """
    Count the number of tokens in a text string.
    
    Args:
        text: The text string to count tokens for
        encoding_name: Tokenizer encoding to use (default: cl100k_base)
        
    Returns:
        Number of tokens in the text
    """
    tokenizer = tiktoken.get_encoding(encoding_name)
    return len(tokenizer.encode(text))


def truncate_context_by_tokens(
    context: str, 
    max_tokens: int, 
    encoding_name: str = "cl100k_base",
    logger: Optional[logging.Logger] = None
) -> str:
    """
    Truncate context to fit within max_tokens limit.
    
    Args:
        context: The context string to truncate
        max_tokens: Maximum number of tokens allowed
        encoding_name: Tokenizer encoding to use (default: cl100k_base)
        logger: Optional logger for logging truncation info
        
    Returns:
        Truncated context string
    """
    tokenizer = tiktoken.get_encoding(encoding_name)
    tokens = tokenizer.encode(context)
    
    if len(tokens) <= max_tokens:
        return context

    log_message(f"Context truncated from {len(tokens)} to {max_tokens} tokens", logger=logger)
    return tokenizer.decode(tokens[:max_tokens])
