from typing import List, Dict, Any, Optional, Tuple
import logging
from flow_rag.infrastructure.log_utils import log_message
import time
from concurrent.futures import ThreadPoolExecutor
import traceback

from ..domain.models import QueryResult
from ..domain.ports.embeddings import EmbeddingsPort
from ..domain.ports.vector_store import VectorStorePort
from ..domain.ports.llm import LLMPort
from flow_rag.application.utils import truncate_context_by_tokens


class QueryProcessingService:
    """
    Application service for processing natural language queries.
    
    This service is responsible for:
    1. Processing queries using HyDE (Hypothetical Document Embeddings)
    2. Refining queries with initial search results
    3. Reranking results for better relevance
    4. Generating comprehensive context for answering
    """
    
    def __init__(
        self,
        embeddings_provider: EmbeddingsPort,
        vector_store: VectorStorePort,
        llm_provider: LLMPort,
        logger: Optional[logging.Logger] = None
    ) -> None:
        self.embeddings_provider = embeddings_provider
        self.vector_store = vector_store
        self.llm_provider = llm_provider
        self.logger = logger

        # Get configuration
        from ..infrastructure.config import get_config
        self.config = get_config()
        query_config = self.config.get_query_processing_config()

        # Configuration from config file
        self.hyde_enabled = query_config.get('hyde_enabled')
        self.reranking_enabled = query_config.get('reranking_enabled')
        self.hybrid_search_enabled = query_config.get('hybrid_search_enabled')
        self.hybrid_alpha = query_config.get('hybrid_alpha')
        self.max_results = query_config.get('max_results')
        self.reranking_results = query_config.get('reranking_results')

        # Table names
        self.method_table = "_method"
        self.class_table = "_class"
        self.special_table = "_special"


    def process_query(self, query: str) -> QueryResult:
        """
        Process a query with optional HyDE enhancement based on configuration.
        Simplified version that focuses on direct search and context generation.

        Args:
            query: The natural language query
        Returns:
            QueryResult with processed results and context
        """
        start_time = time.time()
        log_message(f"Processing query: {query}", logger=self.logger)

        # Determine search query based on configuration
        if self.hyde_enabled:
            log_message("Using HyDE for query enhancement (enabled in config)", logger=self.logger)
            search_query = self._generate_initial_hyde(query)
        else:
            log_message("Using direct query (HyDE disabled in config)", logger=self.logger)
            search_query = query

        # Perform search
        final_methods, final_classes, final_special = self._perform_search(search_query)

        # Apply reranking if enabled
        if self.reranking_enabled:
            log_message("Reranking enabled: True. Running rerank in parallel for methods, classes, and special files.", logger=self.logger)
            rerank_start_time = time.time()
            def rerank_methods():
                return self.vector_store.rerank(self.method_table, final_methods, search_query)
            def rerank_classes():
                return self.vector_store.rerank(self.class_table, final_classes, search_query)
            def rerank_special():
                return self.vector_store.rerank(self.special_table, final_special, search_query)
            with ThreadPoolExecutor(max_workers=3) as executor:
                future_methods = executor.submit(rerank_methods)
                future_classes = executor.submit(rerank_classes)
                future_special = executor.submit(rerank_special) if final_special else None
                final_methods = future_methods.result()
                final_classes = future_classes.result()
                final_special = future_special.result() if future_special else []
            rerank_time = time.time()
            log_message(f"Reranking took: {rerank_time - rerank_start_time:.2f} seconds", logger=self.logger)

        # Limit results after reranking
        final_methods = final_methods[:self.reranking_results]
        final_classes = final_classes[:self.reranking_results]
        final_special = final_special[:self.reranking_results]

        # Create final context
        context = self._generate_final_context(final_methods, final_classes, final_special)

        # Time tracking
        processing_time = time.time() - start_time
        log_message(f"Query processing completed in {processing_time:.2f} seconds", logger=self.logger)

        return QueryResult(
            query=query,
            methods=final_methods,  # type: ignore
            classes=final_classes,  # type: ignore
            special_files=final_special,  # type: ignore
            context=context,
            metadata={
                "processing_time": processing_time,
                "search_query": search_query,
                "hyde_enabled": self.hyde_enabled,
                "reranking_enabled": self.reranking_enabled,
                "hybrid_search_enabled": self.hybrid_search_enabled,
                "hybrid_alpha": self.hybrid_alpha
            }
        )
    
    def _generate_initial_hyde(self, query: str) -> str:
        """
        Generate initial hypothetical document for the query using HyDE prompt.
        Args:
            query: The natural language query
        Returns:
            Initial HyDE string (clipped to token limit)
        """
        log_message("Generating initial HyDE query", logger=self.logger)
        hyde_query = self.llm_provider.enhance_query(query)

        # Clip HyDE result to prevent token limit issues
        clipped_hyde = self.embeddings_provider.clip_text_to_max_tokens(hyde_query)

        if len(hyde_query) != len(clipped_hyde):
            log_message(f"[HyDE] Clipped HyDE query from {len(hyde_query)} to {len(clipped_hyde)} characters",
                       level="warning", logger=self.logger)

        return clipped_hyde
    
    def _perform_search(self, search_query: str) -> Tuple[List[Dict[str, Any]], List[Dict[str, Any]], List[Dict[str, Any]]]:
        """
        Perform search using the given query.

        Args:
            search_query: Query to search for (can be original or HyDE-enhanced)

        Returns:
            Tuple of (method_results, class_results, special_results)
        """
        log_message(f"[Search] Starting search for query: {search_query[:100]}...", logger=self.logger)

        # Generate query vector
        log_message("[Search] Generating query vector...", logger=self.logger)
        query_vector = self.embeddings_provider.embed_query(search_query)

        # Log embedding details
        embedding_dim = len(query_vector) if query_vector else None
        provider_name = type(self.embeddings_provider).__name__
        model_name = getattr(self.embeddings_provider, 'model_name', getattr(self.embeddings_provider, '_model_name', 'unknown'))
        log_message(f"[Search] Embedding Provider: {provider_name}, Model: {model_name}, Dimension: {embedding_dim}", logger=self.logger)

        # Check if vector store is connected
        if not hasattr(self.vector_store, 'db') or self.vector_store.db is None:
            log_message("[Search] ERROR: Vector store is not connected to a database", level="error", logger=self.logger)
            return [], [], []

        # Check if tables exist
        method_table_exists = self.vector_store.table_exists(self.method_table) if hasattr(self.vector_store, 'table_exists') else False
        class_table_exists = self.vector_store.table_exists(self.class_table) if hasattr(self.vector_store, 'table_exists') else False
        special_table_exists = self.vector_store.table_exists(self.special_table) if hasattr(self.vector_store, 'table_exists') else False
        log_message(f"[Search] Table check - Methods: {'Exists' if method_table_exists else 'Missing'}, Classes: {'Exists' if class_table_exists else 'Missing'}, Special: {'Exists' if special_table_exists else 'Missing'}", logger=self.logger)

        try:
            # Choose search method based on configuration
            if self.hybrid_search_enabled:
                log_message(f"[Search] Starting parallel hybrid searches (alpha={self.hybrid_alpha})...", logger=self.logger)
                # Execute hybrid searches in parallel
                with ThreadPoolExecutor(max_workers=3) as executor:
                    method_future = executor.submit(
                        self.vector_store.search_hybrid,
                        self.method_table,
                        search_query,
                        query_vector,
                        self.max_results
                    )
                    class_future = executor.submit(
                        self.vector_store.search_hybrid,
                        self.class_table,
                        search_query,
                        query_vector,
                        self.max_results
                    )
                    special_future = executor.submit(
                        self.vector_store.search_hybrid,
                        self.special_table,
                        search_query,
                        query_vector,
                        self.max_results
                    ) if special_table_exists else None

                    method_results = method_future.result()
                    class_results = class_future.result()
                    special_results = special_future.result() if special_future else []
            else:
                log_message("[Search] Starting parallel vector searches...", logger=self.logger)
                # Execute vector searches in parallel (fallback)
                with ThreadPoolExecutor(max_workers=3) as executor:
                    method_future = executor.submit(
                        self.vector_store.search,
                        self.method_table,
                        query_vector,
                        self.max_results
                    )
                    class_future = executor.submit(
                        self.vector_store.search,
                        self.class_table,
                        query_vector,
                        self.max_results
                    )
                    special_future = executor.submit(
                        self.vector_store.search,
                        self.special_table,
                        query_vector,
                        self.max_results
                    ) if special_table_exists else None

                    method_results = method_future.result()
                    class_results = class_future.result()
                    special_results = special_future.result() if special_future else []

            log_message(f"[Search] Search completed - Methods: {len(method_results)} results, Classes: {len(class_results)} results, Special: {len(special_results)} results", logger=self.logger)
            return method_results, class_results, special_results

        except Exception as e:
            log_message(f"[Search] ERROR during search: {str(e)}", level="error", logger=self.logger)
            log_message(traceback.format_exc(), logger=self.logger, level="error")
            return [], [], []
    
    def _generate_final_context(
        self,
        method_results: List[Dict[str, Any]],
        class_results: List[Dict[str, Any]],
        special_results: Optional[List[Dict[str, Any]]] = None
    ) -> str:
        """
        Generate final context for answering the query.
        Simplified version that directly formats the context without LLM reranking.

        Args:
            method_results: Method search results
            class_results: Class search results
            special_results: Special file search results

        Returns:
            Final context string
        """
        log_message("Generating final context", logger=self.logger)

        context_parts = []

        # Add methods to context
        if method_results:
            context_parts.append("=== RELEVANT METHODS ===")
            for i, doc in enumerate(method_results[:self.max_results]):
                method_name = doc.get('name', 'Unknown')
                file_path = doc.get('file_path', '')
                source_code = doc.get('source_code', doc.get('code', ''))

                context_parts.append(f"Method {i+1}: {method_name}")
                context_parts.append(f"File: {file_path}")
                if source_code and source_code.strip():
                    context_parts.append(f"Code:\n{source_code}")
                context_parts.append("---")

        # Add classes to context
        if class_results:
            context_parts.append("\n=== RELEVANT CLASSES ===")
            for i, doc in enumerate(class_results[:self.max_results]):
                class_name = doc.get('class_name', 'Unknown')
                file_path = doc.get('file_path', '')
                source_code = doc.get('source_code', '')
                references = doc.get('references', '')

                context_parts.append(f"Class {i+1}: {class_name}")
                context_parts.append(f"File: {file_path}")
                if source_code and source_code.strip():
                    context_parts.append(f"Code:\n{source_code}")
                if references and references.strip() and references != "empty":
                    context_parts.append(f"References: {references}")
                context_parts.append("---")

        # Add special files to context
        if special_results:
            context_parts.append("\n=== RELEVANT DOCUMENTATION ===")
            for i, doc in enumerate(special_results[:self.max_results]):
                file_path = doc.get('file_path', '')
                file_type = doc.get('file_type', 'unknown')
                content = doc.get('content', '')
                chunk_index = doc.get('chunk_index', 0)
                total_chunks = doc.get('total_chunks', 1)

                chunk_info = f" (chunk {chunk_index + 1}/{total_chunks})" if total_chunks > 1 else ""
                context_parts.append(f"Document {i+1}: {file_path}{chunk_info}")
                context_parts.append(f"Type: {file_type}")
                if content and content.strip():
                    context_parts.append(f"Content:\n{content}")
                context_parts.append("---")

        # Combine all parts
        final_context = "\n".join(context_parts)

        # Log context info with token count and search method used
        context_tokens = len(self.embeddings_provider.tokenizer.encode(final_context))
        special_count = len(special_results) if special_results else 0

        # Check which search method was used
        search_methods = set()
        for results in [method_results, class_results, special_results]:
            if results:
                for result in results:
                    if '_search_method' in result:
                        search_methods.add(result['_search_method'])

        search_method_info = f" (Search: {', '.join(search_methods)})" if search_methods else ""
        log_message(f"Generated context with {len(method_results)} methods, {len(class_results)} classes, and {special_count} special files{search_method_info}. Total: {len(final_context)} chars, {context_tokens} tokens", logger=self.logger)

        # Get max_tokens from LLM provider, originally from config
        max_tokens = self.llm_provider.max_context_length

        # Truncate if necessary using token-based truncation
        final_context = truncate_context_by_tokens(final_context, max_tokens, logger=self.logger)

        return final_context