"""
Ultra-simple reference discovery for when the optimized version is still too slow.
This version prioritizes speed over accuracy.
"""

from typing import Dict, List, Set, Tuple, Any, Optional
from collections import defaultdict
import logging
from dataclasses import dataclass

@dataclass
class SimpleReference:
    symbol_name: str
    file_path: str
    line_number: int
    context: str

class SimpleReferenceDiscovery:
    """
    Ultra-fast reference discovery using minimal processing.
    Trades some accuracy for maximum speed.
    """
    
    def __init__(self, logger: Optional[logging.Logger] = None):
        self.logger = logger
        self.symbol_files: Dict[str, str] = {}  # symbol_name -> file_path where defined
        
    def build_symbol_index(self, files_and_symbols: List[Tuple[str, List[Any]]]) -> None:
        """
        Build a simple symbol index.
        
        Args:
            files_and_symbols: List of (file_path, symbols) tuples
        """
        for file_path, symbols in files_and_symbols:
            for symbol in symbols:
                symbol_name = symbol.name if hasattr(symbol, 'name') else str(symbol)
                self.symbol_files[symbol_name.lower()] = file_path
                
        if self.logger:
            self.logger.info(f"Built simple symbol index with {len(self.symbol_files)} symbols")
    
    def find_references_simple(self, file_list: List[Tuple[str, Any]]) -> Dict[str, Dict[str, List[Dict[str, Any]]]]:
        """
        Find references using ultra-simple text matching.
        
        Args:
            file_list: List of (file_path, language) tuples
            
        Returns:
            Dictionary mapping 'class' and 'method' to dictionaries of name -> references
        """
        references: Dict[str, Dict[str, List[Dict[str, Any]]]] = {'class': defaultdict(list), 'method': defaultdict(list)}
        
        if not self.symbol_files:
            return {'class': {}, 'method': {}}
        
        symbol_names = set(self.symbol_files.keys())
        
        if self.logger:
            self.logger.info(f"Finding references for {len(symbol_names)} symbols using simple text search")
        
        processed_files = 0
        total_files = len(file_list)
        
        for file_path, _ in file_list:
            processed_files += 1
            
            try:
                file_references = self._find_references_in_file_simple(file_path, symbol_names)
                
                # Add to results (assume all are methods for simplicity)
                for symbol_name, refs in file_references.items():
                    # Convert to the expected format
                    ref_dicts = []
                    for ref in refs:
                        ref_dicts.append({
                            'file': ref.file_path,
                            'line': ref.line_number,
                            'column': 1,
                            'text': ref.context
                        })
                    references['method'][symbol_name] = ref_dicts
                
                # Progress every 50 files
                if processed_files % 50 == 0 and self.logger:
                    progress = (processed_files / total_files) * 100
                    self.logger.info(f"Simple reference discovery: {processed_files}/{total_files} files ({progress:.1f}%)")
                    
            except Exception as e:
                if self.logger:
                    self.logger.warning(f"Error processing {file_path}: {e}")
        
        return {
            'class': dict(references['class']),
            'method': dict(references['method'])
        }
    
    def _find_references_in_file_simple(self, file_path: str, symbol_names: Set[str]) -> Dict[str, List[SimpleReference]]:
        """
        Find references in a single file using ultra-simple approach.
        """
        references: Dict[str, List[SimpleReference]] = defaultdict(list)
        
        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
            
            # Quick pre-filter: only check symbols that appear in the file
            content_lower = content.lower()
            potential_symbols = [name for name in symbol_names if name in content_lower]
            
            if not potential_symbols:
                return references
            
            # Simple line-by-line search
            lines = content.split('\n')
            
            for line_num, line in enumerate(lines, 1):
                if not line.strip():
                    continue
                
                line_lower = line.lower()
                
                # Check each potential symbol
                for symbol_name in potential_symbols:
                    # Very simple heuristics
                    if (symbol_name in line_lower and 
                        (f"{symbol_name}(" in line_lower or 
                         f" {symbol_name} " in line_lower or
                         f".{symbol_name}" in line_lower)):
                        
                        references[symbol_name].append(SimpleReference(
                            symbol_name=symbol_name,
                            file_path=file_path,
                            line_number=line_num,
                            context=line.strip()
                        ))
                        
        except Exception:
            # Silently ignore errors for maximum speed
            pass
                
        return references
    
    def get_statistics(self) -> Dict[str, int]:
        """Get basic statistics."""
        return {
            'total_symbols': len(self.symbol_files),
            'unique_symbol_names': len(self.symbol_files)
        }

def create_simple_reference_discovery(classes: List[Any], methods: List[Any], logger: Optional[logging.Logger] = None) -> SimpleReferenceDiscovery:
    """
    Create and initialize a simple reference discovery instance.
    
    Args:
        classes: List of class objects
        methods: List of method objects
        logger: Optional logger
        
    Returns:
        Initialized SimpleReferenceDiscovery instance
    """
    discovery = SimpleReferenceDiscovery(logger)
    
    # Build files and symbols list
    files_and_symbols = []
    file_symbols_map: Dict[str, List[Any]] = {}
    
    # Group symbols by file
    for method in methods:
        file_path = method.file_path
        if file_path not in file_symbols_map:
            file_symbols_map[file_path] = []
        file_symbols_map[file_path].append(method)
    
    for cls in classes:
        file_path = cls.file_path
        if file_path not in file_symbols_map:
            file_symbols_map[file_path] = []
        file_symbols_map[file_path].append(cls)
    
    # Convert to the format expected by the discovery
    for file_path, symbols in file_symbols_map.items():
        files_and_symbols.append((file_path, symbols))
    
    # Build the index
    discovery.build_symbol_index(files_and_symbols)
    
    return discovery
