"""
Optimized reference discovery for large codebases.
Uses symbol indexing and parallel processing instead of O(n²) file scanning.
"""

from typing import Dict, List, Set, Tuple
from collections import defaultdict
import re
from concurrent.futures import ThreadPoolExecutor, as_completed
import logging
from dataclasses import dataclass

from flow_rag.infrastructure.log_utils import log_message

@dataclass
class Symbol:
    name: str
    type: str  # 'function', 'class', 'variable'
    file_path: str
    line_number: int

@dataclass
class Reference:
    symbol_name: str
    file_path: str
    line_number: int
    context: str

class OptimizedReferenceDiscovery:
    """
    Fast reference discovery using symbol indexing.
    Reduces complexity from O(n²) to O(n log n).
    """
    
    def __init__(self, logger: logging.Logger):
        self.logger = logger
        self.symbol_index: Dict[str, List[Symbol]] = defaultdict(list)
        self.file_symbols: Dict[str, List[Symbol]] = defaultdict(list)
        
    def build_symbol_index(self, files_and_symbols: List[Tuple[str, List[Symbol]]]) -> None:
        """
        Build an inverted index of symbols for fast lookup.
        
        Args:
            files_and_symbols: List of (file_path, symbols) tuples
        """
        self.symbol_index.clear()
        self.file_symbols.clear()
        
        for file_path, symbols in files_and_symbols:
            self.file_symbols[file_path] = symbols
            
            for symbol in symbols:
                self.symbol_index[symbol.name.lower()].append(symbol)
                
            # total_symbols = sum(len(symbols) for symbols in self.symbol_index.values())
    
    def find_references_parallel(self, max_workers: int = 8) -> Dict[str, List[Reference]]:
        """
        Find all references using parallel processing.
        
        Args:
            max_workers: Number of parallel workers
            
        Returns:
            Dictionary mapping symbol names to their references
        """
        references = defaultdict(list)
        
        # Get all unique symbol names
        symbol_names = set(self.symbol_index.keys())
        
        log_message(f"Finding references for {len(symbol_names)} symbols using {max_workers} workers", logger=self.logger, to_terminal=True)
        
        # Process files in parallel
        # total_files = len(self.file_symbols)
        processed_files = 0

        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # Submit tasks for each file
            future_to_file = {
                executor.submit(self._find_references_in_file, file_path, symbol_names): file_path
                for file_path in self.file_symbols.keys()
            }

            # Collect results with progress tracking
            for future in as_completed(future_to_file):
                file_path = future_to_file[future]
                processed_files += 1

                try:
                    file_references = future.result()

                    # Count total references found in this file
                    # total_refs_in_file = sum(len(refs) for refs in file_references.values())

                    # Merge results
                    for symbol_name, refs in file_references.items():
                        references[symbol_name].extend(refs)

                    # Progress log every 10 files or for significant findings
                    # if processed_files % 10 == 0 or total_refs_in_file > 0:
                        # progress = (processed_files / total_files) * 100
                        # log_message(f"[ReferenceOptimizer] Progress: {processed_files}/{total_files} files ({progress:.1f}%) - Found {total_refs_in_file} references in {file_path}", logger=self.logger)

                except Exception as e:
                    log_message(f"[ReferenceOptimizer] Error processing {file_path}: {e}", logger=self.logger, level="error")
        
        return dict(references)

    def find_references_fast(self, max_workers: int = 4) -> Dict[str, List[Reference]]:
        """
        Ultra-fast reference discovery using simple text matching.
        Use this as fallback when the full regex approach is too slow.

        Args:
            max_workers: Number of parallel workers

        Returns:
            Dictionary mapping symbol names to their references
        """
        references = defaultdict(list)

        # Get all unique symbol names
        symbol_names = set(self.symbol_index.keys())

        if self.logger:
            log_message(f"[FastReferences] Finding references for {len(symbol_names)} symbols using simple text matching", logger=self.logger)

        # Process files in parallel with simple text search
        total_files = len(self.file_symbols)
        processed_files = 0

        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # Submit tasks for each file
            future_to_file = {
                executor.submit(self._find_references_fast_in_file, file_path, symbol_names): file_path
                for file_path in self.file_symbols.keys()
            }

            # Collect results with progress tracking
            for future in as_completed(future_to_file):
                file_path = future_to_file[future]
                processed_files += 1

                try:
                    file_references = future.result()

                    # Count total references found in this file
                    # total_refs_in_file = sum(len(refs) for refs in file_references.values())

                    # Merge results
                    for symbol_name, refs in file_references.items():
                        references[symbol_name].extend(refs)

                    # Progress log every 20 files
                    if processed_files % 20 == 0:
                        progress = (processed_files / total_files) * 100
                        log_message(f"[FastReferences] Progress: {processed_files}/{total_files} files ({progress:.1f}%)", logger=self.logger)

                except Exception as e:
                    if self.logger:
                        log_message(f"[FastReferences] Error processing {file_path}: {e}", logger=self.logger, level="error")

        return dict(references)

    def _find_references_fast_in_file(self, file_path: str, symbol_names: Set[str]) -> Dict[str, List[Reference]]:
        """
        Ultra-fast reference finding using only simple text matching.
        """
        references = defaultdict(list)

        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()

            # Pre-filter symbols that are definitely in this file
            potential_symbols = []
            for symbol_name in symbol_names:
                if symbol_name.lower() in content.lower():
                    potential_symbols.append(symbol_name)

            # If no potential symbols found, return early
            if not potential_symbols:
                return references

            # Simple line-by-line search for potential symbols
            lines = content.split('\n')

            for line_num, line in enumerate(lines, 1):
                line_stripped = line.strip()

                # Skip empty lines and obvious comments
                if not line_stripped or line_stripped.startswith("'") or line_stripped.startswith("//"):
                    continue

                line_lower = line_stripped.lower()

                # Check each potential symbol with simple patterns
                for symbol_name in potential_symbols:
                    symbol_lower = symbol_name.lower()

                    # Simple heuristics for function calls and references
                    if (f"{symbol_lower}(" in line_lower or
                        f" {symbol_lower} " in line_lower or
                        f".{symbol_lower}" in line_lower or
                        f"call {symbol_lower}" in line_lower):

                        references[symbol_name].append(Reference(
                            symbol_name=symbol_name,
                            file_path=file_path,
                            line_number=line_num,
                            context=line_stripped
                        ))

        except Exception:
            # Silently continue on errors for fast processing
            pass

        return references
    
    def _find_references_in_file(self, file_path: str, symbol_names: Set[str]) -> Dict[str, List[Reference]]:
        """
        Find references to symbols in a single file using optimized approach.

        Args:
            file_path: Path to the file to scan
            symbol_names: Set of symbol names to look for

        Returns:
            Dictionary mapping symbol names to references found in this file
        """
        references = defaultdict(list)

        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()

            # Pre-filter symbols that might be in this file (simple text search first)
            potential_symbols = set()
            for symbol_name in symbol_names:
                if symbol_name.lower() in content.lower():
                    potential_symbols.add(symbol_name)

            # If no potential symbols found, return early
            if not potential_symbols:
                return references

            # Only process lines if we found potential symbols
            lines = content.split('\n')

            for line_num, line in enumerate(lines, 1):
                line_stripped = line.strip()

                # Skip empty lines and comments
                if not line_stripped or self._is_comment(line_stripped, file_path):
                    continue

                # Only check symbols that were found in the file
                for symbol_name in potential_symbols:
                    if symbol_name.lower() in line_stripped.lower():  # Quick check first
                        if self._is_symbol_referenced(line_stripped, symbol_name, file_path):
                            references[symbol_name].append(Reference(
                                symbol_name=symbol_name,
                                file_path=file_path,
                                line_number=line_num,
                                context=line_stripped
                            ))

        except Exception as e:
            log_message(f"[ReferenceOptimizer] Error reading file {file_path}: {e}", logger=self.logger, level="error")

        return references
    
    def _is_comment(self, line: str, file_path: str) -> bool:
        """Check if a line is a comment based on file extension."""
        ext = file_path.lower().split('.')[-1]
        
        comment_patterns = {
            'vb': ["'", "rem "],
            'frm': ["'", "rem "],
            'bas': ["'", "rem "],
            'cls': ["'", "rem "],
            'py': ["#"],
            'js': ["//", "/*"],
            'java': ["//", "/*"],
            'c': ["//", "/*"],
            'cpp': ["//", "/*"],
        }
        
        patterns = comment_patterns.get(ext, [])
        return any(line.lower().startswith(pattern) for pattern in patterns)
    
    def _is_symbol_referenced(self, line: str, symbol_name: str, file_path: str) -> bool:
        """
        Check if a symbol is referenced in a line of code.
        Uses language-specific patterns for better accuracy.
        """
        ext = file_path.lower().split('.')[-1]
        
        # VB6/VBA patterns
        if ext in ['vb', 'frm', 'bas', 'cls']:
            return self._is_vb6_symbol_reference(line, symbol_name)
        
        # Default patterns for other languages
        return self._is_generic_symbol_reference(line, symbol_name)
    
    def _is_vb6_symbol_reference(self, line: str, symbol_name: str) -> bool:
        """Check for VB6-specific symbol references using optimized approach."""
        # Quick case-insensitive check first
        line_lower = line.lower()
        symbol_lower = symbol_name.lower()

        # If symbol not in line at all, return False immediately
        if symbol_lower not in line_lower:
            return False

        # Only use regex if simple check passed
        patterns = [
            rf'\b{re.escape(symbol_name)}\s*\(',  # Function call
            rf'Call\s+{re.escape(symbol_name)}\b',  # Call statement
            rf'\.{re.escape(symbol_name)}\b',  # Method call
            rf'\b{re.escape(symbol_name)}\s*=',  # Assignment target
            rf'=\s*{re.escape(symbol_name)}\b',  # Assignment source
            rf'New\s+{re.escape(symbol_name)}\b',  # Object creation
            rf'As\s+{re.escape(symbol_name)}\b',  # Type declaration
        ]

        for pattern in patterns:
            if re.search(pattern, line, re.IGNORECASE):
                return True
        return False
    
    def _is_generic_symbol_reference(self, line: str, symbol_name: str) -> bool:
        """Check for generic symbol references using optimized approach."""
        # Quick case-insensitive check first
        line_lower = line.lower()
        symbol_lower = symbol_name.lower()

        # If symbol not in line at all, return False immediately
        if symbol_lower not in line_lower:
            return False

        # Only use regex if simple check passed
        patterns = [
            rf'\b{re.escape(symbol_name)}\s*\(',  # Function call
            rf'\.{re.escape(symbol_name)}\b',  # Method call
            rf'\b{re.escape(symbol_name)}\s*=',  # Assignment
            rf'=\s*{re.escape(symbol_name)}\b',  # Assignment
        ]

        for pattern in patterns:
            if re.search(pattern, line, re.IGNORECASE):
                return True
        return False
    
    def get_reference_graph(self) -> Dict[str, Dict[str, int]]:
        """
        Generate a reference graph showing relationships between files.
        
        Returns:
            Dictionary mapping file paths to their dependencies
        """
        graph = defaultdict(lambda: defaultdict(int))
        
        references = self.find_references_parallel()
        
        for symbol_name, refs in references.items():
            # Find where this symbol is defined
            symbol_definitions = self.symbol_index.get(symbol_name.lower(), [])
            
            for definition in symbol_definitions:
                for reference in refs:
                    if reference.file_path != definition.file_path:
                        # reference.file_path depends on definition.file_path
                        graph[reference.file_path][definition.file_path] += 1
        
        return dict(graph)
    
    def get_statistics(self) -> Dict[str, int]:
        """Get statistics about the symbol index."""
        return {
            'total_files': len(self.file_symbols),
            'total_symbols': sum(len(symbols) for symbols in self.symbol_index.values()),
            'unique_symbol_names': len(self.symbol_index),
            'avg_symbols_per_file': sum(len(symbols) for symbols in self.file_symbols.values()) / max(len(self.file_symbols), 1)
        }
