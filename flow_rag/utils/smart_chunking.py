"""
Smart chunking strategy for different file sizes and types.
Optimizes embedding strategy based on file characteristics.
"""

from typing import List, Dict
from dataclasses import dataclass
from enum import Enum
import re

class ChunkStrategy(Enum):
    FULL_FILE = "full_file"          # Small files: embed entire file
    FUNCTION_BASED = "function_based" # Medium files: chunk by functions
    SECTION_BASED = "section_based"   # Large files: chunk by logical sections
    LINE_BASED = "line_based"         # Very large files: fixed-size chunks

@dataclass
class FileChunk:
    content: str
    start_line: int
    end_line: int
    chunk_type: str  # 'file', 'function', 'section', 'lines'
    metadata: Dict[str, str]

@dataclass
class ChunkingConfig:
    small_file_threshold: int = 500      # Lines
    medium_file_threshold: int = 2000    # Lines
    large_file_threshold: int = 5000     # Lines
    max_chunk_size: int = 1000           # Lines per chunk
    overlap_size: int = 50               # Lines of overlap between chunks

class SmartChunker:
    """
    Intelligent chunking based on file size and content structure.
    """
    
    def __init__(self, config: ChunkingConfig = None):
        self.config = config or ChunkingConfig()
    
    def determine_strategy(self, file_path: str, line_count: int) -> ChunkStrategy:
        """
        Determine the best chunking strategy for a file.
        
        Args:
            file_path: Path to the file
            line_count: Number of lines in the file
            
        Returns:
            Recommended chunking strategy
        """
        if line_count <= self.config.small_file_threshold:
            return ChunkStrategy.FULL_FILE
        elif line_count <= self.config.medium_file_threshold:
            return ChunkStrategy.FUNCTION_BASED
        elif line_count <= self.config.large_file_threshold:
            return ChunkStrategy.SECTION_BASED
        else:
            return ChunkStrategy.LINE_BASED
    
    def chunk_file(self, file_path: str, content: str, symbols: List[Dict] = None) -> List[FileChunk]:
        """
        Chunk a file based on its characteristics.
        
        Args:
            file_path: Path to the file
            content: File content
            symbols: Optional list of symbols (functions, classes) found in the file
            
        Returns:
            List of file chunks
        """
        lines = content.split('\n')
        line_count = len(lines)
        strategy = self.determine_strategy(file_path, line_count)
        
        if strategy == ChunkStrategy.FULL_FILE:
            return self._chunk_full_file(file_path, content, lines)
        elif strategy == ChunkStrategy.FUNCTION_BASED:
            return self._chunk_by_functions(file_path, content, lines, symbols)
        elif strategy == ChunkStrategy.SECTION_BASED:
            return self._chunk_by_sections(file_path, content, lines)
        else:  # LINE_BASED
            return self._chunk_by_lines(file_path, content, lines)
    
    def _chunk_full_file(self, file_path: str, content: str, lines: List[str]) -> List[FileChunk]:
        """Embed the entire file as a single chunk."""
        return [FileChunk(
            content=content,
            start_line=1,
            end_line=len(lines),
            chunk_type='file',
            metadata={
                'file_path': file_path,
                'strategy': 'full_file',
                'line_count': str(len(lines))
            }
        )]
    
    def _chunk_by_functions(self, file_path: str, content: str, lines: List[str], symbols: List[Dict] = None) -> List[FileChunk]:
        """Chunk by functions/methods with context."""
        chunks = []
        
        if not symbols:
            # Fallback to section-based if no symbols provided
            return self._chunk_by_sections(file_path, content, lines)
        
        # Sort symbols by line number
        sorted_symbols = sorted(symbols, key=lambda s: s.get('start_line', 0))
        
        last_end = 0
        for i, symbol in enumerate(sorted_symbols):
            start_line = max(symbol.get('start_line', 1) - 1, last_end)  # 0-based indexing
            end_line = symbol.get('end_line', start_line + 50)  # Default to 50 lines if not specified
            
            # Ensure we don't overlap with next symbol
            if i + 1 < len(sorted_symbols):
                next_start = sorted_symbols[i + 1].get('start_line', end_line + 1) - 1
                end_line = min(end_line, next_start)
            
            # Extract chunk content
            chunk_lines = lines[start_line:end_line]
            chunk_content = '\n'.join(chunk_lines)
            
            chunks.append(FileChunk(
                content=chunk_content,
                start_line=start_line + 1,  # Convert back to 1-based
                end_line=end_line,
                chunk_type='function',
                metadata={
                    'file_path': file_path,
                    'strategy': 'function_based',
                    'symbol_name': symbol.get('name', 'unknown'),
                    'symbol_type': symbol.get('type', 'unknown')
                }
            ))
            
            last_end = end_line
        
        # Add any remaining content as a final chunk
        if last_end < len(lines):
            remaining_content = '\n'.join(lines[last_end:])
            if remaining_content.strip():  # Only if there's meaningful content
                chunks.append(FileChunk(
                    content=remaining_content,
                    start_line=last_end + 1,
                    end_line=len(lines),
                    chunk_type='remainder',
                    metadata={
                        'file_path': file_path,
                        'strategy': 'function_based',
                        'symbol_name': 'file_remainder',
                        'symbol_type': 'remainder'
                    }
                ))
        
        return chunks
    
    def _chunk_by_sections(self, file_path: str, content: str, lines: List[str]) -> List[FileChunk]:
        """Chunk by logical sections (comments, imports, etc.)."""
        chunks = []
        current_chunk_lines = []
        current_start = 1
        section_type = 'code'
        
        for i, line in enumerate(lines):
            current_chunk_lines.append(line)
            
            # Check if we should start a new chunk
            should_break = (
                len(current_chunk_lines) >= self.config.max_chunk_size or
                self._is_section_boundary(line, file_path)
            )
            
            if should_break and current_chunk_lines:
                chunk_content = '\n'.join(current_chunk_lines)
                chunks.append(FileChunk(
                    content=chunk_content,
                    start_line=current_start,
                    end_line=i + 1,
                    chunk_type='section',
                    metadata={
                        'file_path': file_path,
                        'strategy': 'section_based',
                        'section_type': section_type
                    }
                ))
                
                # Start new chunk with overlap
                overlap_start = max(0, len(current_chunk_lines) - self.config.overlap_size)
                current_chunk_lines = current_chunk_lines[overlap_start:]
                current_start = i + 1 - len(current_chunk_lines) + 1
        
        # Add final chunk
        if current_chunk_lines:
            chunk_content = '\n'.join(current_chunk_lines)
            chunks.append(FileChunk(
                content=chunk_content,
                start_line=current_start,
                end_line=len(lines),
                chunk_type='section',
                metadata={
                    'file_path': file_path,
                    'strategy': 'section_based',
                    'section_type': 'final'
                }
            ))
        
        return chunks
    
    def _chunk_by_lines(self, file_path: str, content: str, lines: List[str]) -> List[FileChunk]:
        """Chunk by fixed line count with overlap."""
        chunks = []
        
        for i in range(0, len(lines), self.config.max_chunk_size - self.config.overlap_size):
            end_idx = min(i + self.config.max_chunk_size, len(lines))
            chunk_lines = lines[i:end_idx]
            chunk_content = '\n'.join(chunk_lines)
            
            chunks.append(FileChunk(
                content=chunk_content,
                start_line=i + 1,
                end_line=end_idx,
                chunk_type='lines',
                metadata={
                    'file_path': file_path,
                    'strategy': 'line_based',
                    'chunk_index': str(len(chunks))
                }
            ))
        
        return chunks
    
    def _is_section_boundary(self, line: str, file_path: str) -> bool:
        """Check if a line represents a logical section boundary."""
        ext = file_path.lower().split('.')[-1]
        
        # VB6 section boundaries
        if ext in ['vb', 'frm', 'bas', 'cls']:
            vb6_boundaries = [
                r'^\s*Option\s+',
                r'^\s*Attribute\s+',
                r'^\s*(Public|Private|Friend)\s+(Function|Sub|Property)',
                r'^\s*Type\s+\w+',
                r'^\s*Enum\s+\w+',
                r'^\s*\'.*={5,}',  # Comment separators
            ]
            return any(re.match(pattern, line, re.IGNORECASE) for pattern in vb6_boundaries)
        
        # Generic boundaries
        generic_boundaries = [
            r'^\s*import\s+',
            r'^\s*from\s+.*import',
            r'^\s*#include\s+',
            r'^\s*class\s+\w+',
            r'^\s*function\s+\w+',
            r'^\s*def\s+\w+',
        ]
        return any(re.match(pattern, line, re.IGNORECASE) for pattern in generic_boundaries)
    
    def get_chunking_stats(self, chunks: List[FileChunk]) -> Dict[str, any]:
        """Get statistics about the chunking results."""
        if not chunks:
            return {}
        
        chunk_sizes = [chunk.end_line - chunk.start_line + 1 for chunk in chunks]
        chunk_types = [chunk.chunk_type for chunk in chunks]
        
        return {
            'total_chunks': len(chunks),
            'chunk_types': {chunk_type: chunk_types.count(chunk_type) for chunk_type in set(chunk_types)},
            'avg_chunk_size': sum(chunk_sizes) / len(chunk_sizes),
            'min_chunk_size': min(chunk_sizes),
            'max_chunk_size': max(chunk_sizes),
            'strategy': chunks[0].metadata.get('strategy', 'unknown')
        }
