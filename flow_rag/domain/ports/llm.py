from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, List  # Used for type hints in interface definitions


class LLMPort(ABC):
    """
    Interface for Large Language Model providers.
    
    Implementations should be able to:
    1. Generate completions for prompts
    2. Generate embeddings for text
    3. Handle system and user prompts
    """
    
    @abstractmethod
    def send_via_flow(self, 
                   user_prompt: str, 
                   system_prompt: str) -> str:
        """
        Send a prompt to the LLM and get a completion.
        
        Args:
            user_prompt: The user prompt to send to the LLM
            system_prompt: System prompt to set context for the LLM
            
        Returns:
            Generated text completion
        """
        pass
    
    @abstractmethod
    def generate_context(self, 
                         query: str, 
                         retrieved_content: List[Dict[str, Any]],
                         rerank: bool = False) -> str:
        """
        Generate a context for a query using retrieved content.
        
        Args:
            query: The user query
            retrieved_content: List of retrieved content items from vector store
            rerank: Whether to rerank results using the LLM
            
        Returns:
            Generated context string
        """
        pass
    
    @abstractmethod
    def answer_with_context(self, 
                           query: str, 
                           context: str) -> str:
        """
        Generate an answer to a query using provided context.
        
        Args:
            query: The user query
            context: The context to use for answering
            
        Returns:
            Generated answer
        """
        pass
    
    @abstractmethod
    def enhance_query(self, 
                     query: str, 
                     context: Optional[str] = None) -> str:
        """
        Enhance or rewrite a query to make it more effective for retrieval.
        
        Args:
            query: The original query
            context: Optional context to help with query enhancement
            
        Returns:
            Enhanced query
        """
        pass
    
    @abstractmethod
    def rerank_results(self, 
                      query: str,
                      results: List[Dict[str, Any]],
                      content_key: str = "content") -> List[Dict[str, Any]]:
        """
        Rerank search results using the LLM.
        
        Args:
            query: The original query
            results: List of search results to rerank
            content_key: Key in the result dictionaries containing the content to rerank
            
        Returns:
            Reranked list of results
        """
        pass
    
    @property
    @abstractmethod
    def max_context_length(self) -> int:
        """
        Get the maximum context length supported by this LLM.
        
        Returns:
            Maximum number of tokens allowed in the context
        """
        pass
    
    @property
    @abstractmethod
    def model_name(self) -> str:
        """
        Get the name of the LLM model.
        
        Returns:
            Model name as string
        """
        pass