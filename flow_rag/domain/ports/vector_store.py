from abc import ABC, abstractmethod
from typing import List, Dict, Any, Union, TypeVar, Generic  # Used for type hints and generics in interface definitions

from ...domain.models import EmbeddedMethod, EmbeddedClass

T = TypeVar('T', bound=Union[EmbeddedMethod, EmbeddedClass])


class VectorStorePort(Generic[T], ABC):
    """
    Interface for vector database storage and retrieval operations.
    
    Implementations should be able to:
    1. Store embeddings for different types of code objects
    2. Search for similar vectors given a query vector
    3. Manage tables and connections to the vector database
    """
    
    @abstractmethod
    def connect(self, uri: str) -> bool:
        """
        Connect to the vector store.
        
        Args:
            uri: Connection string or path to the vector store
            
        Returns:
            True if connection was successful, False otherwise
        """
        pass
    
    @abstractmethod
    def create_table(self, table_name: str, schema: Any, mode: str = "create") -> Any:
        """
        Create a new table in the vector store.
        
        Args:
            table_name: Name of the table to create
            schema: Schema definition for the table
            mode: Table creation mode (create, overwrite, etc.)
            
        Returns:
            Reference to the created table
        """
        pass
    
    @abstractmethod
    def add_vectors(self, table_name: str, vectors: List[T]) -> bool:
        """
        Add vectors to a table.
        
        Args:
            table_name: Name of the table
            vectors: List of objects with embedded vectors to add
            
        Returns:
            True if vectors were added successfully, False otherwise
        """
        pass
    
    @abstractmethod
    def search(self, table_name: str, query_vector: List[float], limit: int = 10) -> List[Dict[str, Any]]:
        """
        Search for similar vectors in a table.
        
        Args:
            table_name: Name of the table to search in
            query_vector: Query vector to search for
            limit: Maximum number of results to return
            
        Returns:
            List of dictionaries containing search results
        """
        pass
    
    @abstractmethod
    def search_hybrid(self, table_name: str, query_text: str, query_vector: List[float], limit: int = 10) -> List[Dict[str, Any]]:
        """
        Perform a hybrid search using both text and vector similarity.
        
        Args:
            table_name: Name of the table to search in
            query_text: Text query for keyword search
            query_vector: Vector for semantic search
            limit: Maximum number of results to return
            
        Returns:
            List of dictionaries containing search results
        """
        pass
    
    @abstractmethod
    def rerank(self, table_name: str, results: List[Dict[str, Any]], query: str) -> List[Dict[str, Any]]:
        """
        Rerank search results using a reranker.
        
        Args:
            table_name: Name of the table
            results: Search results to rerank
            query: Original query text
            
        Returns:
            Reranked list of results
        """
        pass
    
    @abstractmethod
    def table_exists(self, table_name: str) -> bool:
        """
        Check if a table exists in the vector store.
        
        Args:
            table_name: Name of the table to check
            
        Returns:
            True if the table exists, False otherwise
        """
        pass
    
    @abstractmethod
    def close(self) -> None:
        """
        Close the connection to the vector store.
        """
        pass