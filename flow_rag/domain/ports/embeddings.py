from abc import ABC, abstractmethod
from typing import List, Dict, Any  # Used for type hints in interface definitions

from ...domain.models import Method, Class, SpecialFile, EmbeddedMethod, EmbeddedClass


class EmbeddingsPort(ABC):
    """
    Interface for generating vector embeddings from text.
    
    Implementations should be able to:
    1. Generate embeddings for code methods
    2. Generate embeddings for classes
    3. Generate embeddings for text queries
    """
    
    @abstractmethod
    def get_embedding_dimensions(self) -> int:
        """
        Get the dimensions of the embeddings produced by this provider.
        
        Returns:
            Number of dimensions in the embedding vectors
        """
        pass
    
    @abstractmethod
    def embed_methods(self, methods: List[Method]) -> List[EmbeddedMethod]:
        """
        Generate embeddings for a list of Method objects.
        
        Args:
            methods: List of Method objects to embed
            
        Returns:
            List of EmbeddedMethod objects
        """
        pass
    
    @abstractmethod
    def embed_classes(self, classes: List[Class]) -> List[EmbeddedClass]:
        """
        Generate embeddings for a list of Class objects.
        
        Args:
            classes: List of Class objects to embed
            
        Returns:
            List of EmbeddedClass objects
        """
        pass
    
    @abstractmethod
    def embed_query(self, query: str) -> List[float]:
        """
        Generate embeddings for a text query.
        
        Args:
            query: The query text to embed
            
        Returns:
            Embedding vector as a list of floats
        """
        pass
    
    @abstractmethod
    def embed_special_files(self, special_files: List[SpecialFile]) -> List[Dict[str, Any]]:
        """
        Generate embeddings for special files like markdown or shell scripts.
        
        Args:
            special_files: List of SpecialFile objects to embed
            
        Returns:
            List of dictionaries with embeddings data
        """
        pass
    
    @abstractmethod
    def max_token_limit(self) -> int:
        """
        Get the maximum number of tokens supported by this embedding model.
        
        Returns:
            Maximum token count allowed for embedding
        """
        pass
    
    @abstractmethod
    def clip_text_to_max_tokens(self, text: str) -> str:
        """
        Clip text to the maximum token limit for this embedding model.
        
        Args:
            text: The text to clip
            
        Returns:
            Clipped text
        """
        pass
    
    @property
    @abstractmethod
    def model_name(self) -> str:
        """
        Get the name of the embedding model.
        
        Returns:
            Model name as string
        """
        pass