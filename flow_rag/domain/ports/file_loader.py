from typing import List, Tuple, Optional  # Used for type hints in interface definitions
from enum import Enum

class LanguageEnum(Enum):
    PYTHON = "python"
    JAVASCRIPT = "javascript"
    JAVA = "java"
    TYPESCRIPT = "typescript"
    VB6 = "vb6"
    MARKDOWN = "markdown"
    SHELL = "shell"
    JSON = "json"
    YAML = "yaml"
    TOML = "toml"
    TEXT = "text"
    HTML = "html"
    UNKNOWN = "unknown"

def get_main_extension(language_enum: LanguageEnum) -> Optional[str]:
    """
    Get the main file extension for a given language enum.
    """
    # Mapeamento inverso para encontrar a primeira extensão para cada linguagem
    for ext, lang in EXTENSION_MAP.items():
        if lang == language_enum:
            return ext
    return None

# File extensions and their corresponding programming languages
EXTENSION_MAP = {
    # Python files
    '.py': LanguageEnum.PYTHON,
    '.pyx': LanguageEnum.PYTHON,  # Cython files (Python syntax)

    # Java
    '.java': LanguageEnum.JAVA,

    # JavaScript/TypeScript files
    '.js': LanguageEnum.JAVASCRIPT,
    '.jsx': LanguageEnum.JAVASCRIPT,
    '.mjs': LanguageEnum.JAVASCRIPT,  # ES modules
    '.ts': LanguageEnum.TYPESCRIPT,
    '.tsx': LanguageEnum.TYPESCRIPT,

    # Visual Basic 6 files
    '.bas': LanguageEnum.VB6,  # Basic module
    '.cls': LanguageEnum.VB6,  # Class module
    '.frm': LanguageEnum.VB6,  # Form module
    '.vbp': LanguageEnum.VB6,  # Project file

    # Markdown files
    '.md': LanguageEnum.MARKDOWN,
    '.markdown': LanguageEnum.MARKDOWN,

    # Shell script files
    '.sh': LanguageEnum.SHELL,
    '.bash': LanguageEnum.SHELL,
    '.zsh': LanguageEnum.SHELL,
    '.ps1': LanguageEnum.SHELL,

    # JSON files
    '.json': LanguageEnum.JSON,

    # YAML files
    '.yaml': LanguageEnum.YAML,
    '.yml': LanguageEnum.YAML,

    # TOML files
    '.toml': LanguageEnum.TOML,

    # Text files
    '.txt': LanguageEnum.TEXT,
    '.ini': LanguageEnum.TEXT,

    # HTML files
    '.html': LanguageEnum.HTML,
    '.htm': LanguageEnum.HTML,
}

# Filters for Tree-sitter FileLoader/Parser
TREE_SITTER_FILE_FILTERS = {
    "WHITELIST_EXTS": [
        '.py', '.pyx', '.java', '.js', '.jsx', '.mjs', '.ts', '.tsx'
    ],
    "BLACKLIST_FILES": [
        'package-lock.json', 'yarn.lock', 'poetry.lock',
        'Pipfile', 'Pipfile.lock'
    ],
    "SPECIAL_FILE_EXTS": [
        '.md', '.sh', '.json', '.txt', '.yaml', '.yml', '.toml', '.html', '.htm'
    ]
}

# Mapping of file extensions to their special file types
SPECIAL_FILE_TYPES = {
    '.md': 'markdown',
    '.markdown': 'markdown',
    '.sh': 'shell',
    '.bash': 'shell',
    '.zsh': 'shell',
    '.json': 'json',
    '.txt': 'text',
    '.yaml': 'yaml',
    '.yml': 'yaml',
    '.toml': 'toml',
    '.html': 'html',
    '.htm': 'html',
    # VB6 special files
    '.ini': 'text',  # Configuration files
    '.ps1': 'shell'  # PowerShell scripts
}

# VB6 file filters
VB6_FILE_FILTERS = {
    "WHITELIST_EXTS": ['.bas', '.cls', '.frm', '.vbp'],
    "BLACKLIST_FILES": [
        # VB6 binary/metadata files that are not useful for code analysis
        '*.frx',  # Form resource files (binary)
        '*.scc',  # Source code control files
        '*.vbw',  # Visual Basic workspace files
        '*.exp'   # Export files
    ],
    "SPECIAL_FILE_EXTS": ['.ini', '.ps1']  # Configuration and PowerShell scripts
}

# Consolidate of filters
FILE_FILTERS = {
    "WHITELIST_EXTS": TREE_SITTER_FILE_FILTERS["WHITELIST_EXTS"] + VB6_FILE_FILTERS["WHITELIST_EXTS"],
    "BLACKLIST_DIRS": [
        "__pycache__", ".pytest_cache", ".venv", ".git", ".idea",
        "venv", "env", "node_modules", "dist", "build", ".vscode",
        ".github", ".gitlab", ".angular", "cdk.out", ".aws-sam", ".terraform",
        ".coder-ide", ".pytest_cache"
    ],
    "BLACKLIST_FILES": TREE_SITTER_FILE_FILTERS["BLACKLIST_FILES"] + VB6_FILE_FILTERS["BLACKLIST_FILES"],
    "SPECIAL_FILE_EXTS": TREE_SITTER_FILE_FILTERS["SPECIAL_FILE_EXTS"] + VB6_FILE_FILTERS["SPECIAL_FILE_EXTS"]
}

class FileLoaderPort:
    """
    Interface for loading and filtering code files from a codebase.
    """
    
    def load_files(self, codebase_path: str) -> List[Tuple[str, LanguageEnum]]:
        """
        Load and filter files from the specified codebase path.
        
        Args:
            codebase_path: Path to the root of the codebase
            
        Returns:
            List of tuples containing (file_path, language_enum) for each valid file
        """
        raise NotImplementedError
    
    def get_file_language(self, file_path: str) -> Optional[LanguageEnum]:
        """
        Determine the language of a file based on its extension.
        
        Args:
            file_path: Path to the file
            
        Returns:
            LanguageEnum if the file type is supported, None otherwise
        """
        raise NotImplementedError
