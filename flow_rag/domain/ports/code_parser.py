from abc import ABC, abstractmethod
from typing import List, <PERSON><PERSON>, Dict, Any, Optional  # Used for type hints in interface definitions

from ...domain.models import Method, Class, LanguageEnum


class CodeParserPort(ABC):
    """
    Interface defining code parsing capabilities.
    
    Implementations should be able to:
    1. Load code files from a directory
    2. Parse code to extract structured information
    3. Find references between code entities
    """
    
    @abstractmethod
    def parse_code_files(self, file_list: List[Tuple[str, LanguageEnum]]) -> Tuple[List[Class], List[Method], List[str], List[str]]:
        """
        Parse a list of code files and extract classes and methods.
        
        Args:
            file_list: List of tuples (file_path, language_enum)
            
        Returns:
            Tuple containing:
            - List of Class objects
            - List of Method objects
            - List of class names
            - List of method names
        """
        pass
    
    @abstractmethod
    def find_references(self, file_list: List[Tuple[str, LanguageEnum]], class_names: List[str], method_names: List[str]) -> Dict[str, Dict[str, List[Dict[str, Any]]]]:
        """
        Find references to classes and methods in the codebase.
        
        Args:
            file_list: List of tuples (file_path, language_enum)
            class_names: List of class names to find references for
            method_names: List of method names to find references for
            
        Returns:
            Dictionary mapping 'class' and 'method' to dictionaries of name -> references
        """
        pass
    
    @abstractmethod
    def get_language_from_extension(self, file_ext: str) -> Optional[LanguageEnum]:
        """
        Determine the programming language from a file extension.
        
        Args:
            file_ext: The file extension including the dot (e.g., '.py')
            
        Returns:
            LanguageEnum or None if the extension is not supported
        """
        pass
    
    @abstractmethod
    def get_supported_languages(self) -> List[LanguageEnum]:
        """
        Get a list of programming languages supported by this parser.
        
        Returns:
            List of supported language enums
        """
        pass
    
    @abstractmethod
    def should_ignore_path(self, path: str) -> bool:
        """
        Determine if a file or directory should be ignored during parsing.
        
        Args:
            path: The path to check
            
        Returns:
            True if the path should be ignored, False otherwise
        """
        pass