from dataclasses import dataclass, field
from typing import List, Dict, Any, Set
from flow_rag.domain.ports.file_loader import LanguageEnum

@dataclass
class Reference:
    """A reference to a code element from another part of the codebase."""
    file: str
    line: int
    column: int
    text: str

    def __str__(self) -> str:
        return f"{self.file}:{self.line}:{self.column}"


@dataclass
class Method:
    """A code method or function representation."""
    name: str
    file_path: str
    source_code: str
    doc_comment: str = ""
    class_name: str = ""
    references: List[Reference] = field(default_factory=list)

    def __str__(self) -> str:
        return f"{self.class_name}.{self.name}" if self.class_name else self.name


@dataclass
class Class:
    """A code class representation."""
    name: str
    file_path: str
    source_code: str
    method_declarations: List[str] = field(default_factory=list)
    constructor_declaration: str = ""
    references: List[Reference] = field(default_factory=list)

    def __str__(self) -> str:
        return self.name


@dataclass
class SpecialFile:
    """A representation of non-code files like README.md or shell scripts."""
    file_path: str
    content: str
    file_type: str = "markdown"  # Default type

    @property
    def is_markdown(self) -> bool:
        return self.file_path.endswith(".md") or self.file_type == "markdown"

    @property
    def is_shell(self) -> bool:
        return self.file_path.endswith(".sh") or self.file_type == "shell"

    @property
    def is_json(self) -> bool:
        return self.file_path.endswith(".json") or self.file_type == "json"

    @property
    def is_text(self) -> bool:
        return self.file_path.endswith(".txt") or self.file_type == "text"


@dataclass
class CodebaseMetadata:
    """Metadata about the codebase being analyzed."""
    name: str
    path: str
    languages: Set[LanguageEnum] = field(default_factory=set)
    file_count: int = 0
    method_count: int = 0
    class_count: int = 0
    
    def add_language(self, language: LanguageEnum) -> None:
        self.languages.add(language)


@dataclass
class EmbeddedMethod(Method):
    """A Method with vector embeddings."""
    embeddings: List[float] = field(default_factory=list)
    vector_id: str = ""


@dataclass
class EmbeddedClass(Class):
    """A Class with vector embeddings."""
    embeddings: List[float] = field(default_factory=list)
    vector_id: str = ""


@dataclass
class EmbeddedSpecialFile(SpecialFile):
    """A SpecialFile with vector embeddings."""
    embeddings: List[float] = field(default_factory=list)
    vector_id: str = ""
    chunk_index: int = 0
    total_chunks: int = 1
    token_count: int = 0


@dataclass
class QueryResult:
    """Results of a query against the codebase."""
    query: str
    methods: List[Method] = field(default_factory=list)
    classes: List[Class] = field(default_factory=list)
    special_files: List[SpecialFile] = field(default_factory=list)
    context: str = ""
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    @property
    def has_results(self) -> bool:
        return bool(self.methods or self.classes or self.special_files)