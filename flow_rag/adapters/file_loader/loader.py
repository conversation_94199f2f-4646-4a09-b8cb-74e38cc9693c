"""
Implementation of the file loader that handles file loading and filtering.
"""
import os
import logging
from typing import List, Optional, Tuple

from flow_rag.domain.ports.file_loader import LanguageEnum
from .utils import get_file_language, load_files_generic


class FileLoader:
    """
    Handles loading and filtering files from a codebase.
    """
    
    def __init__(self, logger: Optional[logging.Logger] = None):
        """
        Initialize the file loader.
        
        Args:
            logger: Optional logger instance for logging messages
        """
        self.logger = logger or logging.getLogger(__name__)
    
    def load_files(self, codebase_path: str) -> List[Tuple[str, LanguageEnum]]:
        """
        Load and filter files from the specified codebase path.
        
        Args:
            codebase_path: Path to the root of the codebase
            
        Returns:
            List of tuples containing (file_path, language_enum) for each valid file
        """
        codebase_path = os.path.abspath(codebase_path)
        if not os.path.isdir(codebase_path):
            self.logger.error(f"[File Loader] Invalid codebase path: {codebase_path}")
            return []
        
        # Get all code files using the generic loader
        files = load_files_generic(
            codebase_path=codebase_path,
            mode='code',
            logger=self.logger
        )
        
        # Process files to detect languages
        result = []
        for file_path in files:
            language = get_file_language(file_path)
            if language is not None:
                result.append((file_path, language))
        
        return result
    
    def get_file_language(self, file_path: str) -> Optional[LanguageEnum]:
        """
        Determine the language of a file based on its extension.
        Uses the standardized get_file_language function.

        Args:
            file_path: Path to the file

        Returns:
            LanguageEnum if the file type is supported, None otherwise
        """
        return get_file_language(file_path)
