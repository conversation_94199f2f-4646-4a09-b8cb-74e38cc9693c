"""
File Loader Adapters

This module provides implementations of the FileLoaderPort for loading and filtering

Example:
    To use the file loader:
    ```python
    from flow_rag.adapters.file_loader import FileLoader, get_file_language
    
    # Create a file loader instance
    loader = FileLoader()
    
    # Load files from a codebase
    files = loader.load_files("/path/to/codebase")
    
    # Get language for a specific file
    language = get_file_language("example.py")
    """

from .loader import FileLoader
from .utils import get_file_language, get_file_language_string, detect_language_from_extension, load_files_generic

__all__ = [
    'FileLoader',
    'get_file_language',
    'get_file_language_string',
    'detect_language_from_extension',
    'load_files_generic'
]
