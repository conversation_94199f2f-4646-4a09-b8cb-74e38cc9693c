"""
Jina Embedding Provider

This module provides an implementation of the EmbeddingsPort using Jina AI's embedding models.
"""

from typing import List, Dict, Any, Optional
import logging
import requests
import time

from flow_rag.infrastructure.config import get_config
from .base_embedding_provider import BaseEmbeddingProvider
from flow_rag.infrastructure.log_utils import log_message
import traceback

class JinaEmbeddingProvider(BaseEmbeddingProvider):
    """
    Implementation of EmbeddingsPort using Jina AI embeddings.
    All batching, clipping, etc., are handled by BaseEmbeddingProvider.
    """
    def __init__(self, config: Optional[Dict[str, Any]] = None, logger: Optional[logging.Logger] = None, **kwargs):
        config = self._get_config_manager().get_embedding_config()

        self.api_key = config.get("api_key")
        if not self.api_key:
            raise ValueError("Jina API key is required. Please set the JINA_API_KEY environment variable.")

        model_name = config.get("model_name", "jina-embeddings-v3")
        embedding_dim = config.get("dimension", 1024)
        max_token_limit = config.get("max_tokens", 8192)

        self.base_url = 'https://api.jina.ai/v1/embeddings'
        self.session = None

        # Store these before calling super().__init__
        self._model_name = model_name
        self.embedding_dim = embedding_dim
        self._max_token_limit = max_token_limit
        self.logger = logger or logging.getLogger(__name__)

        # Call parent constructor with explicit parameters
        super().__init__(
            config=config,
            logger=self.logger,
            **kwargs
        )

        # Override max_tokens to ensure it's set correctly
        self.max_tokens = max_token_limit

        # Initialize provider-specific client
        self.session = self._setup_client()

    def _get_config_manager(self):
        """Get the configuration manager instance."""
        return get_config()
    
    def get_embedding_dimensions(self) -> int:
        return self.embedding_dim

    @property
    def max_token_limit(self) -> int:
        return self._max_token_limit

    @property
    def model_name(self) -> str:
        return self._model_name

    def _setup_client(self) -> requests.Session:
        """Initialize and configure the HTTP session for API requests."""
        session = requests.Session()
        session.headers.update({
            'Authorization': f'Bearer {self.api_key}',
            'Content-Type': 'application/json'
        })
        return session

    def _handle_error_response(self, response: requests.Response) -> None:
        """Extract and log error details from API response."""
        try:
            error_data = response.json()
            error_msg = error_data.get('detail', 'Unknown error')
            log_message(f"[JinaEmbeddingProvider] {error_msg}", 
                       logger=self.logger, level="error", to_terminal=True)
        except ValueError:
            log_message("[JinaEmbeddingProvider] Invalid request to Jina API", 
                       logger=self.logger, level="error", to_terminal=True)

    def _get_embeddings(self, texts: List[str]) -> List[Optional[List[float]]]:
        """
        Get embeddings for a list of texts using the Jina AI API.
        
        Args:
            texts: List of text strings to embed
            
        Returns:
            List of embedding vectors or None values for failed embeddings
        """
        if not self.api_key:
            raise ValueError("Jina API key is required")

        if not texts:
            return []

        # Validate input texts individually and clip to max tokens
        valid_indices = []
        valid_texts = []
        result = [None] * len(texts)
        for i, text in enumerate(texts):
            if not isinstance(text, str) or not text.strip():
                log_message(f"Text at index {i} is empty or not a string", logger=self.logger, level="error")
            else:
                # Clip text to max tokens as safety measure
                clipped_text = self.clip_text_to_max_tokens(text)
                valid_indices.append(i)
                valid_texts.append(clipped_text)

        if not valid_texts:
            return result

        payload = {"model": self._model_name, "input": valid_texts}
        for attempt in range(3):
            try:
                # Small delay to avoid rate limits (except on first call)
                if attempt > 0:
                    time.sleep(0.5)

                response = self.session.post(self.base_url, json=payload, timeout=30)
                if response.status_code == 400:
                    self._handle_error_response(response)
                
                response.raise_for_status()
                
                data = response.json()
                embeddings = [item["embedding"] for item in data.get("data", [])]

                if len(embeddings) != len(valid_texts):
                    log_message(
                        f"[JinaEmbeddingProvider] Mismatch in number of embeddings returned. Expected {len(valid_texts)}, got {len(embeddings)}", 
                        logger=self.logger, level="warning", to_terminal=True
                    )
                    return [None] * len(texts)

                # Map embeddings back to original positions
                for idx, orig_idx in enumerate(valid_indices):
                    result[orig_idx] = embeddings[idx]
                return result
                
            except requests.exceptions.HTTPError as http_err:
                status_code = http_err.response.status_code

                # Retry on server errors (5xx) and rate limiting (429)
                if attempt < 2 and (status_code >= 500 or status_code == 429):
                    if status_code == 429:
                        # Rate limit - use longer backoff
                        wait_time = (2 ** attempt) * 5  # 5s, 10s, 20s
                        log_message(
                            f"[JinaEmbeddingProvider] Rate limit exceeded (HTTP 429). Retrying in {wait_time}s (attempt {attempt + 1}/3)",
                            logger=self.logger, level="warning", to_terminal=True
                        )
                    else:
                        # Server error - use normal backoff
                        wait_time = 2 ** attempt
                        log_message(
                            f"[JinaEmbeddingProvider] Server error. Retrying in {wait_time}s. Status: {status_code}",
                            logger=self.logger, level="error", to_terminal=True
                        )
                    time.sleep(wait_time)
                else:
                    # Final attempt failed or non-retryable error
                    if status_code == 400:
                        self._handle_error_response(http_err.response)
                    elif status_code == 429:
                        log_message(
                            "[JinaEmbeddingProvider] Rate limit exceeded after 3 attempts. Please wait before making more requests.",
                            logger=self.logger, level="error", to_terminal=True
                        )
                    else:
                        log_message(
                            f"[JinaEmbeddingProvider] API communication failed (HTTP {status_code})",
                            logger=self.logger, level="error", to_terminal=True
                        )
                    return [None] * len(texts)
                
            except Exception as e:
                # Log the exception details
                log_message(f"[JinaEmbeddingProvider] Exception: {str(e)}", logger=self.logger, level="error", to_terminal=True)
                log_message(traceback.format_exc(), logger=self.logger, level="error")
                # Retry on other exceptions
                if attempt < 2:
                    wait_time = 2 ** attempt
                    log_message(
                        f"[JinaEmbeddingProvider] Unexpected error. Retrying in {wait_time}s. Error: {str(e)}", 
                        logger=self.logger, level="error", to_terminal=True
                    )
                    time.sleep(wait_time)
                else:
                    log_message(
                        f"[JinaEmbeddingProvider] Failed after 3 attempts: {str(e)}", 
                        logger=self.logger, level="error", to_terminal=True
                    )
                    return [None] * len(texts)
                    
        return [None] * len(texts)  # Fallback return