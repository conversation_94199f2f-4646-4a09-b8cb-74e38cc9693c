"""
Embedding Providers Module

This module provides implementations of the EmbeddingsPort interface for different
embedding providers like OpenAI and Jina AI. It includes a factory for creating
the appropriate provider based on configuration and a base class for implementing
new embedding providers.
"""

from typing import Optional
import logging

from ...domain.ports.embeddings import EmbeddingsPort
from .factory import EmbeddingProviderFactory
from .base_embedding_provider import BaseEmbeddingProvider
from .jina_embeddings import JinaEmbeddingProvider
from .openai_embeddings import OpenAIEmbeddingProvider

__all__ = [
    "JinaEmbeddingProvider",
    "OpenAIEmbeddingProvider",
    "EmbeddingProviderFactory",
    "create_embedding_provider",
    "BaseEmbeddingProvider",
]


def create_embedding_provider(
    logger: Optional[logging.Logger] = None
) -> "EmbeddingsPort":
    """
    Cria um provider de embeddings usando apenas as configurações centralizadas.

    Wrapper para EmbeddingProviderFactory.create_provider().
    
    Args:
        logger: Logger opcional.
    Returns:
        Instância do provider solicitado.
    """
    return EmbeddingProviderFactory.create_provider(logger=logger)