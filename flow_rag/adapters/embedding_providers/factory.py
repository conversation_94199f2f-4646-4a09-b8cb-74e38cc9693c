"""
Factory module for creating embedding providers.

This module provides a factory class for creating different types of embedding providers.
"""

from typing import Dict, Type, Optional
import logging

from ...domain.ports.embeddings import EmbeddingsPort
from .jina_embeddings import JinaEmbeddingProvider
from .openai_embeddings import OpenAIEmbeddingProvider
from ...infrastructure.config import get_config
from ...infrastructure.log_utils import log_message

class EmbeddingProviderFactory:
    """Factory for creating embedding providers."""
    
    # Registry of available providers: maps provider names to their implementation classes
    _providers: Dict[str, Type[EmbeddingsPort]] = {
        "jina": JinaEmbeddingProvider,
        "openai": OpenAIEmbeddingProvider,
    }

    _config_manager = get_config()

    @classmethod
    def _get_embedding_config(cls):
        return cls._config_manager.get_embedding_config() or {}
    
    @classmethod
    def register_provider(cls, name: str, provider_class: Type[EmbeddingsPort]) -> None:
        """Register a new embedding provider type.
        
        Args:
            name: Name to register the provider under
            provider_class: Provider class to register
        """
        cls._providers[name.lower()] = provider_class
    
    @classmethod
    def create_provider(
        cls,
        logger: Optional[logging.Logger] = None
    ) -> EmbeddingsPort:
        """
        Cria um provider de embeddings usando apenas as configurações centralizadas.
        Todas as configurações são lidas do arquivo/config manager.
        Args:
            logger: Logger opcional.
        Returns:
            Instância do provider solicitado.
        """
        embedding_config = cls._get_embedding_config()
        provider_type = embedding_config.get('provider')
        if not provider_type:
            raise ValueError("Please, set a provider type in config > embedding > provider")
        provider_class = cls._providers[provider_type]
        log_message(f"# Provider selected: {provider_type}", logger=logger)
        return provider_class(config=embedding_config, logger=logger)
    
    @classmethod
    def _get_default_model(cls, provider_type: str) -> str:
        """
        Obtém o nome do modelo do provider a partir do config centralizado.
        """
        model_name = cls._get_embedding_config().get('model_name')
        if not model_name:
            raise ValueError("Please, set a model name in config > embedding > model_name")
        return model_name

    @classmethod
    def get_available_providers(cls) -> list[str]:
        """Get a list of available embedding providers."""
        return list(cls._providers.keys())
    
    @classmethod
    def get_provider_config(cls, provider_type: str):
        """Get configuration for a specific provider."""
        if provider_type not in cls._providers:
            raise ValueError(f"Unknown provider: {provider_type}")
        return cls._providers[provider_type]