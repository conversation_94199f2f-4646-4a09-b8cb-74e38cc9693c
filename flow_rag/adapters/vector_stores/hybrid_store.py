"""
Hybrid Store Implementation - BM25 + Selective Embeddings

This module implements a hybrid search approach that combines:
1. Fast BM25 search using SQLite FTS5 for initial retrieval
2. Selective embedding generation only for top-K results
3. Semantic reranking for final results

Performance targets:
- Indexing: <1 minute for 244 VB6 files (vs 4+ hours)
- Search: <300ms per query (vs seconds)
- Embeddings: 95% reduction in generation

Thread Safety:
- All SQLite database operations are protected with threading.RLock()
- Prevents "Segmentation fault: 11" errors from concurrent access
- Safe for use with Flask web servers and ThreadPoolExecutor
"""

import sqlite3
import json
import hashlib
import pickle
import time
import threading
from typing import List, Dict, Any, Optional
from dataclasses import dataclass
import logging
from pathlib import Path

from ...domain.ports.vector_store import VectorStorePort
from ...infrastructure.log_utils import log_message
from ...adapters.file_loader import get_file_language_string


@dataclass
class SearchResult:
    """Result from BM25 or hybrid search."""
    content: str
    file_path: str
    entity_type: str  # 'method', 'class', 'file'
    entity_name: str
    language: str
    metadata: Dict[str, Any]
    score: float
    rank: int = 0


@dataclass
class ProcessedQuery:
    """Processed and expanded query for better search."""
    original: str
    expanded_terms: List[str]
    query_type: str  # 'method', 'class', 'general'
    language_filter: Optional[str] = None


class BM25Searcher:
    """
    BM25 search engine using SQLite FTS5.
    Optimized for code search with camelCase expansion and type boosting.
    Thread-safe with database access protection.
    """

    def __init__(self, db_connection: sqlite3.Connection, logger: Optional[logging.Logger] = None):
        self.db = db_connection
        self.logger = logger or logging.getLogger(__name__)
        self._db_lock = threading.RLock()  # Reentrant lock for thread safety
        self._setup_fts_tables()

    def _setup_fts_tables(self):
        """Create FTS5 tables and indexes for code search."""
        with self._db_lock:
            try:
                # Main FTS5 table for code search
                self.db.execute("""
                    CREATE VIRTUAL TABLE IF NOT EXISTS code_search USING fts5(
                        content,           -- Full text content (code + comments)
                        file_path,         -- File path
                        entity_type,       -- 'method', 'class', 'file'
                        entity_name,       -- Name of function/class
                        language,          -- Programming language
                        metadata,          -- JSON metadata
                        tokenize='porter'  -- Porter stemming for better search
                    )
                """)

                # Embedding cache table
                self.db.execute("""
                    CREATE TABLE IF NOT EXISTS embedding_cache (
                        content_hash TEXT PRIMARY KEY,
                        embedding BLOB,           -- Serialized vector
                        model_name TEXT,          -- Model used (jina-v3, etc.)
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        access_count INTEGER DEFAULT 1
                    )
                """)

                # Performance indexes
                self.db.execute("CREATE INDEX IF NOT EXISTS idx_embedding_cache_model ON embedding_cache(model_name)")

                self.db.commit()
                log_message("[BM25Searcher] FTS5 tables and indexes created successfully", logger=self.logger)

            except Exception as e:
                log_message(f"[BM25Searcher] Error setting up FTS tables: {str(e)}",
                           level="error", logger=self.logger)
                raise
    
    def add_document(self, content: str, file_path: str, entity_type: str,
                    entity_name: str, language: str, metadata: Dict[str, Any]) -> bool:
        """Add a document to the FTS5 index."""
        with self._db_lock:
            try:
                self.db.execute("""
                    INSERT INTO code_search (content, file_path, entity_type, entity_name, language, metadata)
                    VALUES (?, ?, ?, ?, ?, ?)
                """, (content, file_path, entity_type, entity_name, language, json.dumps(metadata)))

                return True
            except Exception as e:
                log_message(f"[BM25Searcher] Error adding document: {str(e)}",
                           level="error", logger=self.logger)
                return False
    
    def bulk_insert(self, documents: List[Dict[str, Any]]) -> bool:
        """Bulk insert documents for better performance."""
        with self._db_lock:
            try:
                cursor = self.db.cursor()
                cursor.executemany("""
                    INSERT INTO code_search (content, file_path, entity_type, entity_name, language, metadata)
                    VALUES (?, ?, ?, ?, ?, ?)
                """, [
                    (doc['content'], doc['file_path'], doc['entity_type'],
                     doc['entity_name'], doc['language'], json.dumps(doc['metadata']))
                    for doc in documents
                ])

                self.db.commit()
                log_message(f"[BM25Searcher] Bulk inserted {len(documents)} documents", logger=self.logger)
                return True

            except Exception as e:
                log_message(f"[BM25Searcher] Error in bulk insert: {str(e)}",
                           level="error", logger=self.logger)
                return False
    
    def search(self, query: str, limit: int = 50, entity_type_filter: Optional[str] = None,
               language_filter: Optional[str] = None) -> List[SearchResult]:
        """
        Perform BM25 search with optional filters.

        Args:
            query: Search query
            limit: Maximum number of results
            entity_type_filter: Filter by entity type ('method', 'class', 'file')
            language_filter: Filter by programming language

        Returns:
            List of search results ordered by BM25 score
        """
        with self._db_lock:
            try:
                # Build FTS5 query
                fts_query = self._build_fts_query(query)

                # Build WHERE clause for filters
                where_conditions = []
                params = [fts_query]

                if entity_type_filter:
                    where_conditions.append("entity_type = ?")
                    params.append(entity_type_filter)

                if language_filter:
                    where_conditions.append("language = ?")
                    params.append(language_filter)

                where_clause = ""
                if where_conditions:
                    where_clause = " AND " + " AND ".join(where_conditions)

                # Execute search
                sql = f"""
                    SELECT content, file_path, entity_type, entity_name, language, metadata,
                           rank as score
                    FROM code_search
                    WHERE code_search MATCH ?{where_clause}
                    ORDER BY rank
                    LIMIT ?
                """
                params.append(limit)

                cursor = self.db.execute(sql, params)
                results = []

                for rank, row in enumerate(cursor.fetchall(), 1):
                    try:
                        metadata = json.loads(row[5]) if row[5] else {}
                    except json.JSONDecodeError:
                        metadata = {}

                    # Use rank-based scoring (higher rank = better match)
                    score = 1.0 / rank  # Convert rank to score (1.0, 0.5, 0.33, etc.)

                    result = SearchResult(
                        content=row[0],
                        file_path=row[1],
                        entity_type=row[2],
                        entity_name=row[3],
                        language=row[4],
                        metadata=metadata,
                        score=score
                    )
                    results.append(result)

                log_message(f"[BM25Searcher] Found {len(results)} results for query: {query}",
                           logger=self.logger)
                return results

            except Exception as e:
                log_message(f"[BM25Searcher] Error in search: {str(e)}",
                           level="error", logger=self.logger)
                return []
    
    def _build_fts_query(self, query: str) -> str:
        """
        Build FTS5 query with term expansion for code search.

        Expands camelCase and handles code-specific patterns:
        - getUserName → "getUserName" OR "get" OR "user" OR "name"
        - tela_login → "tela_login" OR "tela" OR "login"
        """
        # Simple escape for problematic characters
        import re
        query = re.sub(r'[^\w\s\-\.]', '', query.strip())

        if not query:
            return '""'

        terms = []

        # Split query into words
        words = query.split()

        for word in words:
            # Check if this looks like a method/function name
            if self._looks_like_method_name(word):
                # For method names, search in entity_name with higher priority
                terms.append(f'entity_name:"{word}"^3')  # Boost entity_name matches
                terms.append(f'"{word}"')  # Also search in content
            else:
                # Add original word
                terms.append(f'"{word}"')

            # Expand camelCase: getUserName → get, User, Name
            camel_parts = self._split_camel_case(word)
            if len(camel_parts) > 1:
                for part in camel_parts:
                    if len(part) > 2:  # Skip very short parts
                        if self._looks_like_method_name(word):
                            terms.append(f'entity_name:"{part}"^2')  # Boost for method parts
                        terms.append(f'"{part}"')

            # Expand snake_case: tela_login → tela, login
            snake_parts = word.split('_')
            if len(snake_parts) > 1:
                for part in snake_parts:
                    if len(part) > 2:
                        if self._looks_like_method_name(word):
                            terms.append(f'entity_name:"{part}"^2')
                        terms.append(f'"{part}"')

        # Join with OR for broader search
        return " OR ".join(terms) if terms else '""'

    def _looks_like_method_name(self, word: str) -> bool:
        """Check if a word looks like a method/function name."""
        # Skip very short words or common words
        if len(word) < 4 or word.lower() in ['data', 'hello', 'test', 'info', 'name', 'file', 'path']:
            return False

        # Check for camelCase pattern (mixed case with uppercase after lowercase)
        has_camel_case = any(c.isupper() for c in word[1:]) and any(c.islower() for c in word)

        # Check for common method prefixes
        method_prefixes = ['get', 'set', 'is', 'has', 'can', 'should', 'sub', 'function', 'proc']
        has_method_prefix = any(word.lower().startswith(prefix) for prefix in method_prefixes)

        # Check for method-like patterns
        has_method_pattern = (
            len(word) > 6 and  # Longer words more likely to be methods
            word[0].islower() and  # Starts with lowercase (typical for methods)
            ' ' not in word and  # Single word
            any(c.isupper() for c in word)  # Has some uppercase
        )

        return has_camel_case or has_method_prefix or has_method_pattern
    
    def _split_camel_case(self, word: str) -> List[str]:
        """Split camelCase words into parts."""
        import re
        # Insert space before uppercase letters
        spaced = re.sub(r'([a-z])([A-Z])', r'\1 \2', word)
        return [part.strip() for part in spaced.split() if part.strip()]

    def optimize_indexes(self):
        """Optimize FTS5 indexes for better performance."""
        with self._db_lock:
            try:
                self.db.execute("INSERT INTO code_search(code_search) VALUES('optimize')")
                self.db.commit()
                log_message("[BM25Searcher] FTS5 indexes optimized", logger=self.logger)
            except Exception as e:
                log_message(f"[BM25Searcher] Error optimizing indexes: {str(e)}",
                           level="warning", logger=self.logger)


class QueryProcessor:
    """
    Process and expand queries for better code search.
    Identifies query intent and expands terms appropriately.
    """
    
    def __init__(self, logger: Optional[logging.Logger] = None):
        self.logger = logger or logging.getLogger(__name__)
        
        # Common patterns for query classification
        self.method_patterns = ['função', 'function', 'método', 'method', 'sub', 'procedure']
        self.class_patterns = ['classe', 'class', 'form', 'formulário', 'tela', 'screen']
        
    def process_query(self, query: str) -> ProcessedQuery:
        """
        Process query and determine search strategy.
        
        Args:
            query: Original user query
            
        Returns:
            ProcessedQuery with expanded terms and metadata
        """
        query_lower = query.lower().strip()
        
        # Determine query type
        query_type = self._classify_query(query_lower)
        
        # Expand terms
        expanded_terms = self._expand_query_terms(query)

        return ProcessedQuery(
            original=query,
            expanded_terms=expanded_terms,
            query_type=query_type,
            language_filter=None
        )
    
    def _classify_query(self, query: str) -> str:
        """Classify query intent: method, class, or general."""
        if any(pattern in query for pattern in self.method_patterns):
            return 'method'
        elif any(pattern in query for pattern in self.class_patterns):
            return 'class'
        else:
            return 'general'
    
    def _expand_query_terms(self, query: str) -> List[str]:
        """Expand query terms for better matching."""
        terms = [query]  # Always include original
        
        # Add individual words
        words = query.split()
        terms.extend(words)
        
        # Add camelCase expansions
        for word in words:
            camel_parts = self._split_camel_case(word)
            terms.extend(camel_parts)
        
        # Remove duplicates and short terms
        unique_terms = list(set(term for term in terms if len(term) > 2))
        return unique_terms
    
    def _split_camel_case(self, word: str) -> List[str]:
        """Split camelCase words into parts."""
        import re
        spaced = re.sub(r'([a-z])([A-Z])', r'\1 \2', word)
        return [part.strip() for part in spaced.split() if part.strip()]
    



class EmbeddingCache:
    """
    Intelligent cache for embeddings with lazy loading.
    Only generates embeddings when needed for semantic reranking.
    Thread-safe with database access protection.
    """

    def __init__(self, db_connection: sqlite3.Connection, logger: Optional[logging.Logger] = None):
        self.db = db_connection
        self.logger = logger or logging.getLogger(__name__)
        self.provider = None  # Lazy loading
        self._model_name = None
        self._db_lock = threading.RLock()  # Reentrant lock for thread safety

    def get_or_generate_embedding(self, content: str) -> Optional[List[float]]:
        """
        Get embedding from cache or generate if not exists.

        Args:
            content: Text content to embed

        Returns:
            Embedding vector or None if generation fails
        """
        content_hash = hashlib.sha256(content.encode('utf-8')).hexdigest()

        # 1. Try cache first
        cached = self._get_from_cache(content_hash)
        if cached:
            self._update_access_count(content_hash)
            return cached

        # 2. Generate embedding if not exists (with simple timeout)
        try:
            if not self.provider:
                self._initialize_provider()

            # Simple timeout using threading (works in all contexts)
            import threading

            result = [None]
            exception = [None]

            def generate_embedding():
                try:
                    # Clip content before generating embedding
                    clipped_content = content
                    if hasattr(self.provider, 'clip_text_to_max_tokens'):
                        clipped_content = self.provider.clip_text_to_max_tokens(content)
                    result[0] = self.provider.embed_query(clipped_content)
                except Exception as e:
                    exception[0] = e

            # Start embedding generation in thread with timeout
            thread = threading.Thread(target=generate_embedding)
            thread.daemon = True
            thread.start()
            thread.join(timeout=5)  # 5 second timeout

            if thread.is_alive():
                log_message("[EmbeddingCache] Embedding generation timed out",
                           level="warning", logger=self.logger)
                return None

            if exception[0]:
                raise exception[0]

            embedding = result[0]
            if embedding:
                # 3. Save to cache
                self._save_to_cache(content_hash, embedding)
                return embedding

        except Exception as e:
            log_message(f"[EmbeddingCache] Error generating embedding: {str(e)}",
                       level="warning", logger=self.logger)

        return None

    def batch_get_or_generate(self, contents: List[str]) -> List[Optional[List[float]]]:
        """
        Batch process multiple contents for better performance.

        Args:
            contents: List of text contents to embed

        Returns:
            List of embedding vectors (None for failed generations)
        """
        results = []
        to_generate = []
        to_generate_indices = []

        # 1. Check cache for all contents
        for i, content in enumerate(contents):
            content_hash = hashlib.sha256(content.encode('utf-8')).hexdigest()
            cached = self._get_from_cache(content_hash)

            if cached:
                self._update_access_count(content_hash)
                results.append(cached)
            else:
                results.append(None)
                to_generate.append(content)
                to_generate_indices.append(i)

        # 2. Generate missing embeddings in batch (with clipping)
        if to_generate:
            try:
                if not self.provider:
                    self._initialize_provider()

                # Clip all content before generating embeddings
                clipped_to_generate = []
                for content in to_generate:
                    if hasattr(self.provider, 'clip_text_to_max_tokens'):
                        clipped_content = self.provider.clip_text_to_max_tokens(content)
                        clipped_to_generate.append(clipped_content)
                    else:
                        # Fallback: simple character-based truncation
                        max_chars = 8192 * 3  # Conservative estimate
                        clipped_to_generate.append(content[:max_chars])

                # Use batch embedding if available
                if hasattr(self.provider, '_get_embeddings'):
                    generated = self.provider._get_embeddings(clipped_to_generate)
                else:
                    # Fallback to individual generation
                    generated = [self.provider.embed_query(content) for content in clipped_to_generate]

                # 3. Save to cache and update results
                for i, embedding in enumerate(generated):
                    if embedding:
                        # Use original content for hash, but clipped content was used for embedding
                        content_hash = hashlib.sha256(to_generate[i].encode('utf-8')).hexdigest()
                        self._save_to_cache(content_hash, embedding)
                        results[to_generate_indices[i]] = embedding

            except Exception as e:
                log_message(f"[EmbeddingCache] Error in batch generation: {str(e)}",
                           level="warning", logger=self.logger)

        return results

    def _initialize_provider(self):
        """Lazy initialization of embedding provider."""
        try:
            from ...adapters.embedding_providers.factory import EmbeddingProviderFactory
            self.provider = EmbeddingProviderFactory.create_provider()

            # Get model name for cache key
            if hasattr(self.provider, '_model_name'):
                self._model_name = self.provider._model_name
            else:
                self._model_name = 'unknown'

            log_message(f"[EmbeddingCache] Initialized embedding provider: {self._model_name}",
                       logger=self.logger)

        except Exception as e:
            log_message(f"[EmbeddingCache] Error initializing provider: {str(e)}",
                       level="error", logger=self.logger)
            raise

    def _get_from_cache(self, content_hash: str) -> Optional[List[float]]:
        """Get embedding from cache."""
        with self._db_lock:
            try:
                cursor = self.db.execute("""
                    SELECT embedding FROM embedding_cache
                    WHERE content_hash = ? AND model_name = ?
                """, (content_hash, self._model_name or 'unknown'))

                row = cursor.fetchone()
                if row and row[0] is not None:
                    return pickle.loads(row[0])

            except Exception as e:
                log_message(f"[EmbeddingCache] Error reading from cache: {str(e)}",
                           level="warning", logger=self.logger)

            return None

    def _save_to_cache(self, content_hash: str, embedding: List[float]):
        """Save embedding to cache."""
        with self._db_lock:
            try:
                self.db.execute("""
                    INSERT OR REPLACE INTO embedding_cache
                    (content_hash, embedding, model_name, created_at, access_count)
                    VALUES (?, ?, ?, datetime('now'), 1)
                """, (content_hash, pickle.dumps(embedding), self._model_name or 'unknown'))

                self.db.commit()

            except Exception as e:
                log_message(f"[EmbeddingCache] Error saving to cache: {str(e)}",
                           level="warning", logger=self.logger)

    def _update_access_count(self, content_hash: str):
        """Update access count for cache entry."""
        with self._db_lock:
            try:
                self.db.execute("""
                    UPDATE embedding_cache
                    SET access_count = access_count + 1
                    WHERE content_hash = ?
                """, (content_hash,))

            except Exception as e:
                log_message(f"[EmbeddingCache] Error updating access count: {str(e)}",
                           level="warning", logger=self.logger)

    def cleanup_old_embeddings(self, max_age_days: int = 30, min_access_count: int = 2):
        """Clean up old and rarely used embeddings."""
        with self._db_lock:
            try:
                cursor = self.db.execute("""
                    DELETE FROM embedding_cache
                    WHERE created_at < datetime('now', '-{} days')
                    AND access_count < ?
                """.format(max_age_days), (min_access_count,))

                deleted_count = cursor.rowcount
                self.db.commit()

                log_message(f"[EmbeddingCache] Cleaned up {deleted_count} old embeddings",
                           logger=self.logger)

            except Exception as e:
                log_message(f"[EmbeddingCache] Error cleaning up cache: {str(e)}",
                           level="warning", logger=self.logger)


class SemanticReranker:
    """
    Semantic reranker that combines BM25 scores with embedding similarity.
    Uses cosine similarity for semantic scoring.
    """

    def __init__(self, embedding_cache: EmbeddingCache, logger: Optional[logging.Logger] = None):
        self.cache = embedding_cache
        self.logger = logger or logging.getLogger(__name__)
        # Cache for chunked embeddings to avoid recomputation
        self._chunked_embedding_cache = {}

    def rerank(self, query: str, bm25_results: List[SearchResult],
               alpha: float = 0.7, max_rerank: int = 20) -> List[SearchResult]:
        """
        Rerank BM25 results using semantic similarity.

        Args:
            query: Original search query
            bm25_results: Results from BM25 search
            alpha: Weight for BM25 score (1-alpha for semantic score)
            max_rerank: Maximum number of results to rerank

        Returns:
            Reranked results with combined scores
        """
        if not bm25_results:
            return []

        # Only rerank top results for performance
        to_rerank = bm25_results[:max_rerank]
        remaining = bm25_results[max_rerank:]

        try:
            # 1. Generate query embedding
            query_embedding = self.cache.get_or_generate_embedding(query)
            if not query_embedding:
                log_message("[SemanticReranker] Failed to generate query embedding, returning BM25 results",
                           level="warning", logger=self.logger)
                return bm25_results

            # 2. Generate embeddings for top results (with intelligent chunking)
            result_embeddings = []
            for result in to_rerank:
                # Get or generate embedding with chunking support
                embedding = self._get_content_embedding_with_chunking(result.content)
                result_embeddings.append(embedding)

            # 3. Calculate semantic scores
            semantic_scores = []
            for embedding in result_embeddings:
                if embedding:
                    score = self._cosine_similarity(query_embedding, embedding)
                    semantic_scores.append(score)
                else:
                    semantic_scores.append(0.0)  # Fallback for failed embeddings

            # 4. Combine scores and rerank
            combined_results = []
            for i, result in enumerate(to_rerank):
                bm25_score = result.score
                semantic_score = semantic_scores[i]

                # Normalize scores to 0-1 range
                normalized_bm25 = self._normalize_score(bm25_score, [r.score for r in to_rerank])
                normalized_semantic = semantic_score  # Cosine similarity is already 0-1

                # Combine scores
                final_score = alpha * normalized_bm25 + (1 - alpha) * normalized_semantic

                # Create new result with combined score
                reranked_result = SearchResult(
                    content=result.content,
                    file_path=result.file_path,
                    entity_type=result.entity_type,
                    entity_name=result.entity_name,
                    language=result.language,
                    metadata=result.metadata,
                    score=final_score
                )
                combined_results.append(reranked_result)

            # 5. Sort by final score
            combined_results.sort(key=lambda x: x.score, reverse=True)

            # 6. Add remaining results (not reranked)
            final_results = combined_results + remaining

            log_message(f"[SemanticReranker] Reranked {len(to_rerank)} results using semantic similarity",
                       logger=self.logger)

            return final_results

        except Exception as e:
            log_message(f"[SemanticReranker] Error in reranking: {str(e)}",
                       level="warning", logger=self.logger)
            return bm25_results  # Fallback to original results

    def _cosine_similarity(self, vec1: List[float], vec2: List[float]) -> float:
        """Calculate cosine similarity between two vectors."""
        try:
            import numpy as np

            # Convert to numpy arrays
            a = np.array(vec1)
            b = np.array(vec2)

            # Calculate cosine similarity
            dot_product = np.dot(a, b)
            norm_a = np.linalg.norm(a)
            norm_b = np.linalg.norm(b)

            if norm_a == 0 or norm_b == 0:
                return 0.0

            similarity = dot_product / (norm_a * norm_b)

            # Ensure result is in [0, 1] range
            return max(0.0, min(1.0, similarity))

        except Exception as e:
            log_message(f"[SemanticReranker] Error calculating cosine similarity: {str(e)}",
                       level="warning", logger=self.logger)
            return 0.0

    def _normalize_score(self, score: float, all_scores: List[float]) -> float:
        """Normalize score to 0-1 range based on min/max of all scores."""
        if not all_scores:
            return 0.0

        min_score = min(all_scores)
        max_score = max(all_scores)

        if max_score == min_score:
            return 1.0

        return (score - min_score) / (max_score - min_score)

    def _get_content_embedding_with_chunking(self, content: str) -> Optional[List[float]]:
        """
        Get embedding for content with intelligent chunking for large texts.

        For small content: generates single embedding
        For large content: chunks it, generates multiple embeddings, and combines them

        Args:
            content: Text content to embed

        Returns:
            Combined embedding vector or None if failed
        """
        if not content or not content.strip():
            return None

        # Check cache first for chunked embeddings
        content_hash = hashlib.sha256(content.encode('utf-8')).hexdigest()
        if content_hash in self._chunked_embedding_cache:
            return self._chunked_embedding_cache[content_hash]

        try:
            # Check if content fits within token limit
            if hasattr(self.cache, 'provider') and self.cache.provider:
                provider = self.cache.provider
                tokens = provider.tokenizer.encode(content)
                max_tokens = getattr(provider, 'max_tokens', 8192)

                # If content fits, use simple embedding
                if len(tokens) <= max_tokens:
                    return self.cache.get_or_generate_embedding(content)

                # Skip extremely large content to avoid performance issues
                if len(tokens) > 100000:  # 100K tokens is too much
                    log_message(f"[SemanticReranker] Skipping extremely large content ({len(tokens)} tokens)",
                               level="warning", logger=self.logger)
                    # Use simple clipping for very large content
                    clipped = provider.clip_text_to_max_tokens(content)
                    return self.cache.get_or_generate_embedding(clipped)

                # Content is too large - use chunking
                log_message(f"[SemanticReranker] Content too large ({len(tokens)} tokens), using chunking",
                           level="debug", logger=self.logger)  # Reduce log noise

                # Split into chunks
                chunks = self._split_content_into_chunks(content, max_tokens - 100)  # Leave margin

                if not chunks:
                    # Fallback to simple clipping
                    clipped = provider.clip_text_to_max_tokens(content)
                    return self.cache.get_or_generate_embedding(clipped)

                # Generate embeddings for each chunk
                chunk_embeddings = []
                for chunk in chunks:
                    chunk_embedding = self.cache.get_or_generate_embedding(chunk)
                    if chunk_embedding:
                        chunk_embeddings.append(chunk_embedding)

                if not chunk_embeddings:
                    return None

                # Combine embeddings using average (simple but effective)
                combined_embedding = self._combine_embeddings(chunk_embeddings)

                # Cache the result to avoid recomputation
                if combined_embedding and len(self._chunked_embedding_cache) < 100:  # Limit cache size
                    self._chunked_embedding_cache[content_hash] = combined_embedding

                return combined_embedding

            else:
                # Fallback if no provider available
                return self.cache.get_or_generate_embedding(content[:24000])  # Conservative limit

        except Exception as e:
            log_message(f"[SemanticReranker] Error in chunked embedding: {str(e)}",
                       level="warning", logger=self.logger)
            return None

    def _split_content_into_chunks(self, content: str, max_tokens: int) -> List[str]:
        """
        Split content into chunks that fit within token limits.

        Uses intelligent splitting that tries to preserve semantic boundaries.
        """
        try:
            # Try to split by logical boundaries first
            chunks = []

            # Split by double newlines (paragraphs/sections)
            sections = content.split('\n\n')
            current_chunk = ""

            for section in sections:
                # Check if adding this section would exceed limit
                test_chunk = current_chunk + "\n\n" + section if current_chunk else section

                if hasattr(self.cache, 'provider') and self.cache.provider:
                    provider = self.cache.provider
                    test_tokens = provider.tokenizer.encode(test_chunk)

                    if len(test_tokens) <= max_tokens:
                        current_chunk = test_chunk
                    else:
                        # Save current chunk and start new one
                        if current_chunk:
                            chunks.append(current_chunk)
                        current_chunk = section

                        # If single section is too large, split it further
                        if len(provider.tokenizer.encode(section)) > max_tokens:
                            # Split by single newlines
                            lines = section.split('\n')
                            line_chunk = ""
                            for line in lines:
                                test_line_chunk = line_chunk + "\n" + line if line_chunk else line
                                if len(provider.tokenizer.encode(test_line_chunk)) <= max_tokens:
                                    line_chunk = test_line_chunk
                                else:
                                    if line_chunk:
                                        chunks.append(line_chunk)
                                    line_chunk = line

                            if line_chunk:
                                current_chunk = line_chunk
                            else:
                                current_chunk = ""

            # Add final chunk
            if current_chunk:
                chunks.append(current_chunk)

            return chunks

        except Exception as e:
            log_message(f"[SemanticReranker] Error in content splitting: {str(e)}",
                       level="warning", logger=self.logger)
            # Fallback to simple character-based splitting
            chunk_size = max_tokens * 3  # Conservative character estimate
            return [content[i:i+chunk_size] for i in range(0, len(content), chunk_size)]

    def _combine_embeddings(self, embeddings: List[List[float]]) -> List[float]:
        """
        Combine multiple embeddings into a single representative embedding.

        Uses weighted average where first chunks get slightly higher weight
        (since important info is often at the beginning).
        """
        if not embeddings:
            return None

        if len(embeddings) == 1:
            return embeddings[0]

        try:
            import numpy as np

            # Convert to numpy arrays
            embedding_arrays = [np.array(emb) for emb in embeddings]

            # Create weights (first chunks get slightly higher weight)
            weights = []
            for i in range(len(embeddings)):
                # Exponential decay: first chunk = 1.0, second = 0.9, third = 0.81, etc.
                weight = 0.9 ** i
                weights.append(weight)

            # Normalize weights
            total_weight = sum(weights)
            weights = [w / total_weight for w in weights]

            # Weighted average
            combined = np.zeros_like(embedding_arrays[0])
            for emb, weight in zip(embedding_arrays, weights):
                combined += emb * weight

            # Normalize the result
            norm = np.linalg.norm(combined)
            if norm > 0:
                combined = combined / norm

            return combined.tolist()

        except ImportError:
            # Fallback without numpy - simple average
            if not embeddings:
                return None

            dim = len(embeddings[0])
            combined = [0.0] * dim

            for embedding in embeddings:
                for i in range(dim):
                    combined[i] += embedding[i]

            # Average
            for i in range(dim):
                combined[i] /= len(embeddings)

            return combined

        except Exception as e:
            log_message(f"[SemanticReranker] Error combining embeddings: {str(e)}",
                       level="warning", logger=self.logger)
            # Return first embedding as fallback
            return embeddings[0]


class HybridStore(VectorStorePort):
    """
    Hybrid vector store that combines BM25 search with selective embeddings.

    Architecture:
    1. Fast BM25 search for initial retrieval (SQLite FTS5)
    2. Selective embedding generation for top-K results only
    3. Semantic reranking for final results
    """

    def __init__(self, db_path: Optional[str] = None, logger: Optional[logging.Logger] = None):
        """
        Initialize hybrid store.

        Args:
            db_path: Path to SQLite database file
            logger: Optional logger instance
        """
        self.logger = logger or logging.getLogger(__name__)

        # Load configuration
        from ...infrastructure.config import get_config
        config = get_config()
        vector_store_config = config.get_vector_store_config()
        self.hybrid_config = vector_store_config.get('hybrid', {})

        # Setup database connection
        if db_path is not None:
            # Direct initialization with specific db_path (for tests)
            self.db_path = db_path
            self.db = sqlite3.connect(db_path, check_same_thread=False)
            self.db.row_factory = sqlite3.Row  # Enable column access by name

            # Initialize components
            self.bm25_searcher = BM25Searcher(self.db, logger)
            self.embedding_cache = EmbeddingCache(self.db, logger)
            self.semantic_reranker = SemanticReranker(self.embedding_cache, logger)
            self.query_processor = QueryProcessor(logger)

            log_message(f"[HybridStore] Initialized with database: \"{db_path}\"", logger=self.logger)
        else:
            # Lazy initialization - will be set up in connect()
            self.db_path = None
            self.db = None
            self.bm25_searcher = None
            self.embedding_cache = None
            self.semantic_reranker = None
            self.query_processor = QueryProcessor(logger)

            log_message("[HybridStore] Initialized without database - call connect() to set up database", logger=self.logger)

    def connect(self, uri: str) -> bool:
        """
        Connect to the vector store using the provided URI.
        For HybridStore, URI is used to determine the database path.
        """
        try:
            # Close existing connection if any
            if hasattr(self, 'db') and self.db:
                self.db.close()

            # Create database path from URI (similar to LanceDB pattern)
            # URI format: /path/to/base/codebase_name
            db_path = f"{uri}.db"  # Add .db extension for SQLite

            # Ensure directory exists
            import os
            db_dir = os.path.dirname(db_path)
            if db_dir and not os.path.exists(db_dir):
                os.makedirs(db_dir, exist_ok=True)
                log_message(f"[HybridStore] Created directory: {db_dir}", logger=self.logger)

            # Update database connection
            self.db_path = db_path
            self.db = sqlite3.connect(db_path, check_same_thread=False)
            self.db.row_factory = sqlite3.Row

            # Initialize or reinitialize components with new connection
            self.bm25_searcher = BM25Searcher(self.db, self.logger)
            self.embedding_cache = EmbeddingCache(self.db, self.logger)
            self.semantic_reranker = SemanticReranker(self.embedding_cache, self.logger)

            log_message(f"[HybridStore] Connected to database: {db_path}", logger=self.logger)
            return True

        except Exception as e:
            log_message(f"[HybridStore] Error connecting to {uri}: {str(e)}",
                       level="error", logger=self.logger)
            return False

    def create_table(self, table_name: str, schema: Any, mode: str = "create") -> bool:
        """
        Create table - for compatibility with VectorStorePort interface.
        In hybrid store, we use a unified schema in SQLite FTS.
        """
        # Tables are created automatically in BM25Searcher
        # Parameters are kept for interface compatibility but not used
        _ = schema, mode  # Suppress unused parameter warnings
        log_message(f"[HybridStore] Table creation handled automatically for: {table_name}",
                   logger=self.logger)
        return True

    def add_vectors(self, table_name: str, vectors: List[Any]) -> bool:
        """
        Add vectors to the store.
        In hybrid approach, we index content directly without generating embeddings.

        Args:
            table_name: Table name (for compatibility)
            vectors: List of objects with content and metadata

        Returns:
            True if successful
        """
        if not self.db or not self.bm25_searcher:
            log_message("[HybridStore] Database not connected. Call connect() first.",
                       level="error", logger=self.logger)
            return False

        try:
            documents = []

            for vector in vectors:
                # Extract content and metadata from vector object
                if hasattr(vector, '__dict__'):
                    data = vector.__dict__
                elif isinstance(vector, dict):
                    data = vector
                else:
                    log_message(f"[HybridStore] Unsupported vector type: {type(vector)}",
                               level="warning", logger=self.logger)
                    continue

                # Determine entity type and content based on table name
                if table_name == "_method":
                    content = data.get('source_code', '') or data.get('code', '')
                    entity_type = 'method'
                    entity_name = data.get('name', '')
                elif table_name == "_class":
                    content = data.get('source_code', '')
                    entity_type = 'class'
                    entity_name = data.get('class_name', '') or data.get('name', '')
                elif table_name == "_special":
                    content = data.get('content', '')
                    entity_type = 'file'
                    entity_name = Path(data.get('file_path', '')).name
                else:
                    content = str(data)
                    entity_type = 'unknown'
                    entity_name = ''

                # Build document for indexing
                doc = {
                    'content': content,
                    'file_path': data.get('file_path', ''),
                    'entity_type': entity_type,
                    'entity_name': entity_name,
                    'language': get_file_language_string(data.get('file_path', '')),
                    'metadata': {k: v for k, v in data.items() if k not in ['content', 'source_code', 'code']}
                }
                documents.append(doc)

            # Bulk insert for performance
            success = self.bm25_searcher.bulk_insert(documents)

            if success:
                log_message(f"[HybridStore] Added {len(documents)} documents to {table_name}",
                           logger=self.logger)

            return success

        except Exception as e:
            log_message(f"[HybridStore] Error adding vectors: {str(e)}",
                       level="error", logger=self.logger)
            return False

    def search(self, table_name: str, query_vector: List[float], limit: int = 10) -> List[Dict[str, Any]]:
        """
        Vector search - not used in hybrid approach.
        Redirects to hybrid search with empty query.
        """
        # Parameters kept for interface compatibility but not used
        _ = table_name, query_vector, limit  # Suppress unused parameter warnings
        log_message("[HybridStore] Vector search not supported, use search_hybrid instead",
                   level="warning", logger=self.logger)
        return []

    def search_hybrid(self, table_name: str, query_text: str, query_vector: Optional[List[float]] = None,
                     limit: int = 10, alpha: Optional[float] = None, enable_semantic_reranking: Optional[bool] = None) -> List[Dict[str, Any]]:
        """
        Hybrid search combining BM25 and selective embeddings.

        Args:
            table_name: Table name (used for entity type filtering)
            query_text: Text query for BM25 search
            query_vector: Not used in hybrid approach
            limit: Maximum number of results
            alpha: Weight for BM25 score (1-alpha for semantic score)

        Returns:
            List of search results
        """
        if not self.db or not self.bm25_searcher:
            log_message("[HybridStore] Database not connected. Call connect() first.",
                       level="error", logger=self.logger)
            return []

        try:
            start_time = time.time()

            # query_vector not used in hybrid approach but kept for interface compatibility
            _ = query_vector  # Suppress unused parameter warning

            # Use configuration defaults if not provided
            if alpha is None:
                alpha = self.hybrid_config.get('semantic_reranking_alpha', 0.7)
            if enable_semantic_reranking is None:
                enable_semantic_reranking = self.hybrid_config.get('enable_semantic_reranking', True)

            # 1. Process query
            processed_query = self.query_processor.process_query(query_text)

            # 2. Determine entity type filter from table name
            entity_type_filter = None
            if table_name == "_method":
                entity_type_filter = "method"
            elif table_name == "_class":
                entity_type_filter = "class"
            elif table_name == "_special":
                entity_type_filter = "file"

            # 3. BM25 search (fast initial retrieval)
            bm25_limit = min(limit * 5, 100)  # Get more results for reranking
            bm25_results = self.bm25_searcher.search(
                query_text,
                limit=bm25_limit,
                entity_type_filter=entity_type_filter,
                language_filter=processed_query.language_filter
            )

            bm25_time = time.time() - start_time

            # 4. Intelligent semantic reranking decision
            should_rerank = self._should_use_semantic_reranking(
                query_text, bm25_results, enable_semantic_reranking, limit
            )

            if should_rerank:
                log_message("[HybridStore] Using semantic reranking for better results", logger=self.logger)
                reranked_results = self.semantic_reranker.rerank(
                    query_text, bm25_results, alpha=alpha, max_rerank=min(20, len(bm25_results))
                )
            else:
                log_message("[HybridStore] Using BM25-only results (fast mode)", logger=self.logger)
                reranked_results = bm25_results

            total_time = time.time() - start_time

            # 5. Convert to expected format and limit results
            final_results = []
            for i, result in enumerate(reranked_results[:limit]):
                result_dict = {
                    'content': result.content,
                    'file_path': result.file_path,
                    'entity_type': result.entity_type,
                    'entity_name': result.entity_name,
                    'language': result.language,
                    'score': result.score,
                    'rank': i + 1,
                    **result.metadata
                }
                final_results.append(result_dict)

            log_message(f"[HybridStore] Search completed in {total_time:.3f}s "
                       f"(BM25: {bm25_time:.3f}s, found {len(final_results)} results)",
                       logger=self.logger)

            return final_results

        except Exception as e:
            log_message(f"[HybridStore] Error in hybrid search: {str(e)}",
                       level="error", logger=self.logger)
            return []



    def _should_use_semantic_reranking(self, query_text: str, bm25_results: List[SearchResult],
                                     enable_semantic_reranking: bool, limit: int) -> bool:
        """
        Intelligent decision on whether to use semantic reranking.

        Uses semantic reranking only when:
        1. It's enabled
        2. We have enough results to rerank
        3. Query seems to benefit from semantic understanding
        4. BM25 results seem ambiguous or insufficient

        Args:
            query_text: Original query
            bm25_results: Results from BM25 search
            enable_semantic_reranking: Whether semantic reranking is enabled
            limit: Requested number of results

        Returns:
            True if semantic reranking should be used
        """
        # 1. Check if semantic reranking is enabled
        if not enable_semantic_reranking:
            return False

        # 2. Check if query would benefit from semantic understanding
        query_lower = query_text.lower().strip()

        # Semantic queries (benefit from embeddings)
        semantic_indicators = [
            'para que serve', 'what does', 'how does', 'como funciona',
            'relacionado', 'related', 'similar', 'parecido',
            'todo', 'all', 'tudo', 'everything',
            'contexto', 'context', 'sobre', 'about'
        ]

        # Exact queries (BM25 is usually better)
        exact_indicators = [
            'função', 'function', 'método', 'method',
            'classe', 'class', 'sub ', 'procedure'
        ]

        # Check for semantic indicators
        has_semantic_indicators = any(indicator in query_lower for indicator in semantic_indicators)
        has_exact_indicators = any(indicator in query_lower for indicator in exact_indicators)

        # 3. Strong semantic indicators always trigger reranking
        if has_semantic_indicators:
            return True  # Clearly semantic query

        # 4. Exact indicators with specific names should use BM25 only
        if has_exact_indicators:
            # Check if it's a specific function/class name query
            words = query_lower.split()
            if len(words) >= 2:
                # If it has both exact indicator and a specific name, use BM25
                # e.g., "função getUserName"
                return False
            else:
                # If it's just "função" or "class", might benefit from reranking
                return len(bm25_results) > limit

        # 5. Check for direct function/class names (camelCase or specific patterns)
        words = query_lower.split()
        if len(words) == 1:
            word = words[0]
            # Check if it looks like a function name (camelCase, contains common patterns)
            if (any(c.isupper() for c in query_text) or  # Has uppercase (camelCase)
                any(pattern in word for pattern in ['get', 'set', 'validate', 'show', 'process', 'generate', 'create', 'update', 'delete']) or
                word.endswith('login') or word.endswith('name') or word.endswith('screen')):
                # Looks like a direct function name, use BM25 only
                return False

        # 6. Check BM25 result quality for ambiguous queries
        if bm25_results and len(bm25_results) > 1:
            # If top result has very high score, BM25 is probably good enough
            top_score = bm25_results[0].score
            second_score = bm25_results[1].score
            # If there's a clear winner, no need for reranking
            if top_score > second_score * 3:
                return False

        # 7. For ambiguous queries, use semantic reranking if we have multiple results
        if len(bm25_results) > limit:
            return True

        return False  # Default to BM25-only for performance



    def rerank(self, table_name: str, results: List[Dict[str, Any]], query: str, reranking_enabled: bool = True) -> List[Dict[str, Any]]:
        """
        Rerank search results using semantic similarity.
        In HybridStore, reranking is integrated into search_hybrid.
        """
        # If reranking is disabled, return original results
        if not reranking_enabled:
            log_message(f"[HybridStore] Reranking disabled for table {table_name}, returning original results",
                       logger=self.logger)
            return results

        try:
            # Convert results to SearchResult objects
            search_results = []
            for result in results:
                search_result = SearchResult(
                    content=result.get('content', ''),
                    file_path=result.get('file_path', ''),
                    entity_type=result.get('entity_type', ''),
                    entity_name=result.get('entity_name', ''),
                    language=result.get('language', ''),
                    metadata={k: v for k, v in result.items()
                             if k not in ['content', 'file_path', 'entity_type', 'entity_name', 'language', 'score']},
                    score=result.get('score', 0.0)
                )
                search_results.append(search_result)

            # Use semantic reranker
            reranked = self.semantic_reranker.rerank(query, search_results)

            # Convert back to dict format
            reranked_dicts = []
            for result in reranked:
                result_dict = {
                    'content': result.content,
                    'file_path': result.file_path,
                    'entity_type': result.entity_type,
                    'entity_name': result.entity_name,
                    'language': result.language,
                    'score': result.score,
                    **result.metadata
                }
                reranked_dicts.append(result_dict)

            log_message(f"[HybridStore] Reranked {len(results)} results for table {table_name}",
                       logger=self.logger)
            return reranked_dicts

        except Exception as e:
            log_message(f"[HybridStore] Error in reranking: {str(e)}",
                       level="warning", logger=self.logger)
            return results  # Return original results on error

    def table_exists(self, table_name: str) -> bool:
        """
        Check if a table exists.
        In HybridStore, we use a unified FTS table, so this always returns True.
        """
        if not self.db:
            log_message("[HybridStore] Database not connected. Call connect() first.",
                       level="warning", logger=self.logger)
            return False

        # Use the BM25Searcher's lock for consistency
        with self.bm25_searcher._db_lock if self.bm25_searcher else threading.RLock():
            try:
                # Check if the FTS table exists
                cursor = self.db.execute("""
                    SELECT name FROM sqlite_master
                    WHERE type='table' AND name='code_search'
                """)
                exists = cursor.fetchone() is not None

                log_message(f"[HybridStore] Table {table_name} exists: {exists}", logger=self.logger)
                return exists

            except Exception as e:
                log_message(f"[HybridStore] Error checking table existence: {str(e)}",
                           level="warning", logger=self.logger)
                return False

    def optimize_indexes(self):
        """Optimize database indexes for better performance."""
        try:
            self.bm25_searcher.optimize_indexes()
            log_message("[HybridStore] Indexes optimized", logger=self.logger)
        except Exception as e:
            log_message(f"[HybridStore] Error optimizing indexes: {str(e)}",
                       level="warning", logger=self.logger)

    def cleanup_cache(self, max_age_days: int = 30):
        """Clean up old embeddings from cache."""
        try:
            self.embedding_cache.cleanup_old_embeddings(max_age_days)
            log_message("[HybridStore] Cache cleaned up", logger=self.logger)
        except Exception as e:
            log_message(f"[HybridStore] Error cleaning cache: {str(e)}",
                       level="warning", logger=self.logger)

    def close(self):
        """Close database connection."""
        if self.db:
            self.db.close()
            log_message("[HybridStore] Database connection closed", logger=self.logger)
