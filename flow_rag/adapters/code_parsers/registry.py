from ...domain.ports.file_loader import LanguageEnum
from ...domain.ports.code_parser import CodeParserPort
from typing import Type, Optional, List, Dict

class ParserRegistry:
    """
    Registry for different code parsers.
    This allows for easy addition of new parsers for different languages.
    """
    _parsers: Dict[LanguageEnum, CodeParserPort] = {}

    @classmethod
    def register(cls, language: LanguageEnum, parser_class: Type[CodeParserPort]) -> None:
        """Register a parser for a specific language."""
        cls._parsers[language] = parser_class()

    @classmethod
    def get_parser(cls, language: LanguageEnum) -> Optional[CodeParserPort]:
        """Get a parser for the specified language."""
        return cls._parsers.get(language)

    @classmethod
    def get_supported_languages(cls) -> List[LanguageEnum]:
        """Get a list of all supported languages."""
        return list(cls._parsers.keys())
