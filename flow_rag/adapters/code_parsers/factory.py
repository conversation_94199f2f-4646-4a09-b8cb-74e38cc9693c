from flow_rag.infrastructure.log_utils import log_message
from .tree_sitter_parser import TreesitterCodeParser
from .optimized_vb6_parser import OptimizedVB6CodeParser
from typing import Optional, Any
from flow_rag.domain.ports.file_loader import (
    TREE_SITTER_FILE_FILTERS, 
    VB6_FILE_FILTERS, 
    LanguageEnum,
    get_main_extension
)

class CodeParserFactory:
    """
    Factory for creating code parser instances.
    """
    @staticmethod
    def create_parser(language: LanguageEnum, logger: Optional[Any] = None):
        """
        Create a code parser instance based on language.
        Args:
            language: LanguageEnum
            logger: Optional logger instance
        Returns:
            Instance of BaseCodeParser
        Raises:
            ValueError: If an unsupported language is specified
        """
        log_message(f"create_parser: language={language}, type={type(language)}", logger=logger)

        # Get the main file extension for the language
        file_extension = get_main_extension(language)
            
        # Check if the file extension is in the whitelist
        if file_extension in TREE_SITTER_FILE_FILTERS["WHITELIST_EXTS"]:
            log_message(f"# TreesitterCodeParser selected for {language.value} (extension: {file_extension})", logger=logger)
            return TreesitterCodeParser(logger=logger)
        elif file_extension in VB6_FILE_FILTERS["WHITELIST_EXTS"]:
            log_message(f"# OptimizedVB6CodeParser selected for {language.value} (extension: {file_extension})", logger=logger)
            return OptimizedVB6CodeParser(logger=logger)
        else:
            raise ValueError(f"Unsupported language for parsing: {file_extension}")
