from flow_rag.adapters.code_parsers.registry import ParserRegistry
from flow_rag.adapters.code_parsers.tree_sitter_parser import TreesitterCodeParser
from flow_rag.adapters.code_parsers.optimized_vb6_parser import OptimizedVB6CodeParser
from flow_rag.domain.ports.file_loader import LanguageEnum

# Register all TreeSitter parsers for supported languages
ParserRegistry.register(LanguageEnum.PYTHON, TreesitterCodeParser)
ParserRegistry.register(LanguageEnum.JAVA, TreesitterCodeParser)
ParserRegistry.register(LanguageEnum.JAVASCRIPT, TreesitterCodeParser)
ParserRegistry.register(LanguageEnum.TYPESCRIPT, TreesitterCodeParser)

# Register Optimized VB6 parser (high-performance)
ParserRegistry.register(LanguageEnum.VB6, OptimizedVB6CodeParser)

# Add other parsers/languages as needed
