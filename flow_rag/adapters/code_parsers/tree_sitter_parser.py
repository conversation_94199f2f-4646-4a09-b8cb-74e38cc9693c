import sys
import traceback
from typing import List, <PERSON><PERSON>, Dict, Any, Optional
import logging
import os
import fnmatch
from collections import defaultdict

from flow_rag.infrastructure.log_utils import log_message
from ...domain.models import Method, Class, Reference
from ...domain.ports.file_loader import LanguageEnum, TREE_SITTER_FILE_FILTERS, FILE_FILTERS
from .base_parser import BaseCodeParser

from tree_sitter import Node
from tree_sitter_languages import get_language, get_parser
import warnings
warnings.filterwarnings('ignore', category=FutureWarning)

class TreesitterMethodNode:
    """
    Represents a method node parsed by Tree-sitter.
    """
    def __init__(
        self,
        name: str,
        doc_comment: str,
        method_source_code: str,
        node: Node,
        class_name: Optional[str] = None
    ):
        self.name = name
        self.doc_comment = doc_comment
        self.method_source_code = method_source_code
        self.node = node
        self.class_name = class_name


class TreesitterClassNode:
    """
    Represents a class node parsed by Tree-sitter.
    """
    def __init__(
        self,
        name: str,
        method_declarations: list,
        node: Node,
        logger: logging.Logger
    ):
        self.name = name
        self.source_code = node.text.decode() if node.text else ""
        self.method_declarations = method_declarations
        self.node = node
        self.logger = logger


class TreesitterParser:
    """
    Core Tree-sitter parser functionality.
    """
    LANGUAGE_QUERIES = {
        'java': {
            'class_query': '(class_declaration name: (identifier) @class.name)',
            'method_query': '[ (method_declaration name: (identifier) @method.name) (constructor_declaration name: (identifier) @method.name) ]',
            'doc_comment': '((block_comment) @comment)'
        },
        'python': {
            'class_query': '(class_definition name: (identifier) @class.name)',
            'method_query': '(function_definition name: (identifier) @method.name)',
            'doc_comment': '(expression_statement (string) @comment)'
        },
        'javascript': {
            'class_query': '(class_declaration name: (identifier) @class.name)',
            'method_query': '''[
                (function_declaration name: (identifier) @function.name)
                (lexical_declaration (variable_declarator name: (identifier) @function.name value: [(arrow_function) (function)]))
                (variable_declaration (variable_declarator name: (identifier) @function.name value: [(arrow_function) (function)]))
                (export_statement (function_declaration name: (identifier) @function.name))
                (export_statement (lexical_declaration (variable_declarator name: (identifier) @function.name value: [(arrow_function) (function)])))
                (method_definition name: (property_identifier) @function.name)
                (pair key: (property_identifier) @function.name value: [(arrow_function) (function)])
            ]''',
            'doc_comment': '(comment) @comment'
        },
        'typescript': {
            'class_query': '(class_declaration name: (identifier) @class.name)',
            'method_query': '''[
                (function_declaration name: (identifier) @function.name)
                (lexical_declaration (variable_declarator name: (identifier) @function.name value: [(arrow_function) (function)]))
                (variable_declaration (variable_declarator name: (identifier) @function.name value: [(arrow_function) (function)]))
                (export_statement (function_declaration name: (identifier) @function.name))
                (export_statement (lexical_declaration (variable_declarator name: (identifier) @function.name value: [(arrow_function) (function)])))
                (method_definition name: (property_identifier) @function.name)
                (pair key: (property_identifier) @function.name value: [(arrow_function) (function)])
            ]''',
            'doc_comment': '(comment) @comment'
}
    }

    def __init__(self, language: LanguageEnum):
        self.language_enum = language
        # Ensure string type for tree_sitter_languages
        # language.value is already a string (e.g., 'python', 'java', etc)
        lang_str = language.value
        
        self.logger = logging.getLogger(__name__)
        log_message(f"[TreeSitter] lang_str={lang_str}, language={language}, type={type(language)}", logger=self.logger)
        self.parser = get_parser(lang_str)
        self.language_obj = get_language(lang_str)
        
        # Get query config using language string value as key
        self.query_config = self.LANGUAGE_QUERIES.get(lang_str)
        if not self.query_config:
            log_message(f"Unsupported language: {language} (lang_str: {lang_str})", logger=self.logger, level="error")
            sys.exit(1)
        
        # Initialize queries
        try:
            self.class_query = self.language_obj.query(self.query_config['class_query'])
            self.method_query = self.language_obj.query(self.query_config['method_query'])
            self.doc_query = self.language_obj.query(self.query_config['doc_comment'])  # Note: changed from doc_query to doc_comment
        except Exception as e:
            log_message(f"Error initializing queries for language {language}: {str(e)}", logger=self.logger, level="error")
            log_message(f"Query config keys: {list(self.query_config.keys())}", logger=self.logger)
            raise

    def parse(self, file_bytes: bytes) -> tuple[list[TreesitterClassNode], list[TreesitterMethodNode]]:
        """
        Parse code content and extract class and method nodes.

        Args:
            file_bytes: Bytes representation of code content

        Returns:
            Tuple of (class_nodes, method_nodes)
        """
        tree = self.parser.parse(file_bytes)
        root_node = tree.root_node

        class_results = []
        method_results = []

        class_name_by_node = {}
        class_captures = self.class_query.captures(root_node)
        class_nodes = []

        for node, capture_name in class_captures:
            if capture_name == 'class.name':
                class_name = node.text.decode() if node.text else ""
                class_node = node.parent
                if class_node:
                    class_name_by_node[class_node.id] = class_name
                    method_declarations = self._extract_methods_in_class(class_node)
                    class_results.append(TreesitterClassNode(class_name, method_declarations, class_node, self.logger))
                    class_nodes.append(class_node)

        method_captures = self.method_query.captures(root_node)
        for node, capture_name in method_captures:
            if capture_name in ['method.name', 'function.name']:
                method_name = node.text.decode() if node.text else ""
                method_node = node.parent
                if method_node:
                    method_source_code = method_node.text.decode() if method_node.text else ""
                    doc_comment = self._extract_doc_comment(method_node)
                    parent_class_name = None

                    for class_node in class_nodes:
                        if self._is_descendant_of(method_node, class_node):
                            parent_class_name = class_name_by_node[class_node.id]
                            break

                    method_results.append(TreesitterMethodNode(
                        name=method_name,
                        doc_comment=doc_comment,
                        method_source_code=method_source_code,
                        node=method_node,
                        class_name=parent_class_name
                    ))

        return class_results, method_results

    def _extract_methods_in_class(self, class_node):
        """Extract method declarations within a class."""
        method_declarations = []
        method_captures = self.method_query.captures(class_node)

        for node, capture_name in method_captures:
            if capture_name in ['method.name', 'function.name']:
                if node.parent and node.parent.text:
                    method_declaration = node.parent.text.decode()
                    method_declarations.append(method_declaration)

        return method_declarations

    def _extract_doc_comment(self, node):
        """Extract documentation comments preceding a node."""
        doc_comment = ''
        current_node = node.prev_sibling

        while current_node:
            captures = self.doc_query.captures(current_node)
            if captures:
                for cap_node, cap_name in captures:
                    if cap_name == 'comment':
                        doc_comment = cap_node.text.decode() + '\n' + doc_comment
            elif current_node.type not in ['comment', 'block_comment', 'line_comment', 'expression_statement']:
                # Stop if we reach a node that's not a comment
                break

            current_node = current_node.prev_sibling

        return doc_comment.strip()

    def _is_descendant_of(self, node, ancestor):
        """Check if a node is a descendant of another node."""
        current = node.parent
        while current:
            if current == ancestor:
                return True
            current = current.parent
        return False

class TreesitterCodeParser(BaseCodeParser):
    """
    Implementation of CodeParserPort using Tree-sitter for code parsing.
    """
    def __init__(self, logger: Optional[logging.Logger] = None):
        super().__init__(logger=logger)
        self.parsers_by_language = {}
        self.gitignore_patterns = []

    def find_classes(self, tree: Any) -> Any:
        raise NotImplementedError("find_classes is not implemented for TreesitterCodeParser")

    def find_methods(self, tree: Any) -> Any:
        raise NotImplementedError("find_methods is not implemented for TreesitterCodeParser")

    def parse(self, code: str) -> Any:
        raise NotImplementedError("parse is not implemented for TreesitterCodeParser")

    def set_defaults(self):
        """Set default file filters for Tree-sitter."""
        self.BLACKLIST_DIR = FILE_FILTERS["BLACKLIST_DIRS"]
        self.WHITELIST_FILES = TREE_SITTER_FILE_FILTERS["WHITELIST_EXTS"]
        self.BLACKLIST_FILES = TREE_SITTER_FILE_FILTERS["BLACKLIST_FILES"]



    NODE_TYPES = {
        "python": {
            "class": "class_definition",
            "method": "function_definition"
        },
        "java": {
            "class": "class_declaration",
            "method": "method_declaration"
        },
        "javascript": {
            "class": "class_declaration",
            "method": "method_definition"
        },
        "typescript": {
            "class": "class_declaration",
            "method": "method_definition"
        },
    }

    REFERENCE_IDENTIFIERS = {
        "python": {
            "class": "identifier",
            "method": "call",
            "child_field_name": "function"
        },
        "java": {
            "class": "identifier",
            "method": "method_invocation",
            "child_field_name": "name"
        },
        "javascript": {
            "class": "identifier",
            "method": "call_expression",
            "child_field_name": "function"
        },
        "typescript": {
            "class": "identifier",
            "method": "call_expression",
            "child_field_name": "function"
        },
    }

    def parse_code_files(self, file_list: List[Tuple[str, LanguageEnum]]) -> Tuple[List[Class], List[Method], List[str], List[str]]:
        """
        Parse code files and extract classes and methods.

        Args:
            file_list: List of tuples (file_path, language_enum)

        Returns:
            Tuple of (classes, methods, class_names, method_names)
        """
        class_data = []
        method_data = []
        all_class_names = set()
        all_method_names = set()

        files_by_language = defaultdict(list)
        for file_path, language in file_list:
            files_by_language[language].append(file_path)

        for language, files in files_by_language.items():
            parser = self._get_parser_for_language(language)

            for file_path in files:
                try:
                    with open(file_path, "r", encoding="utf-8") as file:
                        code = file.read()
                        file_bytes = code.encode()
                        class_nodes, method_nodes = parser.parse(file_bytes)

                        # Process class nodes
                        for class_node in class_nodes:
                            class_name = class_node.name
                            all_class_names.add(class_name)
                            class_data.append(Class(
                                name=class_name,
                                file_path=file_path,
                                source_code=class_node.source_code,
                                method_declarations=class_node.method_declarations,
                                constructor_declaration="",  # Extract if needed
                                references=[]
                            ))

                        # Process method nodes
                        for method_node in method_nodes:
                            method_name = method_node.name
                            all_method_names.add(method_name)
                            method_data.append(Method(
                                name=method_name,
                                file_path=file_path,
                                class_name=method_node.class_name if method_node.class_name else "",
                                doc_comment=method_node.doc_comment,
                                source_code=method_node.method_source_code,
                                references=[]
                            ))
                except Exception as e:
                    log_message(f"[TreeSitter CodeParser] Error parsing file {file_path}: {str(e)}", logger=self.logger, level="error")
                    log_message(traceback.format_exc(), logger=self.logger, level="error")

        return class_data, method_data, list(all_class_names), list(all_method_names)

    def find_references(self, file_list: List[Tuple[str, LanguageEnum]], class_names: List[str], method_names: List[str]) -> Dict[str, Dict[str, List[Dict[str, Any]]]]:
        """
        Find references to classes and methods in code files.

        Args:
            file_list: List of tuples (file_path, language_enum)
            class_names: List of class names to find references for
            method_names: List of method names to find references for

        Returns:
            Dictionary of references by type and name
        """
        references = {'class': defaultdict(list), 'method': defaultdict(list)}
        files_by_language = defaultdict(list)

        # Convert names to sets for O(1) lookup
        class_names_set = set(class_names)
        method_names_set = set(method_names)

        for file_path, language in file_list:
            files_by_language[language].append(file_path)

        for language, files in files_by_language.items():
            parser = self._get_parser_for_language(language)

            for file_path in files:
                try:
                    with open(file_path, "r", encoding="utf-8") as file:
                        code = file.read()
                        file_bytes = code.encode()
                        tree = parser.parser.parse(file_bytes)

                        # Single pass through the AST
                        stack: List[Tuple[Any, Any]] = [(tree.root_node, None)]
                        while stack:
                            node, parent = stack.pop()

                            # Check for identifiers
                            if node.type == 'identifier':
                                name = node.text.decode()

                                # Check if it's a class reference
                                if name in class_names_set and parent and parent.type in ['type', 'class_type', 'object_creation_expression']:
                                    references['class'][name].append(Reference(
                                        file=file_path,
                                        line=node.start_point[0] + 1,
                                        column=node.start_point[1] + 1,
                                        text=parent.text.decode()
                                    ).__dict__)

                                # Check if it's a method reference
                                if name in method_names_set and parent and parent.type in ['call_expression', 'method_invocation']:
                                    references['method'][name].append(Reference(
                                        file=file_path,
                                        line=node.start_point[0] + 1,
                                        column=node.start_point[1] + 1,
                                        text=parent.text.decode()
                                    ).__dict__)

                            # Add children to stack with their parent
                            stack.extend((child, node) for child in node.children)
                except Exception as e:
                    log_message(f"Error finding references in file {file_path}: {str(e)}", logger=self.logger, level="error")
                    log_message(traceback.format_exc(), logger=self.logger, level="error")

        return dict(references)

    def get_supported_languages(self) -> List[LanguageEnum]:
        """Get list of supported languages."""
        # Getting from the WHITELIST_FILES
        supported_languages = set()
        for ext in self.WHITELIST_FILES:
            language = self.get_language_from_extension(ext)
            if language:
                supported_languages.add(language)
        return list(supported_languages)

    def should_ignore_path(self, path: str) -> bool:
        """
        Check if a path should be ignored based on gitignore patterns.

        Args:
            path: Path to check

        Returns:
            True if path should be ignored, False otherwise
        """
        # Remove the base path for comparison
        relative_path = os.path.relpath(path)

        for pattern in self.gitignore_patterns:
            # Convert gitignore pattern to fnmatch pattern
            if pattern.startswith('!'):
                continue  # Ignore negation patterns for now

            # Add ** to match any number of directories
            if not pattern.startswith('**'):
                pattern = '**/' + pattern

            if fnmatch.fnmatch(relative_path, pattern):
                return True

        return False

    def _load_gitignore_patterns(self, codebase_path: str) -> List[str]:
        """Load patterns from .gitignore file."""
        gitignore_path = os.path.join(codebase_path, '.gitignore')
        gitignore_patterns = []

        if os.path.exists(gitignore_path):
            with open(gitignore_path, 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if line and not line.startswith('#'):
                        # Remove trailing slash for directories
                        if line.endswith('/'):
                            line = line[:-1]
                        gitignore_patterns.append(line)

        return gitignore_patterns

    def _get_parser_for_language(self, language: LanguageEnum) -> TreesitterParser:
        """Get or create a TreesitterParser for a language."""
        if language not in self.parsers_by_language:
            self.parsers_by_language[language] = TreesitterParser(language)
        return self.parsers_by_language[language]
