"""
flow_rag.adapters.llm_providers.factory

This module provides the FlowLLMClientProvider class, which implements the LLMPort interface and centralizes all interactions with the FlowAIClient for LLM usage in the FlowRAG library.
"""

from typing import Optional
import logging
from flow_rag.infrastructure.log_utils import log_message
import traceback

from ...domain.ports.llm import LLMPort
from .llm_prompts import HYDE_SYSTEM_PROMPT, HYDE_V2_SYSTEM_PROMPT, CHAT_SYSTEM_PROMPT, RERANK_PROMPT, DOCUMENTATION_ANALYSIS_PROMPT

# Import Flow client if available
try:
    from flow_api.clients.ai_client import FlowAIClient
    FLOW_AVAILABLE = True
except ImportError:
    FLOW_AVAILABLE = False

# TODO: Implement multi-provider logic to allow selection between different LLM families

class FlowLLMClientProvider(LLMPort):
    HYDE_SYSTEM_PROMPT = HYDE_SYSTEM_PROMPT
    HYDE_V2_SYSTEM_PROMPT = HYDE_V2_SYSTEM_PROMPT
    CHAT_SYSTEM_PROMPT = CHAT_SYSTEM_PROMPT
    RERANK_PROMPT = RERANK_PROMPT
    DOCUMENTATION_ANALYSIS_PROMPT = DOCUMENTATION_ANALYSIS_PROMPT

    def __init__(self, logger: Optional[logging.Logger] = None, api_key: Optional[str] = None, model_name: str = "gpt-4o-mini"):
        if not FLOW_AVAILABLE:
            log_message("Flow AI client is not available. Check the flow_api package installation.", level="error", logger=logger, to_terminal=True)
            raise
        if api_key:
            self.client = FlowAIClient()
            log_message("# Flow AI client initialized", level="info", logger=logger)
        else:
            self.client = FlowAIClient()
        self._model_name = model_name
        self.logger = logger

        # Get max_tokens from config
        from ...infrastructure.config import get_config
        config = get_config()
        llm_config = config.get_llm_config()
        self._max_context_length = llm_config.get('max_tokens')

    def answer_with_context(self, query, context):
        log_message("--- Answering with context", logger=self.logger)

        # Check if this is primarily a documentation query
        if self._is_documentation_query(query, context):
            log_message("Using documentation analysis prompt for this query", logger=self.logger)
            sys_prompt = self.DOCUMENTATION_ANALYSIS_PROMPT.format(context=context)
        else:
            log_message("Using standard chat prompt for this query", logger=self.logger)
            sys_prompt = self.CHAT_SYSTEM_PROMPT.format(context=context)

        return self.send_via_flow(user_prompt=query, system_prompt=sys_prompt)

    def _is_documentation_query(self, query: str, context: str) -> bool:
        """
        Determine if a query is primarily about documentation based on query terms and context content.

        Args:
            query: The user's query
            context: The context containing search results

        Returns:
            True if this appears to be a documentation-focused query
        """
        # Documentation keywords in the query
        doc_keywords = [
            'readme', 'documentation', 'docs', 'what does', 'what is', 'overview',
            'summary', 'summarize', 'explain project', 'project description',
            'getting started', 'how to use', 'installation', 'setup', 'configure',
            'introduction', 'about', 'purpose', 'features', 'architecture overview'
        ]

        query_lower = query.lower()
        has_doc_keywords = any(keyword in query_lower for keyword in doc_keywords)

        # Check if context is primarily documentation (special files)
        has_documentation_section = "=== RELEVANT DOCUMENTATION ===" in context

        # Count lines of documentation vs code
        if has_documentation_section:
            doc_lines = context.count('\n') - context.split("=== RELEVANT DOCUMENTATION ===")[0].count('\n')
            total_lines = context.count('\n')
            doc_ratio = doc_lines / max(total_lines, 1)

            # If >50% of context is documentation OR query has doc keywords, use doc prompt
            return doc_ratio > 0.5 or has_doc_keywords

        # Fallback: use doc prompt if query clearly asks for documentation
        return has_doc_keywords

    def enhance_query(self, query, context=None):
        log_message("--- Enhancing query", logger=self.logger)
        sys_prompt = self.HYDE_SYSTEM_PROMPT
        return self.send_via_flow(user_prompt=f"Help predict the answer to the query: {query}", system_prompt=sys_prompt)

    def generate_context(self, *args, **kwargs):
        # Example: adjust as needed for real logic
        return "[FLOW CONTEXT GENERATED]"

    @property
    def max_context_length(self):
        return self._max_context_length

    @property
    def model_name(self):
        return self._model_name

    def rerank_results(self, query, context):
        log_message("--- Reranking results", logger=self.logger)
        sys_prompt = self.RERANK_PROMPT
        return self.send_via_flow(user_prompt=query, system_prompt=sys_prompt)

    def send_via_flow(self, user_prompt, system_prompt):
        log_message(f"[FlowLLMClientProvider] Sending to FlowAIClient | user_prompt: {user_prompt[:120]} ...({len(user_prompt)} chars)", logger=self.logger)
        try:
            response = self.client.send(sys_prompt=system_prompt, user_prompt=user_prompt)
            log_message(f"[FlowLLMClientProvider] Received from FlowAIClient | response: {str(response)[:300]} ...({len(str(response))} chars)", logger=self.logger)
            return response
        except Exception as e:
            log_message(f"[FlowLLMClientProvider] Error in send_via_flow: {str(e)}", level="error", logger=self.logger)
            log_message(traceback.format_exc(), logger=self.logger, level="error")
            raise