import unittest
import tempfile
import os
import shutil

import flow_rag


class TestFlowRAGClient(unittest.TestCase):
    """Test cases for FlowRAG client interface."""
    
    def setUp(self):
        """Set up test fixtures."""
        # Create temporary directory for test codebase
        self.temp_dir = tempfile.mkdtemp()
        
        # Create a simple test codebase
        self.test_files = {
            "main.py": """
def authenticate_user(username, password):
    '''Authenticate user with credentials'''
    if validate_credentials(username, password):
        return create_session(username)
    return None

def validate_credentials(username, password):
    '''Validate user credentials'''
    return username == "admin" and password == "secret"

class UserManager:
    '''Manages user operations'''
    
    def __init__(self):
        self.users = {}
    
    def create_user(self, username, email):
        '''Create a new user account'''
        self.users[username] = {"email": email, "active": True}
        return True
""",
            "config.py": """
DATABASE_URL = "sqlite:///app.db"
SECRET_KEY = "your-secret-key"
DEBUG = True
""",
            "README.md": """
# Test Project

This is a simple authentication system with the following features:

- User authentication
- Session management
- User account creation
- Database integration

## Usage

```python
from main import authenticate_user
result = authenticate_user("admin", "secret")
```
"""
        }
        
        # Write test files
        for filename, content in self.test_files.items():
            file_path = os.path.join(self.temp_dir, filename)
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
    
    def tearDown(self):
        """Clean up test fixtures."""
        try:
            shutil.rmtree(self.temp_dir, ignore_errors=True)
        except:
            pass
    
    def test_initialize(self):
        """Test codebase initialization."""
        # Test initialization with valid path
        try:
            result = flow_rag.initialize(self.temp_dir)
            # Should not raise exception
            self.assertIsNone(result)  # initialize returns None on success
        except Exception as e:
            # If it fails, it should be due to missing dependencies, not path issues
            self.assertNotIn("does not exist", str(e))
    
    def test_initialize_with_progress_callback(self):
        """Test initialization with progress callback."""
        # Progress callback
        progress_calls = []
        def progress_callback(percentage):
            progress_calls.append(percentage)

        # Test initialization with callback
        try:
            result = flow_rag.initialize(self.temp_dir, progress_callback=progress_callback)
            # Should not raise exception
            self.assertIsNone(result)  # initialize returns None on success
        except Exception as e:
            # If it fails, it should be due to missing dependencies, not callback issues
            self.assertNotIn("progress_callback", str(e))
    
    def test_get_context(self):
        """Test context-only retrieval."""
        # Test context retrieval
        try:
            context = flow_rag.get_context("authentication functions", self.temp_dir)
            # Should return a string (even if empty due to no real indexing)
            self.assertIsInstance(context, str)
        except Exception as e:
            # If it fails, it should be due to missing dependencies or no indexed data
            self.assertNotIn("does not exist", str(e))
    
    def test_query_basic(self):
        """Test basic query functionality."""
        # Test basic query
        try:
            response = flow_rag.query("What does this project do?", self.temp_dir)
            # Should return a string response
            self.assertIsInstance(response, str)
        except Exception as e:
            # If it fails, it should be due to missing LLM or no indexed data
            self.assertNotIn("does not exist", str(e))
    
    def test_query_with_output_format(self):
        """Test query with output format."""
        # Test query with output format
        try:
            response = flow_rag.query(
                "List the main classes",
                self.temp_dir,
                output_format="json"
            )
            # Should return a string response
            self.assertIsInstance(response, str)
        except Exception as e:
            # If it fails, it should be due to missing LLM or no indexed data
            self.assertNotIn("does not exist", str(e))
    
    def test_query_with_return_context(self):
        """Test query with return_context=True."""
        # Test query with return_context
        try:
            response = flow_rag.query(
                "How does authentication work?",
                self.temp_dir,
                return_context=True
            )
            # Should return a dict with answer and context
            self.assertIsInstance(response, (dict, str))
            if isinstance(response, dict):
                self.assertIn('answer', response)
                self.assertIn('context', response)
        except Exception as e:
            # If it fails, it should be due to missing LLM or no indexed data
            self.assertNotIn("does not exist", str(e))
    
    def test_initialize_invalid_path(self):
        """Test initialization with invalid codebase path."""
        invalid_path = "/nonexistent/path"
        
        with self.assertRaises(ValueError) as context:
            flow_rag.initialize(invalid_path)
        
        self.assertIn("does not exist", str(context.exception))
    
    def test_get_context_invalid_path(self):
        """Test get_context with invalid codebase path."""
        invalid_path = "/nonexistent/path"
        
        with self.assertRaises(ValueError) as context:
            flow_rag.get_context("test query", invalid_path)
        
        self.assertIn("does not exist", str(context.exception))
    
    def test_query_invalid_path(self):
        """Test query with invalid codebase path."""
        invalid_path = "/nonexistent/path"
        
        with self.assertRaises(ValueError) as context:
            flow_rag.query("test query", invalid_path)
        
        self.assertIn("does not exist", str(context.exception))


if __name__ == "__main__":
    unittest.main()
