import unittest
import tempfile
import os
import logging

from flow_rag.adapters.vector_stores.hybrid_store import HybridStore
from flow_rag.domain.ports.embeddings import EmbeddingsPort


class MockEmbeddingProvider(EmbeddingsPort):
    """Mock embedding provider for testing."""

    def embed_query(self, query):
        return [0.1, 0.2, 0.3]

    def _get_embeddings(self, texts):
        return [[0.1, 0.2, 0.3] for _ in texts]

    def embed_methods(self, methods):
        """Mock method embedding."""
        for method in methods:
            method.embeddings = [0.1, 0.2, 0.3]
        return methods

    def embed_classes(self, classes):
        """Mock class embedding."""
        for cls in classes:
            cls.embeddings = [0.1, 0.2, 0.3]
        return classes

    def embed_special_files(self, special_files):
        """Mock special file embedding."""
        return special_files

    @property
    def get_embedding_dimensions(self):
        return 3

    @property
    def max_token_limit(self):
        return 1000

    @property
    def model_name(self):
        return "mock-model"

    def clip_text_to_max_tokens(self, text):
        return text[:100]  # Simple truncation


class TestHybridStore(unittest.TestCase):
    """Test cases for HybridStore."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.temp_file = tempfile.NamedTemporaryFile(suffix='.db', delete=False)
        self.db_path = self.temp_file.name
        self.temp_file.close()
        
        # Setup logger
        self.logger = logging.getLogger(__name__)
        self.logger.setLevel(logging.WARNING)  # Reduce noise in tests
        
        # Create HybridStore instance
        self.store = HybridStore(db_path=self.db_path, logger=self.logger)
        
        # Mock embedding provider
        self.embedding_provider = MockEmbeddingProvider()
    
    def tearDown(self):
        """Clean up test fixtures."""
        try:
            self.store.close()
            os.unlink(self.db_path)
        except:
            pass
    
    def test_initialization(self):
        """Test HybridStore initialization."""
        self.assertIsNotNone(self.store.db)
        self.assertEqual(self.store.db_path, self.db_path)
        self.assertIsNotNone(self.store.bm25_searcher)
        self.assertIsNotNone(self.store.embedding_cache)
        self.assertIsNotNone(self.store.semantic_reranker)
        self.assertIsNotNone(self.store.query_processor)
    
    def test_connect(self):
        """Test database connection."""
        test_uri = "/tmp/test_codebase"
        success = self.store.connect(test_uri)
        self.assertTrue(success)
        self.assertEqual(self.store.db_path, f"{test_uri}.db")
    
    def test_create_table(self):
        """Test table creation (compatibility method)."""
        success = self.store.create_table("_method", None, "create")
        self.assertTrue(success)
    
    def test_add_vectors_methods(self):
        """Test adding method vectors."""
        test_methods = [
            {
                'name': 'getUserName',
                'source_code': 'def getUserName(): return "John"',
                'file_path': 'user.py',
                'class_name': 'User',
                'doc_comment': 'Returns user name'
            },
            {
                'name': 'validateLogin',
                'source_code': 'def validateLogin(user, pwd): return True',
                'file_path': 'auth.py',
                'class_name': 'Auth',
                'doc_comment': 'Validates login credentials'
            }
        ]
        
        success = self.store.add_vectors("_method", test_methods)
        self.assertTrue(success)
    
    def test_add_vectors_classes(self):
        """Test adding class vectors."""
        test_classes = [
            {
                'class_name': 'UserManager',
                'source_code': 'class UserManager:\n    def __init__(self): pass',
                'file_path': 'user.py',
                'constructor_declaration': 'def __init__(self)',
                'method_declarations': '["getUserName", "setUserName"]'
            }
        ]
        
        success = self.store.add_vectors("_class", test_classes)
        self.assertTrue(success)
    
    def test_add_vectors_special_files(self):
        """Test adding special file vectors."""
        test_special = [
            {
                'content': 'This is a README file with project documentation',
                'file_path': 'README.md',
                'file_type': 'markdown',
                'chunk_index': 0,
                'total_chunks': 1,
                'token_count': 10
            }
        ]
        
        success = self.store.add_vectors("_special", test_special)
        self.assertTrue(success)
    
    def test_search_hybrid_methods(self):
        """Test hybrid search for methods."""
        # First add some test data
        test_methods = [
            {
                'name': 'getUserName',
                'source_code': 'def getUserName(): return "John"',
                'file_path': 'user.py',
                'class_name': 'User',
                'doc_comment': 'Returns user name'
            }
        ]
        
        self.store.add_vectors("_method", test_methods)
        
        # Test search
        results = self.store.search_hybrid("_method", "getUserName", limit=5)
        
        self.assertIsInstance(results, list)
        self.assertGreater(len(results), 0)
        self.assertEqual(results[0]['entity_name'], 'getUserName')
        self.assertEqual(results[0]['entity_type'], 'method')
    
    def test_search_hybrid_with_semantic_reranking(self):
        """Test hybrid search with semantic reranking enabled."""
        # Add test data
        test_methods = [
            {
                'name': 'authenticate',
                'source_code': 'def authenticate(user, password): return validate(user, password)',
                'file_path': 'auth.py',
                'class_name': 'Auth',
                'doc_comment': 'Authenticates user credentials'
            },
            {
                'name': 'login',
                'source_code': 'def login(username, pwd): return authenticate(username, pwd)',
                'file_path': 'login.py',
                'class_name': 'Login',
                'doc_comment': 'User login function'
            }
        ]
        
        self.store.add_vectors("_method", test_methods)
        
        # Test search with semantic reranking
        results = self.store.search_hybrid(
            "_method", 
            "user authentication", 
            limit=5,
            enable_semantic_reranking=True
        )
        
        self.assertIsInstance(results, list)
        self.assertGreater(len(results), 0)
    
    def test_search_hybrid_no_results(self):
        """Test search when no results are found."""
        results = self.store.search_hybrid("_method", "nonexistent_function", limit=5)
        
        self.assertIsInstance(results, list)
        self.assertEqual(len(results), 0)
    
    def test_optimize_indexes(self):
        """Test index optimization."""
        # Add some data first
        test_methods = [
            {
                'name': 'testFunction',
                'source_code': 'def testFunction(): pass',
                'file_path': 'test.py',
                'class_name': 'Test',
                'doc_comment': 'Test function'
            }
        ]
        
        self.store.add_vectors("_method", test_methods)
        
        # Test optimization (should not raise exception)
        try:
            self.store.optimize_indexes()
            optimization_success = True
        except Exception:
            optimization_success = False
        
        self.assertTrue(optimization_success)
    
    def test_cleanup_cache(self):
        """Test cache cleanup."""
        # Test cleanup (should not raise exception)
        try:
            self.store.cleanup_cache(max_age_days=0)  # Clean all
            cleanup_success = True
        except Exception:
            cleanup_success = False
        
        self.assertTrue(cleanup_success)
    
    def test_close(self):
        """Test database connection closing."""
        # Test close (should not raise exception)
        try:
            self.store.close()
            close_success = True
        except Exception:
            close_success = False
        
        self.assertTrue(close_success)


if __name__ == "__main__":
    unittest.main()
