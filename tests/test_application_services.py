import unittest
import tempfile
import os
from unittest.mock import <PERSON>Mock

from flow_rag.application.code_indexing import CodeIndexingService
from flow_rag.application.query_processing import QueryProcessingService
from flow_rag.domain.models import Method, Class, QueryResult
from flow_rag.domain.ports.code_parser import <PERSON><PERSON>arser<PERSON>ort
from flow_rag.domain.ports.embeddings import EmbeddingsPort
from flow_rag.domain.ports.vector_store import VectorStorePort
from flow_rag.domain.ports.llm import LLMPort
from flow_rag.domain.ports.file_loader import LanguageEnum

class DummyParser(CodeParserPort):
    def __init__(self):
        self.test_file_created = False

    def load_files(self, codebase_path):
        # Create a test file if it doesn't exist
        test_file = os.path.join(codebase_path, 'foo.py')
        if not self.test_file_created:
            os.makedirs(codebase_path, exist_ok=True)
            with open(test_file, 'w') as f:
                f.write("class Foo:\n    def bar(self):\n        return 'test'\n")
            self.test_file_created = True
        return [(test_file, LanguageEnum.PYTHON)]

    def parse_code_files(self, file_list):
        if file_list:
            file_path = file_list[0][0]
            return (
                [Class(name='Foo', file_path=file_path, source_code='class Foo: pass')],
                [Method(name='bar', file_path=file_path, source_code='def bar(): pass')],
                ['Foo'],
                ['bar']
            )
        return [], [], [], []

    def find_references(self, file_list, class_names, method_names):
        return {'class': {}, 'method': {}}

    def get_supported_languages(self):
        return [LanguageEnum.PYTHON]

    def get_language_from_extension(self, file_ext):
        return LanguageEnum.PYTHON if file_ext == '.py' else None

    def should_ignore_path(self, path):
        return False

class DummyEmbeddings(EmbeddingsPort):
    def __init__(self):
        # Create a mock tokenizer
        self.tokenizer = MagicMock()
        self.tokenizer.encode.return_value = [1, 2, 3, 4, 5]  # Mock 5 tokens

    def embed_methods(self, methods):
        for m in methods:
            m.embeddings = [0.1, 0.2]
        return methods
    def embed_classes(self, classes):
        for c in classes:
            c.embeddings = [0.3, 0.4]
        return classes
    def embed_query(self, query):
        return [0.1, 0.2]
    def embed_special_files(self, special_files):
        return []
    @property
    def get_embedding_dimensions(self):
        return 2
    @property
    def max_token_limit(self):
        return 10
    @property
    def model_name(self):
        return "dummy"
    def clip_text_to_max_tokens(self, text):
        return text

class DummyVectorStore(VectorStorePort):
    def __init__(self):
        self.db = MagicMock()  # Mock database connection

    def connect(self, uri):
        return True
    def add_vectors(self, table, vectors):
        return True
    def search(self, table, vector, limit):
        return [{'name': 'bar', 'file_path': 'foo.py', 'source_code': 'def bar(): pass', 'doc_comment': '', 'class_name': '', 'references': []}]
    def search_hybrid(self, table, text, vector, limit):
        return [{'name': 'bar', 'file_path': 'foo.py', 'source_code': 'def bar(): pass', 'doc_comment': '', 'class_name': '', 'references': []}]
    def rerank(self, table, results, query, reranker=None, reranking_enabled=None):
        return results
    def table_exists(self, table_name):
        return True
    def close(self):
        pass
    def create_table(self, table_name, schema, mode="create"):
        return True

class DummyLLM(LLMPort):
    def send_via_flow(self, user_prompt, system_prompt):
        return "answer"
    def enhance_query(self, query, context=None):
        return query + " enhanced"
    def generate_context(self, query, retrieved_content, rerank):
        return "context"
    def answer_with_context(self, query, context):
        return f"Answer for {query} with {context}"
    def rerank_results(self, query, results):
        return "\n".join(results)
    @property
    def max_context_length(self):
        return 1000
    @property
    def model_name(self):
        return "dummy-llm"

class TestApplicationServices(unittest.TestCase):
    def setUp(self):
        self.parser = DummyParser()
        self.embeddings = DummyEmbeddings()
        self.vector_store = DummyVectorStore()
        self.llm = DummyLLM()
        self.temp_dir = tempfile.TemporaryDirectory()
        self.codebase_path = self.temp_dir.name

        # Create a real test file for load_files_generic to find
        test_file = os.path.join(self.codebase_path, 'test.py')
        with open(test_file, 'w') as f:
            f.write("class TestClass:\n    def test_method(self):\n        return 'test'\n")

        # Create logger
        import logging
        self.logger = logging.getLogger(__name__)
        self.logger.setLevel(logging.WARNING)

    def tearDown(self):
        self.temp_dir.cleanup()

    def test_code_indexing_service(self):
        service = CodeIndexingService(
            code_parser=self.parser,
            embeddings_provider=self.embeddings,
            vector_store=self.vector_store
        )
        metadata = service.index_codebase(self.codebase_path)
        self.assertEqual(metadata.name, os.path.basename(self.codebase_path))
        self.assertEqual(metadata.file_count, 1)
        self.assertEqual(metadata.class_count, 1)
        self.assertEqual(metadata.method_count, 1)

    def test_code_indexing_with_progress(self):
        """Test code indexing with progress callback."""
        progress_calls = []

        def progress_callback(percentage):
            progress_calls.append(percentage)

        service = CodeIndexingService(
            code_parser=self.parser,
            embeddings_provider=self.embeddings,
            vector_store=self.vector_store,
            progress_callback=progress_callback
        )

        metadata = service.index_codebase(self.codebase_path)

        # Check that progress was called
        self.assertTrue(len(progress_calls) > 0)
        self.assertEqual(progress_calls[-1], 100)  # Should end at 100%

        # Check metadata
        self.assertEqual(metadata.name, os.path.basename(self.codebase_path))
        self.assertEqual(metadata.file_count, 1)
        self.assertEqual(metadata.class_count, 1)
        self.assertEqual(metadata.method_count, 1)

    def test_query_processing_service(self):
        """Test QueryProcessingService functionality."""
        service = QueryProcessingService(
            embeddings_provider=self.embeddings,
            vector_store=self.vector_store,
            llm_provider=self.llm,
            logger=self.logger
        )

        result = service.process_query("find bar")

        # Check result structure
        self.assertIsInstance(result, QueryResult)
        self.assertEqual(result.query, "find bar")
        self.assertTrue(result.context)
        self.assertIsInstance(result.metadata, dict)

        # Check metadata contains expected fields
        self.assertIn("processing_time", result.metadata)
        self.assertIn("hyde_enabled", result.metadata)
        self.assertIn("hybrid_search_enabled", result.metadata)

    def test_query_processing_with_hyde_disabled(self):
        """Test QueryProcessingService with HyDE disabled."""
        # Create service (HyDE setting comes from config)
        service = QueryProcessingService(
            embeddings_provider=self.embeddings,
            vector_store=self.vector_store,
            llm_provider=self.llm,
            logger=self.logger
        )

        result = service.process_query("find authentication")

        # Check that result has expected metadata
        self.assertIsInstance(result.metadata.get("hyde_enabled"), bool)
        self.assertEqual(result.metadata.get("search_query"), "find authentication")

    def test_query_processing_performance(self):
        """Test QueryProcessingService performance."""
        service = QueryProcessingService(
            embeddings_provider=self.embeddings,
            vector_store=self.vector_store,
            llm_provider=self.llm,
            logger=self.logger
        )

        import time
        start_time = time.time()
        result = service.process_query("test query")
        processing_time = time.time() - start_time

        # Should complete quickly
        self.assertLess(processing_time, 5.0)

        # Check that processing time is recorded
        self.assertIn("processing_time", result.metadata)
        self.assertGreater(result.metadata["processing_time"], 0)

if __name__ == "__main__":
    unittest.main()
