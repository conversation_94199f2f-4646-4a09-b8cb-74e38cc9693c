import unittest
import tempfile
import os
import logging

from flow_rag.adapters.code_parsers.optimized_vb6_parser import OptimizedVB6CodeParser
from flow_rag.domain.ports.file_loader import LanguageEnum


class TestOptimizedVB6CodeParser(unittest.TestCase):
    """Test cases for OptimizedVB6CodeParser."""
    
    def setUp(self):
        """Set up test fixtures."""
        # Setup logger
        self.logger = logging.getLogger(__name__)
        self.logger.setLevel(logging.WARNING)  # Reduce noise in tests
        
        # Create parser instance
        self.parser = OptimizedVB6CodeParser(logger=self.logger)
        
        # Create temporary directory for test files
        self.temp_dir = tempfile.mkdtemp()
    
    def tearDown(self):
        """Clean up test fixtures."""
        import shutil
        try:
            shutil.rmtree(self.temp_dir, ignore_errors=True)
        except:
            pass
    
    def test_get_supported_languages(self):
        """Test supported languages."""
        supported = self.parser.get_supported_languages()
        self.assertIn(LanguageEnum.VB6, supported)
    
    def test_parse_simple_vb6_form(self):
        """Test parsing a simple VB6 form file."""
        vb6_content = """VERSION 5.00
Begin VB.Form frmTest
   Caption         =   "Test Form"
   ClientHeight    =   3195
   ClientWidth     =   4680
End

Public Function GetUserName() As String
    GetUserName = "John Doe"
End Function

Private Sub Form_Load()
    Call GetUserName
End Sub

Private Function ValidateInput(inputText As String) As Boolean
    If Len(inputText) > 0 Then
        ValidateInput = True
    Else
        ValidateInput = False
    End If
End Function
"""
        
        # Create test file
        test_file = os.path.join(self.temp_dir, "test.frm")
        with open(test_file, 'w', encoding='utf-8') as f:
            f.write(vb6_content)
        
        # Parse the file
        classes, methods, class_names, method_names = self.parser.parse_code_files(
            [(test_file, LanguageEnum.VB6)]
        )
        
        # Check results
        self.assertEqual(len(methods), 3)  # GetUserName, Form_Load, ValidateInput
        self.assertIn("GetUserName", method_names)
        self.assertIn("Form_Load", method_names)
        self.assertIn("ValidateInput", method_names)
        
        # Check method details
        get_user_method = next((m for m in methods if m.name == "GetUserName"), None)
        self.assertIsNotNone(get_user_method)
        self.assertEqual(get_user_method.file_path, test_file)
        self.assertIn("GetUserName = \"John Doe\"", get_user_method.source_code)
    
    def test_parse_vb6_module(self):
        """Test parsing a VB6 module file."""
        vb6_content = """Attribute VB_Name = "ModuleUtils"
Option Explicit

Public Function CalculateSum(a As Integer, b As Integer) As Integer
    CalculateSum = a + b
End Function

Public Sub ShowMessage(msg As String)
    MsgBox msg
End Sub

Private Function InternalHelper() As String
    InternalHelper = "Helper"
End Function
"""
        
        # Create test file
        test_file = os.path.join(self.temp_dir, "utils.bas")
        with open(test_file, 'w', encoding='utf-8') as f:
            f.write(vb6_content)
        
        # Parse the file
        classes, methods, class_names, method_names = self.parser.parse_code_files(
            [(test_file, LanguageEnum.VB6)]
        )
        
        # Check results
        self.assertEqual(len(methods), 3)  # CalculateSum, ShowMessage, InternalHelper
        self.assertIn("CalculateSum", method_names)
        self.assertIn("ShowMessage", method_names)
        self.assertIn("InternalHelper", method_names)
        
        # Check method details
        calc_method = next((m for m in methods if m.name == "CalculateSum"), None)
        self.assertIsNotNone(calc_method)
        self.assertEqual(calc_method.file_path, test_file)
        self.assertIn("CalculateSum = a + b", calc_method.source_code)
    
    def test_parse_vb6_class(self):
        """Test parsing a VB6 class file."""
        vb6_content = """VERSION 1.0 CLASS
Attribute VB_Name = "UserClass"
Attribute VB_GlobalNameSpace = False

Private userName As String
Private userAge As Integer

Public Property Get Name() As String
    Name = userName
End Property

Public Property Let Name(value As String)
    userName = value
End Property

Public Function GetAge() As Integer
    GetAge = userAge
End Function

Public Sub SetAge(age As Integer)
    userAge = age
End Sub
"""
        
        # Create test file
        test_file = os.path.join(self.temp_dir, "user.cls")
        with open(test_file, 'w', encoding='utf-8') as f:
            f.write(vb6_content)
        
        # Parse the file
        classes, methods, class_names, method_names = self.parser.parse_code_files(
            [(test_file, LanguageEnum.VB6)]
        )
        
        # Check results - Properties and methods should be found
        self.assertGreater(len(methods), 0)
        
        # Check for property methods
        property_methods = [m.name for m in methods if "Name" in m.name or "Age" in m.name]
        self.assertGreater(len(property_methods), 0)
    
    def test_parse_empty_file(self):
        """Test parsing an empty VB6 file."""
        # Create empty test file
        test_file = os.path.join(self.temp_dir, "empty.frm")
        with open(test_file, 'w', encoding='utf-8') as f:
            f.write("")
        
        # Parse the file
        classes, methods, class_names, method_names = self.parser.parse_code_files(
            [(test_file, LanguageEnum.VB6)]
        )
        
        # Check results
        self.assertEqual(len(methods), 0)
        self.assertEqual(len(method_names), 0)
    
    def test_parse_multiple_files(self):
        """Test parsing multiple VB6 files."""
        # Create first file
        file1_content = """Public Function Function1() As String
    Function1 = "File1"
End Function"""
        
        file1 = os.path.join(self.temp_dir, "file1.frm")
        with open(file1, 'w', encoding='utf-8') as f:
            f.write(file1_content)
        
        # Create second file
        file2_content = """Public Function Function2() As String
    Function2 = "File2"
End Function"""
        
        file2 = os.path.join(self.temp_dir, "file2.bas")
        with open(file2, 'w', encoding='utf-8') as f:
            f.write(file2_content)
        
        # Parse both files
        classes, methods, class_names, method_names = self.parser.parse_code_files([
            (file1, LanguageEnum.VB6),
            (file2, LanguageEnum.VB6)
        ])
        
        # Check results
        self.assertEqual(len(methods), 2)
        self.assertIn("Function1", method_names)
        self.assertIn("Function2", method_names)
        
        # Check file paths
        method1 = next((m for m in methods if m.name == "Function1"), None)
        method2 = next((m for m in methods if m.name == "Function2"), None)
        
        self.assertEqual(method1.file_path, file1)
        self.assertEqual(method2.file_path, file2)
    
    def test_find_references(self):
        """Test reference finding functionality."""
        # Create test file with function calls
        vb6_content = """Public Function MainFunction() As String
    Dim result As String
    result = HelperFunction("test")
    Call AnotherFunction
    MainFunction = result
End Function

Public Function HelperFunction(param As String) As String
    HelperFunction = param
End Function

Public Sub AnotherFunction()
    ' Do something
End Sub"""
        
        test_file = os.path.join(self.temp_dir, "test.frm")
        with open(test_file, 'w', encoding='utf-8') as f:
            f.write(vb6_content)
        
        # Parse the file first
        classes, methods, class_names, method_names = self.parser.parse_code_files(
            [(test_file, LanguageEnum.VB6)]
        )
        
        # Test reference finding
        references = self.parser.find_references(
            [(test_file, LanguageEnum.VB6)],
            class_names,
            method_names
        )
        
        # Check that references were found
        self.assertIsInstance(references, dict)
        self.assertIn('method', references)
        
        # Check for specific references
        method_refs = references.get('method', {})
        if 'HelperFunction' in method_refs:
            helper_refs = method_refs['HelperFunction']
            self.assertGreater(len(helper_refs), 0)
    
    def test_performance_with_large_content(self):
        """Test parser performance with larger content."""
        # Generate a larger VB6 file
        large_content = "VERSION 5.00\nBegin VB.Form frmLarge\nEnd\n\n"
        
        for i in range(50):
            large_content += f"""
Public Function Function{i}() As String
    Dim temp As String
    temp = "Function {i}"
    Function{i} = temp
End Function
"""
        
        # Create test file
        test_file = os.path.join(self.temp_dir, "large.frm")
        with open(test_file, 'w', encoding='utf-8') as f:
            f.write(large_content)
        
        # Parse the file and measure time
        import time
        start_time = time.time()
        
        classes, methods, class_names, method_names = self.parser.parse_code_files(
            [(test_file, LanguageEnum.VB6)]
        )
        
        parse_time = time.time() - start_time
        
        # Check results
        self.assertEqual(len(methods), 50)
        self.assertLess(parse_time, 1.0)  # Should be fast (< 1 second)


if __name__ == "__main__":
    unittest.main()
