import requests
import json
import threading
import time
from datetime import datetime
import logging

from tenacity import retry, stop_after_attempt, wait_exponential, retry_if_exception_type

from flow_api.configuracoes import flow_tenant, client_id, client_secret, app_to_access

logger = logging.getLogger(__name__)

class FlowToken:
    token = None
    token_expiration = None
    lock = threading.Lock()

    @classmethod
    @retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=5, min=5, max=15),
           retry=retry_if_exception_type(requests.RequestException))
    def __obter_token(cls):
        """Obtém o token de autenticação e define a variável de classe token."""

        current_time_seconds = time.time()
        current_time = datetime.fromtimestamp(current_time_seconds)
        formatted_time = current_time.strftime('%H:%M')

        print(f"{formatted_time} - Obtendo token Flow API...")
        logger.debug("Iniciando obtenção do token Flow API...")

        url = 'https://flow.ciandt.com/auth-engine-api/v1/api-key/token'
        headers = {
            'accept': '/',
            'Content-Type': 'application/json',
            'FlowTenant': flow_tenant
        }
        data = {
            "clientId": client_id,
            "clientSecret": client_secret,
            "appToAccess": app_to_access
        }

        logger.debug(f"URL da requisição: {url}")
        logger.debug(f"Headers da requisição: {json.dumps(headers, indent=2)}")
        logger.debug(f"Dados da requisição: {json.dumps(data, indent=2)}")

        try:
            response = requests.post(url, headers=headers, data=json.dumps(data))
            logger.debug(f"Status da resposta: {response.status_code}")
            logger.debug(f"Headers da resposta: {json.dumps(dict(response.headers), indent=2)}")
            
            if response.status_code != 200:
                error_msg = f"Erro na resposta: {response.text}"
                print(f"ERRO: {error_msg}")
                logger.error(error_msg)
                response.raise_for_status()
            
            token_data = response.json()
            logger.debug(f"Resposta completa: {json.dumps(token_data, indent=2)}")
            
            if 'access_token' not in token_data:
                error_msg = "Token não encontrado na resposta"
                print(f"ERRO: {error_msg}")
                logger.error(error_msg)
                raise ValueError(error_msg)
            
            cls.token = token_data['access_token']
            cls.token_expiration = time.time() + 3300  # Token expira em 3600 segundos
            
            print("Token obtido com sucesso!")
            logger.debug(f"Token obtido com sucesso. Expira em: {datetime.fromtimestamp(cls.token_expiration).strftime('%H:%M:%S')}")
            
        except requests.exceptions.RequestException as e:
            error_msg = f"Erro na requisição: {str(e)}"
            print(f"ERRO: {error_msg}")
            logger.error(error_msg)
            raise
        except json.JSONDecodeError as e:
            error_msg = f"Erro ao decodificar JSON da resposta: {str(e)}"
            print(f"ERRO: {error_msg}")
            logger.error(error_msg)
            raise
        except Exception as e:
            error_msg = f"Erro inesperado: {str(e)}"
            print(f"ERRO: {error_msg}")
            logger.error(error_msg)
            raise

    @classmethod
    def get_valid_token(cls):
        """Retorna um token válido, renovando se necessário, e agenda a renovação automática."""
        with cls.lock:
            current_time = time.time()
            if cls.token is None:
                logger.debug("Token não existe. Obtendo novo token...")
                cls.__obter_token()
            elif current_time >= cls.token_expiration:
                logger.debug(f"Token expirado em {datetime.fromtimestamp(cls.token_expiration).strftime('%H:%M:%S')}. Obtendo novo token...")
                cls.__obter_token()
            else:
                logger.debug(f"Usando token existente. Expira em: {datetime.fromtimestamp(cls.token_expiration).strftime('%H:%M:%S')}")

            return cls.token