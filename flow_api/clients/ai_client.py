import json
from abc import ABC, abstractmethod
import logging
import requests
from tenacity import retry, stop_after_attempt, wait_exponential, retry_if_exception_type

from flow_api.configuracoes import flow_tenant
from flow_api.token import FlowToken

logger = logging.getLogger(__name__)

class AIClient(ABC):

    @abstractmethod
    def _get_url(self) -> str:
        pass

    @abstractmethod
    def _get_system_role(self) -> str:
        pass

    def _get_headers(self, token: str) -> dict:
        return {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            'FlowTenant': flow_tenant,
            'FlowAgent': 'mos-bradesco',
            'Authorization': f'Bearer {token}'
        }

    def _get_messages(self, sys_prompt: str, user_prompt: str) -> list:
        return [
            {"role": self._get_system_role(), "content": sys_prompt},
            {"role": "user", "content": user_prompt}
        ]

    def _get_data(self, sys_prompt: str, user_prompt: str) -> dict:
        data = {
            "messages": self._get_messages(sys_prompt, user_prompt),
            "max_tokens": 4096,
            "temperature": 0
        }
        logger.debug(f"Request data: {json.dumps(data, indent=2)}")
        return data

    def _handle_response(self, response) -> str:
        choices = response.get('choices', [])
        if not choices:
            logger.error(f"Response without choices: {json.dumps(response, indent=2)}")
            raise ValueError("Resposta da API não contém 'choices'.")
        content = ""
        for choice in choices:
            message = choice.get('message', {})
            part_content = message.get('content', '')
            if not part_content:
                logger.error(f"Response with empty content: {json.dumps(response, indent=2)}")
                raise ValueError("Resposta da API contém 'content' vazio ou inválido.")
            content += part_content
        return content

    @retry(stop=stop_after_attempt(2), wait=wait_exponential(multiplier=10, min=10, max=50), retry=retry_if_exception_type((requests.exceptions.RequestException, ValueError)))
    def send(self, sys_prompt: str, user_prompt: str) -> str | None:
        token = FlowToken().get_valid_token()
        url = self._get_url()
        headers = self._get_headers(token)
        data = self._get_data(sys_prompt, user_prompt)
        
        logger.debug(f"Request URL: {url}")
        logger.debug(f"Request headers: {json.dumps(headers, indent=2)}")
        
        try:
            resp = requests.post(url, headers=headers, data=json.dumps(data))
            logger.debug(f"Response status: {resp.status_code}")
            logger.debug(f"Response headers: {json.dumps(dict(resp.headers), indent=2)}")
            
            if resp.status_code != 200:
                logger.error(f"Error response: {resp.text}")
            
            resp.raise_for_status()
            result = resp.json()
            return self._handle_response(result)
        except (requests.exceptions.HTTPError, requests.exceptions.RequestException, ValueError) as exc:
            logger.error(f"Exception in send: {str(exc)}")
            raise exc

class FlowAIClient(AIClient):
    def _get_url(self) -> str:
        return "https://flow.ciandt.com/ai-orchestration-api/v1/openai/chat/completions"

    def _get_system_role(self) -> str:
        return "system"

    def _get_data(self, sys_prompt: str, user_prompt: str) -> dict:
        data = super()._get_data(sys_prompt, user_prompt)
        data["model"] = "gpt-4o-mini"  # Adiciona o modelo específico do Flow
        return data
