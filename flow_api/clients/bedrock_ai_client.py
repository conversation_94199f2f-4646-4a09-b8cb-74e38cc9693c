from flow_api.clients.ai_client import AIClient


class BedrockAIClient(AIClient):
    def _get_url(self) -> str:
        return 'https://flow.ciandt.com/ai-orchestration-api/v1/bedrock/invoke'

    def _get_system_role(self) -> str:
        return "assistant"

    def _handle_response(self, response) -> str:
        if 'content' not in response:
            raise ValueError("Resposta da API não contém 'content'.")

        content_items = response.get('content', [])
        if not content_items:
            raise ValueError("Resposta da API contém 'content' vazio.")

        content = ""
        for item in content_items:
            if item.get('type') == 'text':
                part_content = item.get('text', '')
                if part_content:
                    content += part_content

        if not content:
            raise ValueError("Resposta da API não contém texto válido.")

        return content