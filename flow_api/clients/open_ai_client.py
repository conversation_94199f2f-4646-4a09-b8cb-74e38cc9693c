from flow_api.clients.ai_client import AIClient
from flow_rag.infrastructure.log_utils import log_message


class OpenAIClient(AIClient):
    def _get_url(self) -> str:
        return 'https://flow.ciandt.com/ai-orchestration-api/v1/openai/chat/completions'

    def _get_data(self, sys_prompt: str, user_prompt: str) -> dict:
        data = super()._get_data(sys_prompt, user_prompt)
        # Adiciona o modelo específico para a API Flow
        data["model"] = "gpt-4o-mini"
        return data

    def _get_system_role(self) -> str:
        return "system"