{"data_mtime": 1750695898, "dep_lines": [1, 2, 4, 5, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 30, 30], "dependencies": ["re", "sre_constants", "sre_parse", "typing", "builtins", "_frozen_importlib", "abc"], "hash": "314498570402673c11acd537a3e6edacc0ed0b05", "id": "sre_compile", "ignore_all": true, "interface_hash": "33c11301e7b5c61e0da40944ded42c1f8b3798af", "mtime": 1750435055, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/home/<USER>/.pyenv/versions/3.12.5/lib/python3.12/site-packages/mypy/typeshed/stdlib/sre_compile.pyi", "plugin_data": null, "size": 332, "suppressed": [], "version_id": "1.16.1"}