{"data_mtime": 1750695898, "dep_lines": [5, 6, 7, 8, 9, 10, 11, 12, 498, 1, 1], "dep_prios": [10, 10, 5, 5, 5, 5, 5, 5, 5, 5, 30], "dependencies": ["collections", "sys", "typing_extensions", "_collections_abc", "_typeshed", "abc", "re", "types", "contextlib", "builtins", "_frozen_importlib"], "hash": "cde1bb70f528d6484d7aaeb84c81f32eccbb59a9", "id": "typing", "ignore_all": true, "interface_hash": "61bacfcd171fd3e573a345e556516711e37ba176", "mtime": 1750435055, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/home/<USER>/.pyenv/versions/3.12.5/lib/python3.12/site-packages/mypy/typeshed/stdlib/typing.pyi", "plugin_data": null, "size": 37973, "suppressed": [], "version_id": "1.16.1"}