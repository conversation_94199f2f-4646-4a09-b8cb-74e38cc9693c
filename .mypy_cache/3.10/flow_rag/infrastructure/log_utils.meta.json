{"data_mtime": 1750696940, "dep_lines": [5, 1, 2, 3, 4, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 10, 10, 5, 5, 5, 30, 30, 30, 30, 30, 30], "dependencies": ["flow_rag.infrastructure.config", "logging", "os", "datetime", "typing", "builtins", "_frozen_importlib", "_typeshed", "abc", "pathlib", "types", "typing_extensions"], "hash": "4f41df9f7a51469af67abbeb54ec0dd1da042797", "id": "flow_rag.infrastructure.log_utils", "ignore_all": false, "interface_hash": "eb35961789ecd58e94b44522cc49ecbc7724d626", "mtime": 1750696674, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "flow_rag/infrastructure/log_utils.py", "plugin_data": null, "size": 2526, "suppressed": [], "version_id": "1.16.1"}