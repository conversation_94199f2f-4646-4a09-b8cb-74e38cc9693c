{"data_mtime": 1750696708, "dep_lines": [6, 7, 8, 9, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 10, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["typing", "collections", "logging", "dataclasses", "builtins", "_collections_abc", "_frozen_importlib", "_io", "_typeshed", "abc", "io", "os", "types", "typing_extensions"], "hash": "f9f1fbdcc9dc918adb0c8eb6767dec04550925a6", "id": "flow_rag.utils.simple_reference_discovery", "ignore_all": false, "interface_hash": "3732d15533558d1f296ae78d96700d7f45fded17", "mtime": 1750696610, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "flow_rag/utils/simple_reference_discovery.py", "plugin_data": null, "size": 7147, "suppressed": [], "version_id": "1.16.1"}