{".class": "MypyFile", "_fullname": "flow_rag.utils.simple_reference_discovery", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "Set": {".class": "SymbolTableNode", "cross_ref": "typing.Set", "kind": "Gdef"}, "SimpleReference": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "flow_rag.utils.simple_reference_discovery.SimpleReference", "name": "SimpleReference", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "flow_rag.utils.simple_reference_discovery.SimpleReference", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 13, "name": "symbol_name", "type": "builtins.str"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 14, "name": "file_path", "type": "builtins.str"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 15, "name": "line_number", "type": "builtins.int"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 16, "name": "context", "type": "builtins.str"}], "frozen": false}, "dataclass_tag": {}}, "module_name": "flow_rag.utils.simple_reference_discovery", "mro": ["flow_rag.utils.simple_reference_discovery.SimpleReference", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "flow_rag.utils.simple_reference_discovery.SimpleReference.__dataclass_fields__", "name": "__dataclass_fields__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "dataclasses.Field"}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "symbol_name", "file_path", "line_number", "context"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "flow_rag.utils.simple_reference_discovery.SimpleReference.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "symbol_name", "file_path", "line_number", "context"], "arg_types": ["flow_rag.utils.simple_reference_discovery.SimpleReference", "builtins.str", "builtins.str", "builtins.int", "builtins.str"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of SimpleReference", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "flow_rag.utils.simple_reference_discovery.SimpleReference.__match_args__", "name": "__match_args__", "setter_type": null, "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "symbol_name"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "file_path"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "line_number"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "context"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5, 5, 5], "arg_names": ["symbol_name", "file_path", "line_number", "context"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "flow_rag.utils.simple_reference_discovery.SimpleReference.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5], "arg_names": ["symbol_name", "file_path", "line_number", "context"], "arg_types": ["builtins.str", "builtins.str", "builtins.int", "builtins.str"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__mypy-replace of SimpleReference", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "flow_rag.utils.simple_reference_discovery.SimpleReference.__mypy-replace", "name": "__mypy-replace", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5], "arg_names": ["symbol_name", "file_path", "line_number", "context"], "arg_types": ["builtins.str", "builtins.str", "builtins.int", "builtins.str"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__mypy-replace of SimpleReference", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "context": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "flow_rag.utils.simple_reference_discovery.SimpleReference.context", "name": "context", "setter_type": null, "type": "builtins.str"}}, "file_path": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "flow_rag.utils.simple_reference_discovery.SimpleReference.file_path", "name": "file_path", "setter_type": null, "type": "builtins.str"}}, "line_number": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "flow_rag.utils.simple_reference_discovery.SimpleReference.line_number", "name": "line_number", "setter_type": null, "type": "builtins.int"}}, "symbol_name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "flow_rag.utils.simple_reference_discovery.SimpleReference.symbol_name", "name": "symbol_name", "setter_type": null, "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "flow_rag.utils.simple_reference_discovery.SimpleReference.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "flow_rag.utils.simple_reference_discovery.SimpleReference", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SimpleReferenceDiscovery": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "flow_rag.utils.simple_reference_discovery.SimpleReferenceDiscovery", "name": "SimpleReferenceDiscovery", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "flow_rag.utils.simple_reference_discovery.SimpleReferenceDiscovery", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "flow_rag.utils.simple_reference_discovery", "mro": ["flow_rag.utils.simple_reference_discovery.SimpleReferenceDiscovery", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "logger"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "flow_rag.utils.simple_reference_discovery.SimpleReferenceDiscovery.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "logger"], "arg_types": ["flow_rag.utils.simple_reference_discovery.SimpleReferenceDiscovery", {".class": "UnionType", "items": ["logging.Logger", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of SimpleReferenceDiscovery", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_find_references_in_file_simple": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "file_path", "symbol_names"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "flow_rag.utils.simple_reference_discovery.SimpleReferenceDiscovery._find_references_in_file_simple", "name": "_find_references_in_file_simple", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "file_path", "symbol_names"], "arg_types": ["flow_rag.utils.simple_reference_discovery.SimpleReferenceDiscovery", "builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_find_references_in_file_simple of SimpleReferenceDiscovery", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["flow_rag.utils.simple_reference_discovery.SimpleReference"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "build_symbol_index": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "files_and_symbols"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "flow_rag.utils.simple_reference_discovery.SimpleReferenceDiscovery.build_symbol_index", "name": "build_symbol_index", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "files_and_symbols"], "arg_types": ["flow_rag.utils.simple_reference_discovery.SimpleReferenceDiscovery", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.list"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "build_symbol_index of SimpleReferenceDiscovery", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "find_references_simple": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "file_list"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "flow_rag.utils.simple_reference_discovery.SimpleReferenceDiscovery.find_references_simple", "name": "find_references_simple", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "file_list"], "arg_types": ["flow_rag.utils.simple_reference_discovery.SimpleReferenceDiscovery", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "find_references_simple of SimpleReferenceDiscovery", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_statistics": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "flow_rag.utils.simple_reference_discovery.SimpleReferenceDiscovery.get_statistics", "name": "get_statistics", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["flow_rag.utils.simple_reference_discovery.SimpleReferenceDiscovery"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_statistics of SimpleReferenceDiscovery", "ret_type": {".class": "Instance", "args": ["builtins.str", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "logger": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "flow_rag.utils.simple_reference_discovery.SimpleReferenceDiscovery.logger", "name": "logger", "setter_type": null, "type": {".class": "UnionType", "items": ["logging.Logger", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "symbol_files": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "flow_rag.utils.simple_reference_discovery.SimpleReferenceDiscovery.symbol_files", "name": "symbol_files", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "flow_rag.utils.simple_reference_discovery.SimpleReferenceDiscovery.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "flow_rag.utils.simple_reference_discovery.SimpleReferenceDiscovery", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "flow_rag.utils.simple_reference_discovery.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "flow_rag.utils.simple_reference_discovery.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "flow_rag.utils.simple_reference_discovery.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "flow_rag.utils.simple_reference_discovery.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "flow_rag.utils.simple_reference_discovery.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "flow_rag.utils.simple_reference_discovery.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "create_simple_reference_discovery": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["classes", "methods", "logger"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "flow_rag.utils.simple_reference_discovery.create_simple_reference_discovery", "name": "create_simple_reference_discovery", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["classes", "methods", "logger"], "arg_types": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "UnionType", "items": ["logging.Logger", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create_simple_reference_discovery", "ret_type": "flow_rag.utils.simple_reference_discovery.SimpleReferenceDiscovery", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "dataclass": {".class": "SymbolTableNode", "cross_ref": "dataclasses.dataclass", "kind": "Gdef"}, "defaultdict": {".class": "SymbolTableNode", "cross_ref": "collections.defaultdict", "kind": "Gdef"}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef"}}, "path": "flow_rag/utils/simple_reference_discovery.py"}