{"data_mtime": 1750696708, "dep_lines": [6, 7, 8, 9, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 10, 5, 30, 30, 30, 30], "dependencies": ["typing", "dataclasses", "enum", "re", "builtins", "_frozen_importlib", "_typeshed", "abc", "typing_extensions"], "hash": "68b437da8d3d221c67a8b7daf23e7f56952110f2", "id": "flow_rag.utils.smart_chunking", "ignore_all": false, "interface_hash": "fe3cfa4a27f7f4434ef0e47764d3fb89d58c8bab", "mtime": 1750696546, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "flow_rag/utils/smart_chunking.py", "plugin_data": null, "size": 10807, "suppressed": [], "version_id": "1.16.1"}