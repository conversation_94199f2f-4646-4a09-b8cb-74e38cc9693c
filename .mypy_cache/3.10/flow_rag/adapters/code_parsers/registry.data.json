{".class": "MypyFile", "_fullname": "flow_rag.adapters.code_parsers.registry", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "CodeParserPort": {".class": "SymbolTableNode", "cross_ref": "flow_rag.domain.ports.code_parser.CodeParserPort", "kind": "Gdef"}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "LanguageEnum": {".class": "SymbolTableNode", "cross_ref": "flow_rag.domain.ports.file_loader.LanguageEnum", "kind": "Gdef"}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "ParserRegistry": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "flow_rag.adapters.code_parsers.registry.ParserRegistry", "name": "ParserRegistry", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "flow_rag.adapters.code_parsers.registry.ParserRegistry", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "flow_rag.adapters.code_parsers.registry", "mro": ["flow_rag.adapters.code_parsers.registry.ParserRegistry", "builtins.object"], "names": {".class": "SymbolTable", "_parsers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "flow_rag.adapters.code_parsers.registry.ParserRegistry._parsers", "name": "_parsers", "setter_type": null, "type": {".class": "Instance", "args": ["flow_rag.domain.ports.file_loader.LanguageEnum", "flow_rag.domain.ports.code_parser.CodeParserPort"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "get_parser": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "language"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated", "is_trivial_self"], "fullname": "flow_rag.adapters.code_parsers.registry.ParserRegistry.get_parser", "name": "get_parser", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "language"], "arg_types": [{".class": "TypeType", "item": "flow_rag.adapters.code_parsers.registry.ParserRegistry"}, "flow_rag.domain.ports.file_loader.LanguageEnum"], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_parser of ParserRegistry", "ret_type": {".class": "UnionType", "items": ["flow_rag.domain.ports.code_parser.CodeParserPort", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "flow_rag.adapters.code_parsers.registry.ParserRegistry.get_parser", "name": "get_parser", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "language"], "arg_types": [{".class": "TypeType", "item": "flow_rag.adapters.code_parsers.registry.ParserRegistry"}, "flow_rag.domain.ports.file_loader.LanguageEnum"], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_parser of ParserRegistry", "ret_type": {".class": "UnionType", "items": ["flow_rag.domain.ports.code_parser.CodeParserPort", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "get_supported_languages": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["cls"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated", "is_trivial_self"], "fullname": "flow_rag.adapters.code_parsers.registry.ParserRegistry.get_supported_languages", "name": "get_supported_languages", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": "flow_rag.adapters.code_parsers.registry.ParserRegistry"}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_supported_languages of ParserRegistry", "ret_type": {".class": "Instance", "args": ["flow_rag.domain.ports.file_loader.LanguageEnum"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "flow_rag.adapters.code_parsers.registry.ParserRegistry.get_supported_languages", "name": "get_supported_languages", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": "flow_rag.adapters.code_parsers.registry.ParserRegistry"}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_supported_languages of ParserRegistry", "ret_type": {".class": "Instance", "args": ["flow_rag.domain.ports.file_loader.LanguageEnum"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "register": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["cls", "language", "parser_class"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated", "is_trivial_self"], "fullname": "flow_rag.adapters.code_parsers.registry.ParserRegistry.register", "name": "register", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["cls", "language", "parser_class"], "arg_types": [{".class": "TypeType", "item": "flow_rag.adapters.code_parsers.registry.ParserRegistry"}, "flow_rag.domain.ports.file_loader.LanguageEnum", {".class": "TypeType", "item": "flow_rag.domain.ports.code_parser.CodeParserPort"}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "register of ParserRegistry", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "flow_rag.adapters.code_parsers.registry.ParserRegistry.register", "name": "register", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["cls", "language", "parser_class"], "arg_types": [{".class": "TypeType", "item": "flow_rag.adapters.code_parsers.registry.ParserRegistry"}, "flow_rag.domain.ports.file_loader.LanguageEnum", {".class": "TypeType", "item": "flow_rag.domain.ports.code_parser.CodeParserPort"}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "register of ParserRegistry", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "flow_rag.adapters.code_parsers.registry.ParserRegistry.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "flow_rag.adapters.code_parsers.registry.ParserRegistry", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Type": {".class": "SymbolTableNode", "cross_ref": "typing.Type", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "flow_rag.adapters.code_parsers.registry.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "flow_rag.adapters.code_parsers.registry.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "flow_rag.adapters.code_parsers.registry.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "flow_rag.adapters.code_parsers.registry.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "flow_rag.adapters.code_parsers.registry.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "flow_rag.adapters.code_parsers.registry.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}}, "path": "flow_rag/adapters/code_parsers/registry.py"}