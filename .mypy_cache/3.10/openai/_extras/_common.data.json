{".class": "MypyFile", "_fullname": "openai._extras._common", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "INSTRUCTIONS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "openai._extras._common.INSTRUCTIONS", "name": "INSTRUCTIONS", "setter_type": null, "type": "builtins.str"}}, "MissingDependencyError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["openai._exceptions.OpenAIError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "openai._extras._common.MissingDependencyError", "name": "MissingDependencyError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "openai._extras._common.MissingDependencyError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "openai._extras._common", "mro": ["openai._extras._common.MissingDependencyError", "openai._exceptions.OpenAIError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai._extras._common.MissingDependencyError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "openai._extras._common.MissingDependencyError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "OpenAIError": {".class": "SymbolTableNode", "cross_ref": "openai._exceptions.OpenAIError", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai._extras._common.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai._extras._common.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai._extras._common.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai._extras._common.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai._extras._common.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai._extras._common.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "format_instructions": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [3, 3], "arg_names": ["library", "extra"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "openai._extras._common.format_instructions", "name": "format_instructions", "type": {".class": "CallableType", "arg_kinds": [3, 3], "arg_names": ["library", "extra"], "arg_types": ["builtins.str", "builtins.str"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "format_instructions", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "path": "/home/<USER>/.pyenv/versions/3.12.5/lib/python3.12/site-packages/openai/_extras/_common.py"}