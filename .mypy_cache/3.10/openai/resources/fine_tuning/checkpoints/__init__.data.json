{".class": "MypyFile", "_fullname": "openai.resources.fine_tuning.checkpoints", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AsyncCheckpoints": {".class": "SymbolTableNode", "cross_ref": "openai.resources.fine_tuning.checkpoints.checkpoints.AsyncCheckpoints", "kind": "Gdef"}, "AsyncCheckpointsWithRawResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.fine_tuning.checkpoints.checkpoints.AsyncCheckpointsWithRawResponse", "kind": "Gdef"}, "AsyncCheckpointsWithStreamingResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.fine_tuning.checkpoints.checkpoints.AsyncCheckpointsWithStreamingResponse", "kind": "Gdef"}, "AsyncPermissions": {".class": "SymbolTableNode", "cross_ref": "openai.resources.fine_tuning.checkpoints.permissions.AsyncPermissions", "kind": "Gdef"}, "AsyncPermissionsWithRawResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.fine_tuning.checkpoints.permissions.AsyncPermissionsWithRawResponse", "kind": "Gdef"}, "AsyncPermissionsWithStreamingResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.fine_tuning.checkpoints.permissions.AsyncPermissionsWithStreamingResponse", "kind": "Gdef"}, "Checkpoints": {".class": "SymbolTableNode", "cross_ref": "openai.resources.fine_tuning.checkpoints.checkpoints.Checkpoints", "kind": "Gdef"}, "CheckpointsWithRawResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.fine_tuning.checkpoints.checkpoints.CheckpointsWithRawResponse", "kind": "Gdef"}, "CheckpointsWithStreamingResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.fine_tuning.checkpoints.checkpoints.CheckpointsWithStreamingResponse", "kind": "Gdef"}, "Permissions": {".class": "SymbolTableNode", "cross_ref": "openai.resources.fine_tuning.checkpoints.permissions.Permissions", "kind": "Gdef"}, "PermissionsWithRawResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.fine_tuning.checkpoints.permissions.PermissionsWithRawResponse", "kind": "Gdef"}, "PermissionsWithStreamingResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.fine_tuning.checkpoints.permissions.PermissionsWithStreamingResponse", "kind": "Gdef"}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "openai.resources.fine_tuning.checkpoints.__all__", "name": "__all__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.resources.fine_tuning.checkpoints.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.resources.fine_tuning.checkpoints.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.resources.fine_tuning.checkpoints.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.resources.fine_tuning.checkpoints.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.resources.fine_tuning.checkpoints.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.resources.fine_tuning.checkpoints.__path__", "name": "__path__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.resources.fine_tuning.checkpoints.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}}, "path": "/home/<USER>/.pyenv/versions/3.12.5/lib/python3.12/site-packages/openai/resources/fine_tuning/checkpoints/__init__.py"}