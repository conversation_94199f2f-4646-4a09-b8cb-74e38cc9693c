{".class": "MypyFile", "_fullname": "openai.resources.fine_tuning.checkpoints.checkpoints", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AsyncAPIResource": {".class": "SymbolTableNode", "cross_ref": "openai._resource.AsyncAPIResource", "kind": "Gdef", "module_public": false}, "AsyncCheckpoints": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["openai._resource.AsyncAPIResource"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "openai.resources.fine_tuning.checkpoints.checkpoints.AsyncCheckpoints", "name": "AsyncCheckpoints", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "openai.resources.fine_tuning.checkpoints.checkpoints.AsyncCheckpoints", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "openai.resources.fine_tuning.checkpoints.checkpoints", "mro": ["openai.resources.fine_tuning.checkpoints.checkpoints.AsyncCheckpoints", "openai._resource.AsyncAPIResource", "builtins.object"], "names": {".class": "SymbolTable", "permissions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai.resources.fine_tuning.checkpoints.checkpoints.AsyncCheckpoints.permissions", "name": "permissions", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.resources.fine_tuning.checkpoints.checkpoints.AsyncCheckpoints"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "permissions of AsyncCheckpoints", "ret_type": "openai.resources.fine_tuning.checkpoints.permissions.AsyncPermissions", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai.resources.fine_tuning.checkpoints.checkpoints.AsyncCheckpoints.permissions", "name": "permissions", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.resources.fine_tuning.checkpoints.checkpoints.AsyncCheckpoints"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "permissions of AsyncCheckpoints", "ret_type": "openai.resources.fine_tuning.checkpoints.permissions.AsyncPermissions", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "with_raw_response": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai.resources.fine_tuning.checkpoints.checkpoints.AsyncCheckpoints.with_raw_response", "name": "with_raw_response", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.resources.fine_tuning.checkpoints.checkpoints.AsyncCheckpoints"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "with_raw_response of AsyncCheckpoints", "ret_type": "openai.resources.fine_tuning.checkpoints.checkpoints.AsyncCheckpointsWithRawResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai.resources.fine_tuning.checkpoints.checkpoints.AsyncCheckpoints.with_raw_response", "name": "with_raw_response", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.resources.fine_tuning.checkpoints.checkpoints.AsyncCheckpoints"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "with_raw_response of AsyncCheckpoints", "ret_type": "openai.resources.fine_tuning.checkpoints.checkpoints.AsyncCheckpointsWithRawResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "with_streaming_response": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai.resources.fine_tuning.checkpoints.checkpoints.AsyncCheckpoints.with_streaming_response", "name": "with_streaming_response", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.resources.fine_tuning.checkpoints.checkpoints.AsyncCheckpoints"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "with_streaming_response of AsyncCheckpoints", "ret_type": "openai.resources.fine_tuning.checkpoints.checkpoints.AsyncCheckpointsWithStreamingResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai.resources.fine_tuning.checkpoints.checkpoints.AsyncCheckpoints.with_streaming_response", "name": "with_streaming_response", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.resources.fine_tuning.checkpoints.checkpoints.AsyncCheckpoints"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "with_streaming_response of AsyncCheckpoints", "ret_type": "openai.resources.fine_tuning.checkpoints.checkpoints.AsyncCheckpointsWithStreamingResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.resources.fine_tuning.checkpoints.checkpoints.AsyncCheckpoints.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "openai.resources.fine_tuning.checkpoints.checkpoints.AsyncCheckpoints", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "AsyncCheckpointsWithRawResponse": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "openai.resources.fine_tuning.checkpoints.checkpoints.AsyncCheckpointsWithRawResponse", "name": "AsyncCheckpointsWithRawResponse", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "openai.resources.fine_tuning.checkpoints.checkpoints.AsyncCheckpointsWithRawResponse", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "openai.resources.fine_tuning.checkpoints.checkpoints", "mro": ["openai.resources.fine_tuning.checkpoints.checkpoints.AsyncCheckpointsWithRawResponse", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "checkpoints"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "openai.resources.fine_tuning.checkpoints.checkpoints.AsyncCheckpointsWithRawResponse.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "checkpoints"], "arg_types": ["openai.resources.fine_tuning.checkpoints.checkpoints.AsyncCheckpointsWithRawResponse", "openai.resources.fine_tuning.checkpoints.checkpoints.AsyncCheckpoints"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of AsyncCheckpointsWithRawResponse", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_checkpoints": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "openai.resources.fine_tuning.checkpoints.checkpoints.AsyncCheckpointsWithRawResponse._checkpoints", "name": "_checkpoints", "setter_type": null, "type": "openai.resources.fine_tuning.checkpoints.checkpoints.AsyncCheckpoints"}}, "permissions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai.resources.fine_tuning.checkpoints.checkpoints.AsyncCheckpointsWithRawResponse.permissions", "name": "permissions", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.resources.fine_tuning.checkpoints.checkpoints.AsyncCheckpointsWithRawResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "permissions of AsyncCheckpointsWithRawResponse", "ret_type": "openai.resources.fine_tuning.checkpoints.permissions.AsyncPermissionsWithRawResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai.resources.fine_tuning.checkpoints.checkpoints.AsyncCheckpointsWithRawResponse.permissions", "name": "permissions", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.resources.fine_tuning.checkpoints.checkpoints.AsyncCheckpointsWithRawResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "permissions of AsyncCheckpointsWithRawResponse", "ret_type": "openai.resources.fine_tuning.checkpoints.permissions.AsyncPermissionsWithRawResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.resources.fine_tuning.checkpoints.checkpoints.AsyncCheckpointsWithRawResponse.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "openai.resources.fine_tuning.checkpoints.checkpoints.AsyncCheckpointsWithRawResponse", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "AsyncCheckpointsWithStreamingResponse": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "openai.resources.fine_tuning.checkpoints.checkpoints.AsyncCheckpointsWithStreamingResponse", "name": "AsyncCheckpointsWithStreamingResponse", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "openai.resources.fine_tuning.checkpoints.checkpoints.AsyncCheckpointsWithStreamingResponse", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "openai.resources.fine_tuning.checkpoints.checkpoints", "mro": ["openai.resources.fine_tuning.checkpoints.checkpoints.AsyncCheckpointsWithStreamingResponse", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "checkpoints"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "openai.resources.fine_tuning.checkpoints.checkpoints.AsyncCheckpointsWithStreamingResponse.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "checkpoints"], "arg_types": ["openai.resources.fine_tuning.checkpoints.checkpoints.AsyncCheckpointsWithStreamingResponse", "openai.resources.fine_tuning.checkpoints.checkpoints.AsyncCheckpoints"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of AsyncCheckpointsWithStreamingResponse", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_checkpoints": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "openai.resources.fine_tuning.checkpoints.checkpoints.AsyncCheckpointsWithStreamingResponse._checkpoints", "name": "_checkpoints", "setter_type": null, "type": "openai.resources.fine_tuning.checkpoints.checkpoints.AsyncCheckpoints"}}, "permissions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai.resources.fine_tuning.checkpoints.checkpoints.AsyncCheckpointsWithStreamingResponse.permissions", "name": "permissions", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.resources.fine_tuning.checkpoints.checkpoints.AsyncCheckpointsWithStreamingResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "permissions of AsyncCheckpointsWithStreamingResponse", "ret_type": "openai.resources.fine_tuning.checkpoints.permissions.AsyncPermissionsWithStreamingResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai.resources.fine_tuning.checkpoints.checkpoints.AsyncCheckpointsWithStreamingResponse.permissions", "name": "permissions", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.resources.fine_tuning.checkpoints.checkpoints.AsyncCheckpointsWithStreamingResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "permissions of AsyncCheckpointsWithStreamingResponse", "ret_type": "openai.resources.fine_tuning.checkpoints.permissions.AsyncPermissionsWithStreamingResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.resources.fine_tuning.checkpoints.checkpoints.AsyncCheckpointsWithStreamingResponse.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "openai.resources.fine_tuning.checkpoints.checkpoints.AsyncCheckpointsWithStreamingResponse", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "AsyncPermissions": {".class": "SymbolTableNode", "cross_ref": "openai.resources.fine_tuning.checkpoints.permissions.AsyncPermissions", "kind": "Gdef", "module_public": false}, "AsyncPermissionsWithRawResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.fine_tuning.checkpoints.permissions.AsyncPermissionsWithRawResponse", "kind": "Gdef", "module_public": false}, "AsyncPermissionsWithStreamingResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.fine_tuning.checkpoints.permissions.AsyncPermissionsWithStreamingResponse", "kind": "Gdef", "module_public": false}, "Checkpoints": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["openai._resource.SyncAPIResource"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "openai.resources.fine_tuning.checkpoints.checkpoints.Checkpoints", "name": "Checkpoints", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "openai.resources.fine_tuning.checkpoints.checkpoints.Checkpoints", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "openai.resources.fine_tuning.checkpoints.checkpoints", "mro": ["openai.resources.fine_tuning.checkpoints.checkpoints.Checkpoints", "openai._resource.SyncAPIResource", "builtins.object"], "names": {".class": "SymbolTable", "permissions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai.resources.fine_tuning.checkpoints.checkpoints.Checkpoints.permissions", "name": "permissions", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.resources.fine_tuning.checkpoints.checkpoints.Checkpoints"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "permissions of Checkpoints", "ret_type": "openai.resources.fine_tuning.checkpoints.permissions.Permissions", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai.resources.fine_tuning.checkpoints.checkpoints.Checkpoints.permissions", "name": "permissions", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.resources.fine_tuning.checkpoints.checkpoints.Checkpoints"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "permissions of Checkpoints", "ret_type": "openai.resources.fine_tuning.checkpoints.permissions.Permissions", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "with_raw_response": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai.resources.fine_tuning.checkpoints.checkpoints.Checkpoints.with_raw_response", "name": "with_raw_response", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.resources.fine_tuning.checkpoints.checkpoints.Checkpoints"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "with_raw_response of Checkpoints", "ret_type": "openai.resources.fine_tuning.checkpoints.checkpoints.CheckpointsWithRawResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai.resources.fine_tuning.checkpoints.checkpoints.Checkpoints.with_raw_response", "name": "with_raw_response", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.resources.fine_tuning.checkpoints.checkpoints.Checkpoints"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "with_raw_response of Checkpoints", "ret_type": "openai.resources.fine_tuning.checkpoints.checkpoints.CheckpointsWithRawResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "with_streaming_response": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai.resources.fine_tuning.checkpoints.checkpoints.Checkpoints.with_streaming_response", "name": "with_streaming_response", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.resources.fine_tuning.checkpoints.checkpoints.Checkpoints"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "with_streaming_response of Checkpoints", "ret_type": "openai.resources.fine_tuning.checkpoints.checkpoints.CheckpointsWithStreamingResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai.resources.fine_tuning.checkpoints.checkpoints.Checkpoints.with_streaming_response", "name": "with_streaming_response", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.resources.fine_tuning.checkpoints.checkpoints.Checkpoints"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "with_streaming_response of Checkpoints", "ret_type": "openai.resources.fine_tuning.checkpoints.checkpoints.CheckpointsWithStreamingResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.resources.fine_tuning.checkpoints.checkpoints.Checkpoints.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "openai.resources.fine_tuning.checkpoints.checkpoints.Checkpoints", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "CheckpointsWithRawResponse": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "openai.resources.fine_tuning.checkpoints.checkpoints.CheckpointsWithRawResponse", "name": "CheckpointsWithRawResponse", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "openai.resources.fine_tuning.checkpoints.checkpoints.CheckpointsWithRawResponse", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "openai.resources.fine_tuning.checkpoints.checkpoints", "mro": ["openai.resources.fine_tuning.checkpoints.checkpoints.CheckpointsWithRawResponse", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "checkpoints"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "openai.resources.fine_tuning.checkpoints.checkpoints.CheckpointsWithRawResponse.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "checkpoints"], "arg_types": ["openai.resources.fine_tuning.checkpoints.checkpoints.CheckpointsWithRawResponse", "openai.resources.fine_tuning.checkpoints.checkpoints.Checkpoints"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of CheckpointsWithRawResponse", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_checkpoints": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "openai.resources.fine_tuning.checkpoints.checkpoints.CheckpointsWithRawResponse._checkpoints", "name": "_checkpoints", "setter_type": null, "type": "openai.resources.fine_tuning.checkpoints.checkpoints.Checkpoints"}}, "permissions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai.resources.fine_tuning.checkpoints.checkpoints.CheckpointsWithRawResponse.permissions", "name": "permissions", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.resources.fine_tuning.checkpoints.checkpoints.CheckpointsWithRawResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "permissions of CheckpointsWithRawResponse", "ret_type": "openai.resources.fine_tuning.checkpoints.permissions.PermissionsWithRawResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai.resources.fine_tuning.checkpoints.checkpoints.CheckpointsWithRawResponse.permissions", "name": "permissions", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.resources.fine_tuning.checkpoints.checkpoints.CheckpointsWithRawResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "permissions of CheckpointsWithRawResponse", "ret_type": "openai.resources.fine_tuning.checkpoints.permissions.PermissionsWithRawResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.resources.fine_tuning.checkpoints.checkpoints.CheckpointsWithRawResponse.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "openai.resources.fine_tuning.checkpoints.checkpoints.CheckpointsWithRawResponse", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "CheckpointsWithStreamingResponse": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "openai.resources.fine_tuning.checkpoints.checkpoints.CheckpointsWithStreamingResponse", "name": "CheckpointsWithStreamingResponse", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "openai.resources.fine_tuning.checkpoints.checkpoints.CheckpointsWithStreamingResponse", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "openai.resources.fine_tuning.checkpoints.checkpoints", "mro": ["openai.resources.fine_tuning.checkpoints.checkpoints.CheckpointsWithStreamingResponse", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "checkpoints"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "openai.resources.fine_tuning.checkpoints.checkpoints.CheckpointsWithStreamingResponse.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "checkpoints"], "arg_types": ["openai.resources.fine_tuning.checkpoints.checkpoints.CheckpointsWithStreamingResponse", "openai.resources.fine_tuning.checkpoints.checkpoints.Checkpoints"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of CheckpointsWithStreamingResponse", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_checkpoints": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "openai.resources.fine_tuning.checkpoints.checkpoints.CheckpointsWithStreamingResponse._checkpoints", "name": "_checkpoints", "setter_type": null, "type": "openai.resources.fine_tuning.checkpoints.checkpoints.Checkpoints"}}, "permissions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai.resources.fine_tuning.checkpoints.checkpoints.CheckpointsWithStreamingResponse.permissions", "name": "permissions", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.resources.fine_tuning.checkpoints.checkpoints.CheckpointsWithStreamingResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "permissions of CheckpointsWithStreamingResponse", "ret_type": "openai.resources.fine_tuning.checkpoints.permissions.PermissionsWithStreamingResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai.resources.fine_tuning.checkpoints.checkpoints.CheckpointsWithStreamingResponse.permissions", "name": "permissions", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.resources.fine_tuning.checkpoints.checkpoints.CheckpointsWithStreamingResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "permissions of CheckpointsWithStreamingResponse", "ret_type": "openai.resources.fine_tuning.checkpoints.permissions.PermissionsWithStreamingResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.resources.fine_tuning.checkpoints.checkpoints.CheckpointsWithStreamingResponse.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "openai.resources.fine_tuning.checkpoints.checkpoints.CheckpointsWithStreamingResponse", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Permissions": {".class": "SymbolTableNode", "cross_ref": "openai.resources.fine_tuning.checkpoints.permissions.Permissions", "kind": "Gdef", "module_public": false}, "PermissionsWithRawResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.fine_tuning.checkpoints.permissions.PermissionsWithRawResponse", "kind": "Gdef", "module_public": false}, "PermissionsWithStreamingResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.fine_tuning.checkpoints.permissions.PermissionsWithStreamingResponse", "kind": "Gdef", "module_public": false}, "SyncAPIResource": {".class": "SymbolTableNode", "cross_ref": "openai._resource.SyncAPIResource", "kind": "Gdef", "module_public": false}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "openai.resources.fine_tuning.checkpoints.checkpoints.__all__", "name": "__all__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.resources.fine_tuning.checkpoints.checkpoints.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.resources.fine_tuning.checkpoints.checkpoints.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.resources.fine_tuning.checkpoints.checkpoints.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.resources.fine_tuning.checkpoints.checkpoints.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.resources.fine_tuning.checkpoints.checkpoints.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.resources.fine_tuning.checkpoints.checkpoints.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef", "module_public": false}, "cached_property": {".class": "SymbolTableNode", "cross_ref": "openai._compat.cached_property", "kind": "Gdef", "module_public": false}}, "path": "/home/<USER>/.pyenv/versions/3.12.5/lib/python3.12/site-packages/openai/resources/fine_tuning/checkpoints/checkpoints.py"}