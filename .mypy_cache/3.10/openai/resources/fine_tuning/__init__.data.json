{".class": "MypyFile", "_fullname": "openai.resources.fine_tuning", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Alpha": {".class": "SymbolTableNode", "cross_ref": "openai.resources.fine_tuning.alpha.alpha.Alpha", "kind": "Gdef"}, "AlphaWithRawResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.fine_tuning.alpha.alpha.AlphaWithRawResponse", "kind": "Gdef"}, "AlphaWithStreamingResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.fine_tuning.alpha.alpha.AlphaWithStreamingResponse", "kind": "Gdef"}, "AsyncAlpha": {".class": "SymbolTableNode", "cross_ref": "openai.resources.fine_tuning.alpha.alpha.AsyncAlpha", "kind": "Gdef"}, "AsyncAlphaWithRawResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.fine_tuning.alpha.alpha.AsyncAlphaWithRawResponse", "kind": "Gdef"}, "AsyncAlphaWithStreamingResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.fine_tuning.alpha.alpha.AsyncAlphaWithStreamingResponse", "kind": "Gdef"}, "AsyncCheckpoints": {".class": "SymbolTableNode", "cross_ref": "openai.resources.fine_tuning.checkpoints.checkpoints.AsyncCheckpoints", "kind": "Gdef"}, "AsyncCheckpointsWithRawResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.fine_tuning.checkpoints.checkpoints.AsyncCheckpointsWithRawResponse", "kind": "Gdef"}, "AsyncCheckpointsWithStreamingResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.fine_tuning.checkpoints.checkpoints.AsyncCheckpointsWithStreamingResponse", "kind": "Gdef"}, "AsyncFineTuning": {".class": "SymbolTableNode", "cross_ref": "openai.resources.fine_tuning.fine_tuning.AsyncFineTuning", "kind": "Gdef"}, "AsyncFineTuningWithRawResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.fine_tuning.fine_tuning.AsyncFineTuningWithRawResponse", "kind": "Gdef"}, "AsyncFineTuningWithStreamingResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.fine_tuning.fine_tuning.AsyncFineTuningWithStreamingResponse", "kind": "Gdef"}, "AsyncJobs": {".class": "SymbolTableNode", "cross_ref": "openai.resources.fine_tuning.jobs.jobs.AsyncJobs", "kind": "Gdef"}, "AsyncJobsWithRawResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.fine_tuning.jobs.jobs.AsyncJobsWithRawResponse", "kind": "Gdef"}, "AsyncJobsWithStreamingResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.fine_tuning.jobs.jobs.AsyncJobsWithStreamingResponse", "kind": "Gdef"}, "Checkpoints": {".class": "SymbolTableNode", "cross_ref": "openai.resources.fine_tuning.checkpoints.checkpoints.Checkpoints", "kind": "Gdef"}, "CheckpointsWithRawResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.fine_tuning.checkpoints.checkpoints.CheckpointsWithRawResponse", "kind": "Gdef"}, "CheckpointsWithStreamingResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.fine_tuning.checkpoints.checkpoints.CheckpointsWithStreamingResponse", "kind": "Gdef"}, "FineTuning": {".class": "SymbolTableNode", "cross_ref": "openai.resources.fine_tuning.fine_tuning.FineTuning", "kind": "Gdef"}, "FineTuningWithRawResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.fine_tuning.fine_tuning.FineTuningWithRawResponse", "kind": "Gdef"}, "FineTuningWithStreamingResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.fine_tuning.fine_tuning.FineTuningWithStreamingResponse", "kind": "Gdef"}, "Jobs": {".class": "SymbolTableNode", "cross_ref": "openai.resources.fine_tuning.jobs.jobs.Jobs", "kind": "Gdef"}, "JobsWithRawResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.fine_tuning.jobs.jobs.JobsWithRawResponse", "kind": "Gdef"}, "JobsWithStreamingResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.fine_tuning.jobs.jobs.JobsWithStreamingResponse", "kind": "Gdef"}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "openai.resources.fine_tuning.__all__", "name": "__all__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.resources.fine_tuning.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.resources.fine_tuning.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.resources.fine_tuning.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.resources.fine_tuning.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.resources.fine_tuning.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.resources.fine_tuning.__path__", "name": "__path__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.resources.fine_tuning.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}}, "path": "/home/<USER>/.pyenv/versions/3.12.5/lib/python3.12/site-packages/openai/resources/fine_tuning/__init__.py"}