{".class": "MypyFile", "_fullname": "openai.resources.fine_tuning.alpha.alpha", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Alpha": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["openai._resource.SyncAPIResource"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "openai.resources.fine_tuning.alpha.alpha.Alpha", "name": "Alpha", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "openai.resources.fine_tuning.alpha.alpha.Alpha", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "openai.resources.fine_tuning.alpha.alpha", "mro": ["openai.resources.fine_tuning.alpha.alpha.Alpha", "openai._resource.SyncAPIResource", "builtins.object"], "names": {".class": "SymbolTable", "graders": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai.resources.fine_tuning.alpha.alpha.Alpha.graders", "name": "graders", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.resources.fine_tuning.alpha.alpha.Alpha"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "graders of Alpha", "ret_type": "openai.resources.fine_tuning.alpha.graders.Graders", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai.resources.fine_tuning.alpha.alpha.Alpha.graders", "name": "graders", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.resources.fine_tuning.alpha.alpha.Alpha"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "graders of Alpha", "ret_type": "openai.resources.fine_tuning.alpha.graders.Graders", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "with_raw_response": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai.resources.fine_tuning.alpha.alpha.Alpha.with_raw_response", "name": "with_raw_response", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.resources.fine_tuning.alpha.alpha.Alpha"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "with_raw_response of Alpha", "ret_type": "openai.resources.fine_tuning.alpha.alpha.AlphaWithRawResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai.resources.fine_tuning.alpha.alpha.Alpha.with_raw_response", "name": "with_raw_response", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.resources.fine_tuning.alpha.alpha.Alpha"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "with_raw_response of Alpha", "ret_type": "openai.resources.fine_tuning.alpha.alpha.AlphaWithRawResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "with_streaming_response": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai.resources.fine_tuning.alpha.alpha.Alpha.with_streaming_response", "name": "with_streaming_response", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.resources.fine_tuning.alpha.alpha.Alpha"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "with_streaming_response of Alpha", "ret_type": "openai.resources.fine_tuning.alpha.alpha.AlphaWithStreamingResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai.resources.fine_tuning.alpha.alpha.Alpha.with_streaming_response", "name": "with_streaming_response", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.resources.fine_tuning.alpha.alpha.Alpha"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "with_streaming_response of Alpha", "ret_type": "openai.resources.fine_tuning.alpha.alpha.AlphaWithStreamingResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.resources.fine_tuning.alpha.alpha.Alpha.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "openai.resources.fine_tuning.alpha.alpha.Alpha", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "AlphaWithRawResponse": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "openai.resources.fine_tuning.alpha.alpha.AlphaWithRawResponse", "name": "AlphaWithRawResponse", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "openai.resources.fine_tuning.alpha.alpha.AlphaWithRawResponse", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "openai.resources.fine_tuning.alpha.alpha", "mro": ["openai.resources.fine_tuning.alpha.alpha.AlphaWithRawResponse", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "alpha"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "openai.resources.fine_tuning.alpha.alpha.AlphaWithRawResponse.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "alpha"], "arg_types": ["openai.resources.fine_tuning.alpha.alpha.AlphaWithRawResponse", "openai.resources.fine_tuning.alpha.alpha.Alpha"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of AlphaWithRawResponse", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_alpha": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "openai.resources.fine_tuning.alpha.alpha.AlphaWithRawResponse._alpha", "name": "_alpha", "setter_type": null, "type": "openai.resources.fine_tuning.alpha.alpha.Alpha"}}, "graders": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai.resources.fine_tuning.alpha.alpha.AlphaWithRawResponse.graders", "name": "graders", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.resources.fine_tuning.alpha.alpha.AlphaWithRawResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "graders of AlphaWithRawResponse", "ret_type": "openai.resources.fine_tuning.alpha.graders.GradersWithRawResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai.resources.fine_tuning.alpha.alpha.AlphaWithRawResponse.graders", "name": "graders", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.resources.fine_tuning.alpha.alpha.AlphaWithRawResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "graders of AlphaWithRawResponse", "ret_type": "openai.resources.fine_tuning.alpha.graders.GradersWithRawResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.resources.fine_tuning.alpha.alpha.AlphaWithRawResponse.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "openai.resources.fine_tuning.alpha.alpha.AlphaWithRawResponse", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "AlphaWithStreamingResponse": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "openai.resources.fine_tuning.alpha.alpha.AlphaWithStreamingResponse", "name": "AlphaWithStreamingResponse", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "openai.resources.fine_tuning.alpha.alpha.AlphaWithStreamingResponse", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "openai.resources.fine_tuning.alpha.alpha", "mro": ["openai.resources.fine_tuning.alpha.alpha.AlphaWithStreamingResponse", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "alpha"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "openai.resources.fine_tuning.alpha.alpha.AlphaWithStreamingResponse.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "alpha"], "arg_types": ["openai.resources.fine_tuning.alpha.alpha.AlphaWithStreamingResponse", "openai.resources.fine_tuning.alpha.alpha.Alpha"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of AlphaWithStreamingResponse", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_alpha": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "openai.resources.fine_tuning.alpha.alpha.AlphaWithStreamingResponse._alpha", "name": "_alpha", "setter_type": null, "type": "openai.resources.fine_tuning.alpha.alpha.Alpha"}}, "graders": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai.resources.fine_tuning.alpha.alpha.AlphaWithStreamingResponse.graders", "name": "graders", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.resources.fine_tuning.alpha.alpha.AlphaWithStreamingResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "graders of AlphaWithStreamingResponse", "ret_type": "openai.resources.fine_tuning.alpha.graders.GradersWithStreamingResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai.resources.fine_tuning.alpha.alpha.AlphaWithStreamingResponse.graders", "name": "graders", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.resources.fine_tuning.alpha.alpha.AlphaWithStreamingResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "graders of AlphaWithStreamingResponse", "ret_type": "openai.resources.fine_tuning.alpha.graders.GradersWithStreamingResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.resources.fine_tuning.alpha.alpha.AlphaWithStreamingResponse.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "openai.resources.fine_tuning.alpha.alpha.AlphaWithStreamingResponse", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "AsyncAPIResource": {".class": "SymbolTableNode", "cross_ref": "openai._resource.AsyncAPIResource", "kind": "Gdef", "module_public": false}, "AsyncAlpha": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["openai._resource.AsyncAPIResource"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "openai.resources.fine_tuning.alpha.alpha.AsyncAlpha", "name": "AsyncAlpha", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "openai.resources.fine_tuning.alpha.alpha.AsyncAlpha", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "openai.resources.fine_tuning.alpha.alpha", "mro": ["openai.resources.fine_tuning.alpha.alpha.AsyncAlpha", "openai._resource.AsyncAPIResource", "builtins.object"], "names": {".class": "SymbolTable", "graders": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai.resources.fine_tuning.alpha.alpha.AsyncAlpha.graders", "name": "graders", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.resources.fine_tuning.alpha.alpha.AsyncAlpha"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "graders of AsyncAlpha", "ret_type": "openai.resources.fine_tuning.alpha.graders.AsyncGraders", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai.resources.fine_tuning.alpha.alpha.AsyncAlpha.graders", "name": "graders", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.resources.fine_tuning.alpha.alpha.AsyncAlpha"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "graders of AsyncAlpha", "ret_type": "openai.resources.fine_tuning.alpha.graders.AsyncGraders", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "with_raw_response": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai.resources.fine_tuning.alpha.alpha.AsyncAlpha.with_raw_response", "name": "with_raw_response", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.resources.fine_tuning.alpha.alpha.AsyncAlpha"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "with_raw_response of AsyncAlpha", "ret_type": "openai.resources.fine_tuning.alpha.alpha.AsyncAlphaWithRawResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai.resources.fine_tuning.alpha.alpha.AsyncAlpha.with_raw_response", "name": "with_raw_response", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.resources.fine_tuning.alpha.alpha.AsyncAlpha"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "with_raw_response of AsyncAlpha", "ret_type": "openai.resources.fine_tuning.alpha.alpha.AsyncAlphaWithRawResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "with_streaming_response": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai.resources.fine_tuning.alpha.alpha.AsyncAlpha.with_streaming_response", "name": "with_streaming_response", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.resources.fine_tuning.alpha.alpha.AsyncAlpha"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "with_streaming_response of AsyncAlpha", "ret_type": "openai.resources.fine_tuning.alpha.alpha.AsyncAlphaWithStreamingResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai.resources.fine_tuning.alpha.alpha.AsyncAlpha.with_streaming_response", "name": "with_streaming_response", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.resources.fine_tuning.alpha.alpha.AsyncAlpha"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "with_streaming_response of AsyncAlpha", "ret_type": "openai.resources.fine_tuning.alpha.alpha.AsyncAlphaWithStreamingResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.resources.fine_tuning.alpha.alpha.AsyncAlpha.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "openai.resources.fine_tuning.alpha.alpha.AsyncAlpha", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "AsyncAlphaWithRawResponse": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "openai.resources.fine_tuning.alpha.alpha.AsyncAlphaWithRawResponse", "name": "AsyncAlphaWithRawResponse", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "openai.resources.fine_tuning.alpha.alpha.AsyncAlphaWithRawResponse", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "openai.resources.fine_tuning.alpha.alpha", "mro": ["openai.resources.fine_tuning.alpha.alpha.AsyncAlphaWithRawResponse", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "alpha"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "openai.resources.fine_tuning.alpha.alpha.AsyncAlphaWithRawResponse.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "alpha"], "arg_types": ["openai.resources.fine_tuning.alpha.alpha.AsyncAlphaWithRawResponse", "openai.resources.fine_tuning.alpha.alpha.AsyncAlpha"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of AsyncAlphaWithRawResponse", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_alpha": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "openai.resources.fine_tuning.alpha.alpha.AsyncAlphaWithRawResponse._alpha", "name": "_alpha", "setter_type": null, "type": "openai.resources.fine_tuning.alpha.alpha.AsyncAlpha"}}, "graders": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai.resources.fine_tuning.alpha.alpha.AsyncAlphaWithRawResponse.graders", "name": "graders", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.resources.fine_tuning.alpha.alpha.AsyncAlphaWithRawResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "graders of AsyncAlphaWithRawResponse", "ret_type": "openai.resources.fine_tuning.alpha.graders.AsyncGradersWithRawResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai.resources.fine_tuning.alpha.alpha.AsyncAlphaWithRawResponse.graders", "name": "graders", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.resources.fine_tuning.alpha.alpha.AsyncAlphaWithRawResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "graders of AsyncAlphaWithRawResponse", "ret_type": "openai.resources.fine_tuning.alpha.graders.AsyncGradersWithRawResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.resources.fine_tuning.alpha.alpha.AsyncAlphaWithRawResponse.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "openai.resources.fine_tuning.alpha.alpha.AsyncAlphaWithRawResponse", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "AsyncAlphaWithStreamingResponse": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "openai.resources.fine_tuning.alpha.alpha.AsyncAlphaWithStreamingResponse", "name": "AsyncAlphaWithStreamingResponse", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "openai.resources.fine_tuning.alpha.alpha.AsyncAlphaWithStreamingResponse", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "openai.resources.fine_tuning.alpha.alpha", "mro": ["openai.resources.fine_tuning.alpha.alpha.AsyncAlphaWithStreamingResponse", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "alpha"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "openai.resources.fine_tuning.alpha.alpha.AsyncAlphaWithStreamingResponse.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "alpha"], "arg_types": ["openai.resources.fine_tuning.alpha.alpha.AsyncAlphaWithStreamingResponse", "openai.resources.fine_tuning.alpha.alpha.AsyncAlpha"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of AsyncAlphaWithStreamingResponse", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_alpha": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "openai.resources.fine_tuning.alpha.alpha.AsyncAlphaWithStreamingResponse._alpha", "name": "_alpha", "setter_type": null, "type": "openai.resources.fine_tuning.alpha.alpha.AsyncAlpha"}}, "graders": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai.resources.fine_tuning.alpha.alpha.AsyncAlphaWithStreamingResponse.graders", "name": "graders", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.resources.fine_tuning.alpha.alpha.AsyncAlphaWithStreamingResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "graders of AsyncAlphaWithStreamingResponse", "ret_type": "openai.resources.fine_tuning.alpha.graders.AsyncGradersWithStreamingResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai.resources.fine_tuning.alpha.alpha.AsyncAlphaWithStreamingResponse.graders", "name": "graders", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.resources.fine_tuning.alpha.alpha.AsyncAlphaWithStreamingResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "graders of AsyncAlphaWithStreamingResponse", "ret_type": "openai.resources.fine_tuning.alpha.graders.AsyncGradersWithStreamingResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.resources.fine_tuning.alpha.alpha.AsyncAlphaWithStreamingResponse.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "openai.resources.fine_tuning.alpha.alpha.AsyncAlphaWithStreamingResponse", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "AsyncGraders": {".class": "SymbolTableNode", "cross_ref": "openai.resources.fine_tuning.alpha.graders.AsyncGraders", "kind": "Gdef", "module_public": false}, "AsyncGradersWithRawResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.fine_tuning.alpha.graders.AsyncGradersWithRawResponse", "kind": "Gdef", "module_public": false}, "AsyncGradersWithStreamingResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.fine_tuning.alpha.graders.AsyncGradersWithStreamingResponse", "kind": "Gdef", "module_public": false}, "Graders": {".class": "SymbolTableNode", "cross_ref": "openai.resources.fine_tuning.alpha.graders.Graders", "kind": "Gdef", "module_public": false}, "GradersWithRawResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.fine_tuning.alpha.graders.GradersWithRawResponse", "kind": "Gdef", "module_public": false}, "GradersWithStreamingResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.fine_tuning.alpha.graders.GradersWithStreamingResponse", "kind": "Gdef", "module_public": false}, "SyncAPIResource": {".class": "SymbolTableNode", "cross_ref": "openai._resource.SyncAPIResource", "kind": "Gdef", "module_public": false}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "openai.resources.fine_tuning.alpha.alpha.__all__", "name": "__all__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.resources.fine_tuning.alpha.alpha.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.resources.fine_tuning.alpha.alpha.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.resources.fine_tuning.alpha.alpha.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.resources.fine_tuning.alpha.alpha.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.resources.fine_tuning.alpha.alpha.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.resources.fine_tuning.alpha.alpha.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef", "module_public": false}, "cached_property": {".class": "SymbolTableNode", "cross_ref": "openai._compat.cached_property", "kind": "Gdef", "module_public": false}}, "path": "/home/<USER>/.pyenv/versions/3.12.5/lib/python3.12/site-packages/openai/resources/fine_tuning/alpha/alpha.py"}