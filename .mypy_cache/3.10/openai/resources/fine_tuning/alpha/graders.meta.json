{"data_mtime": 1750696463, "dep_lines": [16, 16, 17, 18, 16, 9, 10, 11, 12, 13, 14, 15, 3, 5, 7, 9, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 5, 5, 20, 10, 5, 5, 5, 5, 5, 5, 5, 5, 10, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["openai.types.fine_tuning.alpha.grader_run_params", "openai.types.fine_tuning.alpha.grader_validate_params", "openai.types.fine_tuning.alpha.grader_run_response", "openai.types.fine_tuning.alpha.grader_validate_response", "openai.types.fine_tuning.alpha", "openai._legacy_response", "openai._types", "openai._utils", "openai._compat", "openai._resource", "openai._response", "openai._base_client", "__future__", "typing", "httpx", "openai", "builtins", "_frozen_importlib", "abc", "httpx._config", "openai._models", "openai.types", "openai.types.fine_tuning", "openai.types.graders", "openai.types.graders.label_model_grader_param", "openai.types.graders.multi_grader_param", "openai.types.graders.python_grader_param", "openai.types.graders.score_model_grader_param", "openai.types.graders.string_check_grader_param", "openai.types.graders.text_similarity_grader_param", "openai.types.responses", "openai.types.responses.response_input_text_param", "pydantic", "pydantic._internal", "pydantic._internal._model_construction", "pydantic.main"], "hash": "615c80fe343e07ca3f9a46dadec1cb593c724a30", "id": "openai.resources.fine_tuning.alpha.graders", "ignore_all": true, "interface_hash": "550728ea29a1ab2f293c52a42c577a164b39f412", "mtime": 1747857566, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "/home/<USER>/.pyenv/versions/3.12.5/lib/python3.12/site-packages/openai/resources/fine_tuning/alpha/graders.py", "plugin_data": null, "size": 10022, "suppressed": [], "version_id": "1.16.1"}