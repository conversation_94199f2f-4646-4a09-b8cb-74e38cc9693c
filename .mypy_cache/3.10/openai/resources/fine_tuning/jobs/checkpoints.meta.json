{"data_mtime": 1750696463, "dep_lines": [18, 19, 18, 7, 8, 9, 10, 11, 12, 13, 14, 3, 5, 7, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 20, 10, 5, 5, 5, 5, 5, 5, 5, 5, 10, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["openai.types.fine_tuning.jobs.checkpoint_list_params", "openai.types.fine_tuning.jobs.fine_tuning_job_checkpoint", "openai.types.fine_tuning.jobs", "openai._legacy_response", "openai._types", "openai._utils", "openai._compat", "openai._resource", "openai._response", "openai.pagination", "openai._base_client", "__future__", "httpx", "openai", "builtins", "_frozen_importlib", "abc", "httpx._config", "openai._models", "openai.types", "openai.types.fine_tuning", "pydantic", "pydantic._internal", "pydantic._internal._model_construction", "pydantic.main", "typing"], "hash": "447a2ade2d9add71dd20940eca5250fab0fe9aed", "id": "openai.resources.fine_tuning.jobs.checkpoints", "ignore_all": true, "interface_hash": "ddf05a397f36ad6288b9612ecddc3c2be1ba265d", "mtime": 1747857566, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "/home/<USER>/.pyenv/versions/3.12.5/lib/python3.12/site-packages/openai/resources/fine_tuning/jobs/checkpoints.py", "plugin_data": null, "size": 7466, "suppressed": [], "version_id": "1.16.1"}