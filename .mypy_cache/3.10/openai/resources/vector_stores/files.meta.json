{"data_mtime": 1750696463, "dep_lines": [19, 19, 19, 21, 22, 23, 19, 20, 10, 11, 12, 13, 14, 15, 16, 17, 18, 3, 5, 6, 8, 10, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 10, 5, 5, 5, 20, 5, 10, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 10, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["openai.types.vector_stores.file_list_params", "openai.types.vector_stores.file_create_params", "openai.types.vector_stores.file_update_params", "openai.types.vector_stores.vector_store_file", "openai.types.vector_stores.file_content_response", "openai.types.vector_stores.vector_store_file_deleted", "openai.types.vector_stores", "openai.types.file_chunking_strategy_param", "openai._legacy_response", "openai.types", "openai._types", "openai._utils", "openai._compat", "openai._resource", "openai._response", "openai.pagination", "openai._base_client", "__future__", "typing", "typing_extensions", "httpx", "openai", "builtins", "_frozen_importlib", "abc", "httpx._config", "openai._models", "openai.types.auto_file_chunking_strategy_param", "openai.types.static_file_chunking_strategy_object_param", "openai.types.static_file_chunking_strategy_param", "os", "pydantic", "pydantic._internal", "pydantic._internal._model_construction", "pydantic.main"], "hash": "024cd4a52f5f4b21e0385b1ef2a31c8042f8fdc4", "id": "openai.resources.vector_stores.files", "ignore_all": true, "interface_hash": "63f3f60ddc5fa93c6f9e9b5bcac82f1a007ad00d", "mtime": 1747857566, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "/home/<USER>/.pyenv/versions/3.12.5/lib/python3.12/site-packages/openai/resources/vector_stores/files.py", "plugin_data": null, "size": 39445, "suppressed": [], "version_id": "1.16.1"}