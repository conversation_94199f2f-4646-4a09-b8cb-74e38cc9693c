{".class": "MypyFile", "_fullname": "openai.resources.containers.files", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AsyncContent": {".class": "SymbolTableNode", "cross_ref": "openai.resources.containers.files.content.AsyncContent", "kind": "Gdef"}, "AsyncContentWithRawResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.containers.files.content.AsyncContentWithRawResponse", "kind": "Gdef"}, "AsyncContentWithStreamingResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.containers.files.content.AsyncContentWithStreamingResponse", "kind": "Gdef"}, "AsyncFiles": {".class": "SymbolTableNode", "cross_ref": "openai.resources.containers.files.files.AsyncFiles", "kind": "Gdef"}, "AsyncFilesWithRawResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.containers.files.files.AsyncFilesWithRawResponse", "kind": "Gdef"}, "AsyncFilesWithStreamingResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.containers.files.files.AsyncFilesWithStreamingResponse", "kind": "Gdef"}, "Content": {".class": "SymbolTableNode", "cross_ref": "openai.resources.containers.files.content.Content", "kind": "Gdef"}, "ContentWithRawResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.containers.files.content.ContentWithRawResponse", "kind": "Gdef"}, "ContentWithStreamingResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.containers.files.content.ContentWithStreamingResponse", "kind": "Gdef"}, "Files": {".class": "SymbolTableNode", "cross_ref": "openai.resources.containers.files.files.Files", "kind": "Gdef"}, "FilesWithRawResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.containers.files.files.FilesWithRawResponse", "kind": "Gdef"}, "FilesWithStreamingResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.containers.files.files.FilesWithStreamingResponse", "kind": "Gdef"}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "openai.resources.containers.files.__all__", "name": "__all__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.resources.containers.files.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.resources.containers.files.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.resources.containers.files.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.resources.containers.files.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.resources.containers.files.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.resources.containers.files.__path__", "name": "__path__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.resources.containers.files.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}}, "path": "/home/<USER>/.pyenv/versions/3.12.5/lib/python3.12/site-packages/openai/resources/containers/files/__init__.py"}