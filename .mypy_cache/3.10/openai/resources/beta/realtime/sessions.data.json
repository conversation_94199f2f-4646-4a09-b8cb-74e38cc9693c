{".class": "MypyFile", "_fullname": "openai.resources.beta.realtime.sessions", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AsyncAPIResource": {".class": "SymbolTableNode", "cross_ref": "openai._resource.AsyncAPIResource", "kind": "Gdef", "module_public": false}, "AsyncSessions": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["openai._resource.AsyncAPIResource"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "openai.resources.beta.realtime.sessions.AsyncSessions", "name": "AsyncSessions", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "openai.resources.beta.realtime.sessions.AsyncSessions", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "openai.resources.beta.realtime.sessions", "mro": ["openai.resources.beta.realtime.sessions.AsyncSessions", "openai._resource.AsyncAPIResource", "builtins.object"], "names": {".class": "SymbolTable", "create": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "input_audio_format", "input_audio_noise_reduction", "input_audio_transcription", "instructions", "max_response_output_tokens", "modalities", "model", "output_audio_format", "temperature", "tool_choice", "tools", "turn_detection", "voice", "extra_headers", "extra_query", "extra_body", "timeout"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "openai.resources.beta.realtime.sessions.AsyncSessions.create", "name": "create", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "input_audio_format", "input_audio_noise_reduction", "input_audio_transcription", "instructions", "max_response_output_tokens", "modalities", "model", "output_audio_format", "temperature", "tool_choice", "tools", "turn_detection", "voice", "extra_headers", "extra_query", "extra_body", "timeout"], "arg_types": ["openai.resources.beta.realtime.sessions.AsyncSessions", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "pcm16"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "g711_ulaw"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "g711_alaw"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.realtime.session_create_params.InputAudioNoiseReduction"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.realtime.session_create_params.InputAudioTranscription"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "LiteralType", "fallback": "builtins.str", "value": "inf"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "text"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "audio"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.list"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "gpt-4o-realtime-preview"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "gpt-4o-realtime-preview-2024-10-01"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "gpt-4o-realtime-preview-2024-12-17"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "gpt-4o-mini-realtime-preview"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "gpt-4o-mini-realtime-preview-2024-12-17"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "pcm16"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "g711_ulaw"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "g711_alaw"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.realtime.session_create_params.Tool"}], "extra_attrs": null, "type_ref": "typing.Iterable"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.realtime.session_create_params.TurnDetection"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "LiteralType", "fallback": "builtins.str", "value": "alloy"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "ash"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "ballad"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "coral"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "echo"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "fable"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "onyx"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "nova"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "sage"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "shimmer"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "verse"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Headers"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Query"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "httpx._config.Timeout", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create of AsyncSessions", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "openai.types.beta.realtime.session_create_response.SessionCreateResponse"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "with_raw_response": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai.resources.beta.realtime.sessions.AsyncSessions.with_raw_response", "name": "with_raw_response", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.resources.beta.realtime.sessions.AsyncSessions"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "with_raw_response of AsyncSessions", "ret_type": "openai.resources.beta.realtime.sessions.AsyncSessionsWithRawResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai.resources.beta.realtime.sessions.AsyncSessions.with_raw_response", "name": "with_raw_response", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.resources.beta.realtime.sessions.AsyncSessions"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "with_raw_response of AsyncSessions", "ret_type": "openai.resources.beta.realtime.sessions.AsyncSessionsWithRawResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "with_streaming_response": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai.resources.beta.realtime.sessions.AsyncSessions.with_streaming_response", "name": "with_streaming_response", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.resources.beta.realtime.sessions.AsyncSessions"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "with_streaming_response of AsyncSessions", "ret_type": "openai.resources.beta.realtime.sessions.AsyncSessionsWithStreamingResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai.resources.beta.realtime.sessions.AsyncSessions.with_streaming_response", "name": "with_streaming_response", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.resources.beta.realtime.sessions.AsyncSessions"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "with_streaming_response of AsyncSessions", "ret_type": "openai.resources.beta.realtime.sessions.AsyncSessionsWithStreamingResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.resources.beta.realtime.sessions.AsyncSessions.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "openai.resources.beta.realtime.sessions.AsyncSessions", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "AsyncSessionsWithRawResponse": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "openai.resources.beta.realtime.sessions.AsyncSessionsWithRawResponse", "name": "AsyncSessionsWithRawResponse", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "openai.resources.beta.realtime.sessions.AsyncSessionsWithRawResponse", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "openai.resources.beta.realtime.sessions", "mro": ["openai.resources.beta.realtime.sessions.AsyncSessionsWithRawResponse", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "sessions"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "openai.resources.beta.realtime.sessions.AsyncSessionsWithRawResponse.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "sessions"], "arg_types": ["openai.resources.beta.realtime.sessions.AsyncSessionsWithRawResponse", "openai.resources.beta.realtime.sessions.AsyncSessions"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of AsyncSessionsWithRawResponse", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_sessions": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "openai.resources.beta.realtime.sessions.AsyncSessionsWithRawResponse._sessions", "name": "_sessions", "setter_type": null, "type": "openai.resources.beta.realtime.sessions.AsyncSessions"}}, "create": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "openai.resources.beta.realtime.sessions.AsyncSessionsWithRawResponse.create", "name": "create", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["input_audio_format", "input_audio_noise_reduction", "input_audio_transcription", "instructions", "max_response_output_tokens", "modalities", "model", "output_audio_format", "temperature", "tool_choice", "tools", "turn_detection", "voice", "extra_headers", "extra_query", "extra_body", "timeout"], "arg_types": [{".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "pcm16"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "g711_ulaw"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "g711_alaw"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.realtime.session_create_params.InputAudioNoiseReduction"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.realtime.session_create_params.InputAudioTranscription"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "LiteralType", "fallback": "builtins.str", "value": "inf"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "text"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "audio"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.list"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "gpt-4o-realtime-preview"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "gpt-4o-realtime-preview-2024-10-01"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "gpt-4o-realtime-preview-2024-12-17"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "gpt-4o-mini-realtime-preview"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "gpt-4o-mini-realtime-preview-2024-12-17"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "pcm16"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "g711_ulaw"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "g711_alaw"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.realtime.session_create_params.Tool"}], "extra_attrs": null, "type_ref": "typing.Iterable"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.realtime.session_create_params.TurnDetection"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "LiteralType", "fallback": "builtins.str", "value": "alloy"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "ash"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "ballad"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "coral"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "echo"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "fable"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "onyx"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "nova"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "sage"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "shimmer"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "verse"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Headers"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Query"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "httpx._config.Timeout", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["openai.types.beta.realtime.session_create_response.SessionCreateResponse"], "extra_attrs": null, "type_ref": "openai._legacy_response.LegacyAPIResponse"}], "extra_attrs": null, "type_ref": "typing.Awaitable"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.resources.beta.realtime.sessions.AsyncSessionsWithRawResponse.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "openai.resources.beta.realtime.sessions.AsyncSessionsWithRawResponse", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "AsyncSessionsWithStreamingResponse": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "openai.resources.beta.realtime.sessions.AsyncSessionsWithStreamingResponse", "name": "AsyncSessionsWithStreamingResponse", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "openai.resources.beta.realtime.sessions.AsyncSessionsWithStreamingResponse", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "openai.resources.beta.realtime.sessions", "mro": ["openai.resources.beta.realtime.sessions.AsyncSessionsWithStreamingResponse", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "sessions"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "openai.resources.beta.realtime.sessions.AsyncSessionsWithStreamingResponse.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "sessions"], "arg_types": ["openai.resources.beta.realtime.sessions.AsyncSessionsWithStreamingResponse", "openai.resources.beta.realtime.sessions.AsyncSessions"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of AsyncSessionsWithStreamingResponse", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_sessions": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "openai.resources.beta.realtime.sessions.AsyncSessionsWithStreamingResponse._sessions", "name": "_sessions", "setter_type": null, "type": "openai.resources.beta.realtime.sessions.AsyncSessions"}}, "create": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "openai.resources.beta.realtime.sessions.AsyncSessionsWithStreamingResponse.create", "name": "create", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["input_audio_format", "input_audio_noise_reduction", "input_audio_transcription", "instructions", "max_response_output_tokens", "modalities", "model", "output_audio_format", "temperature", "tool_choice", "tools", "turn_detection", "voice", "extra_headers", "extra_query", "extra_body", "timeout"], "arg_types": [{".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "pcm16"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "g711_ulaw"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "g711_alaw"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.realtime.session_create_params.InputAudioNoiseReduction"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.realtime.session_create_params.InputAudioTranscription"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "LiteralType", "fallback": "builtins.str", "value": "inf"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "text"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "audio"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.list"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "gpt-4o-realtime-preview"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "gpt-4o-realtime-preview-2024-10-01"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "gpt-4o-realtime-preview-2024-12-17"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "gpt-4o-mini-realtime-preview"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "gpt-4o-mini-realtime-preview-2024-12-17"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "pcm16"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "g711_ulaw"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "g711_alaw"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.realtime.session_create_params.Tool"}], "extra_attrs": null, "type_ref": "typing.Iterable"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.realtime.session_create_params.TurnDetection"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "LiteralType", "fallback": "builtins.str", "value": "alloy"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "ash"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "ballad"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "coral"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "echo"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "fable"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "onyx"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "nova"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "sage"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "shimmer"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "verse"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Headers"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Query"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "httpx._config.Timeout", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["openai.types.beta.realtime.session_create_response.SessionCreateResponse"], "extra_attrs": null, "type_ref": "openai._response.AsyncAPIResponse"}], "extra_attrs": null, "type_ref": "openai._response.AsyncResponseContextManager"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.resources.beta.realtime.sessions.AsyncSessionsWithStreamingResponse.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "openai.resources.beta.realtime.sessions.AsyncSessionsWithStreamingResponse", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Body": {".class": "SymbolTableNode", "cross_ref": "openai._types.Body", "kind": "Gdef", "module_public": false}, "Headers": {".class": "SymbolTableNode", "cross_ref": "openai._types.Headers", "kind": "Gdef", "module_public": false}, "Iterable": {".class": "SymbolTableNode", "cross_ref": "typing.Iterable", "kind": "Gdef", "module_public": false}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef", "module_public": false}, "Literal": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.Literal", "kind": "Gdef", "module_public": false}, "NOT_GIVEN": {".class": "SymbolTableNode", "cross_ref": "openai._types.NOT_GIVEN", "kind": "Gdef", "module_public": false}, "NotGiven": {".class": "SymbolTableNode", "cross_ref": "openai._types.NotGiven", "kind": "Gdef", "module_public": false}, "Query": {".class": "SymbolTableNode", "cross_ref": "openai._types.Query", "kind": "Gdef", "module_public": false}, "SessionCreateResponse": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.realtime.session_create_response.SessionCreateResponse", "kind": "Gdef", "module_public": false}, "Sessions": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["openai._resource.SyncAPIResource"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "openai.resources.beta.realtime.sessions.Sessions", "name": "Sessions", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "openai.resources.beta.realtime.sessions.Sessions", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "openai.resources.beta.realtime.sessions", "mro": ["openai.resources.beta.realtime.sessions.Sessions", "openai._resource.SyncAPIResource", "builtins.object"], "names": {".class": "SymbolTable", "create": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "input_audio_format", "input_audio_noise_reduction", "input_audio_transcription", "instructions", "max_response_output_tokens", "modalities", "model", "output_audio_format", "temperature", "tool_choice", "tools", "turn_detection", "voice", "extra_headers", "extra_query", "extra_body", "timeout"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "openai.resources.beta.realtime.sessions.Sessions.create", "name": "create", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "input_audio_format", "input_audio_noise_reduction", "input_audio_transcription", "instructions", "max_response_output_tokens", "modalities", "model", "output_audio_format", "temperature", "tool_choice", "tools", "turn_detection", "voice", "extra_headers", "extra_query", "extra_body", "timeout"], "arg_types": ["openai.resources.beta.realtime.sessions.Sessions", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "pcm16"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "g711_ulaw"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "g711_alaw"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.realtime.session_create_params.InputAudioNoiseReduction"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.realtime.session_create_params.InputAudioTranscription"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "LiteralType", "fallback": "builtins.str", "value": "inf"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "text"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "audio"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.list"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "gpt-4o-realtime-preview"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "gpt-4o-realtime-preview-2024-10-01"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "gpt-4o-realtime-preview-2024-12-17"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "gpt-4o-mini-realtime-preview"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "gpt-4o-mini-realtime-preview-2024-12-17"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "pcm16"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "g711_ulaw"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "g711_alaw"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.realtime.session_create_params.Tool"}], "extra_attrs": null, "type_ref": "typing.Iterable"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.realtime.session_create_params.TurnDetection"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "LiteralType", "fallback": "builtins.str", "value": "alloy"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "ash"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "ballad"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "coral"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "echo"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "fable"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "onyx"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "nova"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "sage"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "shimmer"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "verse"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Headers"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Query"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "httpx._config.Timeout", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create of Sessions", "ret_type": "openai.types.beta.realtime.session_create_response.SessionCreateResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "with_raw_response": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai.resources.beta.realtime.sessions.Sessions.with_raw_response", "name": "with_raw_response", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.resources.beta.realtime.sessions.Sessions"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "with_raw_response of Sessions", "ret_type": "openai.resources.beta.realtime.sessions.SessionsWithRawResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai.resources.beta.realtime.sessions.Sessions.with_raw_response", "name": "with_raw_response", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.resources.beta.realtime.sessions.Sessions"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "with_raw_response of Sessions", "ret_type": "openai.resources.beta.realtime.sessions.SessionsWithRawResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "with_streaming_response": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai.resources.beta.realtime.sessions.Sessions.with_streaming_response", "name": "with_streaming_response", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.resources.beta.realtime.sessions.Sessions"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "with_streaming_response of Sessions", "ret_type": "openai.resources.beta.realtime.sessions.SessionsWithStreamingResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai.resources.beta.realtime.sessions.Sessions.with_streaming_response", "name": "with_streaming_response", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.resources.beta.realtime.sessions.Sessions"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "with_streaming_response of Sessions", "ret_type": "openai.resources.beta.realtime.sessions.SessionsWithStreamingResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.resources.beta.realtime.sessions.Sessions.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "openai.resources.beta.realtime.sessions.Sessions", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SessionsWithRawResponse": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "openai.resources.beta.realtime.sessions.SessionsWithRawResponse", "name": "SessionsWithRawResponse", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "openai.resources.beta.realtime.sessions.SessionsWithRawResponse", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "openai.resources.beta.realtime.sessions", "mro": ["openai.resources.beta.realtime.sessions.SessionsWithRawResponse", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "sessions"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "openai.resources.beta.realtime.sessions.SessionsWithRawResponse.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "sessions"], "arg_types": ["openai.resources.beta.realtime.sessions.SessionsWithRawResponse", "openai.resources.beta.realtime.sessions.Sessions"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of SessionsWithRawResponse", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_sessions": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "openai.resources.beta.realtime.sessions.SessionsWithRawResponse._sessions", "name": "_sessions", "setter_type": null, "type": "openai.resources.beta.realtime.sessions.Sessions"}}, "create": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "openai.resources.beta.realtime.sessions.SessionsWithRawResponse.create", "name": "create", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["input_audio_format", "input_audio_noise_reduction", "input_audio_transcription", "instructions", "max_response_output_tokens", "modalities", "model", "output_audio_format", "temperature", "tool_choice", "tools", "turn_detection", "voice", "extra_headers", "extra_query", "extra_body", "timeout"], "arg_types": [{".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "pcm16"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "g711_ulaw"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "g711_alaw"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.realtime.session_create_params.InputAudioNoiseReduction"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.realtime.session_create_params.InputAudioTranscription"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "LiteralType", "fallback": "builtins.str", "value": "inf"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "text"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "audio"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.list"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "gpt-4o-realtime-preview"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "gpt-4o-realtime-preview-2024-10-01"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "gpt-4o-realtime-preview-2024-12-17"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "gpt-4o-mini-realtime-preview"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "gpt-4o-mini-realtime-preview-2024-12-17"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "pcm16"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "g711_ulaw"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "g711_alaw"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.realtime.session_create_params.Tool"}], "extra_attrs": null, "type_ref": "typing.Iterable"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.realtime.session_create_params.TurnDetection"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "LiteralType", "fallback": "builtins.str", "value": "alloy"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "ash"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "ballad"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "coral"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "echo"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "fable"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "onyx"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "nova"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "sage"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "shimmer"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "verse"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Headers"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Query"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "httpx._config.Timeout", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": ["openai.types.beta.realtime.session_create_response.SessionCreateResponse"], "extra_attrs": null, "type_ref": "openai._legacy_response.LegacyAPIResponse"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.resources.beta.realtime.sessions.SessionsWithRawResponse.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "openai.resources.beta.realtime.sessions.SessionsWithRawResponse", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SessionsWithStreamingResponse": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "openai.resources.beta.realtime.sessions.SessionsWithStreamingResponse", "name": "SessionsWithStreamingResponse", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "openai.resources.beta.realtime.sessions.SessionsWithStreamingResponse", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "openai.resources.beta.realtime.sessions", "mro": ["openai.resources.beta.realtime.sessions.SessionsWithStreamingResponse", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "sessions"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "openai.resources.beta.realtime.sessions.SessionsWithStreamingResponse.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "sessions"], "arg_types": ["openai.resources.beta.realtime.sessions.SessionsWithStreamingResponse", "openai.resources.beta.realtime.sessions.Sessions"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of SessionsWithStreamingResponse", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_sessions": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "openai.resources.beta.realtime.sessions.SessionsWithStreamingResponse._sessions", "name": "_sessions", "setter_type": null, "type": "openai.resources.beta.realtime.sessions.Sessions"}}, "create": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "openai.resources.beta.realtime.sessions.SessionsWithStreamingResponse.create", "name": "create", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["input_audio_format", "input_audio_noise_reduction", "input_audio_transcription", "instructions", "max_response_output_tokens", "modalities", "model", "output_audio_format", "temperature", "tool_choice", "tools", "turn_detection", "voice", "extra_headers", "extra_query", "extra_body", "timeout"], "arg_types": [{".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "pcm16"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "g711_ulaw"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "g711_alaw"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.realtime.session_create_params.InputAudioNoiseReduction"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.realtime.session_create_params.InputAudioTranscription"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "LiteralType", "fallback": "builtins.str", "value": "inf"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "text"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "audio"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.list"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "gpt-4o-realtime-preview"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "gpt-4o-realtime-preview-2024-10-01"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "gpt-4o-realtime-preview-2024-12-17"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "gpt-4o-mini-realtime-preview"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "gpt-4o-mini-realtime-preview-2024-12-17"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "pcm16"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "g711_ulaw"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "g711_alaw"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.realtime.session_create_params.Tool"}], "extra_attrs": null, "type_ref": "typing.Iterable"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.realtime.session_create_params.TurnDetection"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "LiteralType", "fallback": "builtins.str", "value": "alloy"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "ash"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "ballad"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "coral"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "echo"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "fable"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "onyx"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "nova"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "sage"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "shimmer"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "verse"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Headers"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Query"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "httpx._config.Timeout", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["openai.types.beta.realtime.session_create_response.SessionCreateResponse"], "extra_attrs": null, "type_ref": "openai._response.APIResponse"}], "extra_attrs": null, "type_ref": "openai._response.ResponseContextManager"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.resources.beta.realtime.sessions.SessionsWithStreamingResponse.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "openai.resources.beta.realtime.sessions.SessionsWithStreamingResponse", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SyncAPIResource": {".class": "SymbolTableNode", "cross_ref": "openai._resource.SyncAPIResource", "kind": "Gdef", "module_public": false}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef", "module_public": false}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "openai.resources.beta.realtime.sessions.__all__", "name": "__all__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.resources.beta.realtime.sessions.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.resources.beta.realtime.sessions.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.resources.beta.realtime.sessions.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.resources.beta.realtime.sessions.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.resources.beta.realtime.sessions.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.resources.beta.realtime.sessions.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "_legacy_response": {".class": "SymbolTableNode", "cross_ref": "openai._legacy_response", "kind": "Gdef", "module_public": false}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef", "module_public": false}, "async_maybe_transform": {".class": "SymbolTableNode", "cross_ref": "openai._utils._transform.async_maybe_transform", "kind": "Gdef", "module_public": false}, "async_to_streamed_response_wrapper": {".class": "SymbolTableNode", "cross_ref": "openai._response.async_to_streamed_response_wrapper", "kind": "Gdef", "module_public": false}, "cached_property": {".class": "SymbolTableNode", "cross_ref": "openai._compat.cached_property", "kind": "Gdef", "module_public": false}, "httpx": {".class": "SymbolTableNode", "cross_ref": "httpx", "kind": "Gdef", "module_public": false}, "make_request_options": {".class": "SymbolTableNode", "cross_ref": "openai._base_client.make_request_options", "kind": "Gdef", "module_public": false}, "maybe_transform": {".class": "SymbolTableNode", "cross_ref": "openai._utils._transform.maybe_transform", "kind": "Gdef", "module_public": false}, "session_create_params": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.realtime.session_create_params", "kind": "Gdef", "module_public": false}, "to_streamed_response_wrapper": {".class": "SymbolTableNode", "cross_ref": "openai._response.to_streamed_response_wrapper", "kind": "Gdef", "module_public": false}}, "path": "/home/<USER>/.pyenv/versions/3.12.5/lib/python3.12/site-packages/openai/resources/beta/realtime/sessions.py"}