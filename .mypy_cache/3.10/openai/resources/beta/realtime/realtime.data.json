{".class": "MypyFile", "_fullname": "openai.resources.beta.realtime.realtime", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_public": false}, "AsyncAPIResource": {".class": "SymbolTableNode", "cross_ref": "openai._resource.AsyncAPIResource", "kind": "Gdef", "module_public": false}, "AsyncIterator": {".class": "SymbolTableNode", "cross_ref": "typing.AsyncIterator", "kind": "Gdef", "module_public": false}, "AsyncOpenAI": {".class": "SymbolTableNode", "cross_ref": "openai._client.AsyncOpenAI", "kind": "Gdef", "module_public": false}, "AsyncRealtime": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["openai._resource.AsyncAPIResource"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "openai.resources.beta.realtime.realtime.AsyncRealtime", "name": "AsyncRealtime", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "openai.resources.beta.realtime.realtime.AsyncRealtime", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "openai.resources.beta.realtime.realtime", "mro": ["openai.resources.beta.realtime.realtime.AsyncRealtime", "openai._resource.AsyncAPIResource", "builtins.object"], "names": {".class": "SymbolTable", "connect": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 3, 5, 5, 5], "arg_names": ["self", "model", "extra_query", "extra_headers", "websocket_connection_options"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "openai.resources.beta.realtime.realtime.AsyncRealtime.connect", "name": "connect", "type": {".class": "CallableType", "arg_kinds": [0, 3, 5, 5, 5], "arg_names": ["self", "model", "extra_query", "extra_headers", "websocket_connection_options"], "arg_types": ["openai.resources.beta.realtime.realtime.AsyncRealtime", "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Query"}, {".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Headers"}, {".class": "TypeAliasType", "args": [], "type_ref": "openai.types.websocket_connection_options.WebsocketConnectionOptions"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "connect of AsyncRealtime", "ret_type": "openai.resources.beta.realtime.realtime.AsyncRealtimeConnectionManager", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "sessions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai.resources.beta.realtime.realtime.AsyncRealtime.sessions", "name": "sessions", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.resources.beta.realtime.realtime.AsyncRealtime"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "sessions of AsyncRealtime", "ret_type": "openai.resources.beta.realtime.sessions.AsyncSessions", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai.resources.beta.realtime.realtime.AsyncRealtime.sessions", "name": "sessions", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.resources.beta.realtime.realtime.AsyncRealtime"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "sessions of AsyncRealtime", "ret_type": "openai.resources.beta.realtime.sessions.AsyncSessions", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "transcription_sessions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai.resources.beta.realtime.realtime.AsyncRealtime.transcription_sessions", "name": "transcription_sessions", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.resources.beta.realtime.realtime.AsyncRealtime"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "transcription_sessions of AsyncRealtime", "ret_type": "openai.resources.beta.realtime.transcription_sessions.AsyncTranscriptionSessions", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai.resources.beta.realtime.realtime.AsyncRealtime.transcription_sessions", "name": "transcription_sessions", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.resources.beta.realtime.realtime.AsyncRealtime"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "transcription_sessions of AsyncRealtime", "ret_type": "openai.resources.beta.realtime.transcription_sessions.AsyncTranscriptionSessions", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "with_raw_response": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai.resources.beta.realtime.realtime.AsyncRealtime.with_raw_response", "name": "with_raw_response", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.resources.beta.realtime.realtime.AsyncRealtime"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "with_raw_response of AsyncRealtime", "ret_type": "openai.resources.beta.realtime.realtime.AsyncRealtimeWithRawResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai.resources.beta.realtime.realtime.AsyncRealtime.with_raw_response", "name": "with_raw_response", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.resources.beta.realtime.realtime.AsyncRealtime"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "with_raw_response of AsyncRealtime", "ret_type": "openai.resources.beta.realtime.realtime.AsyncRealtimeWithRawResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "with_streaming_response": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai.resources.beta.realtime.realtime.AsyncRealtime.with_streaming_response", "name": "with_streaming_response", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.resources.beta.realtime.realtime.AsyncRealtime"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "with_streaming_response of AsyncRealtime", "ret_type": "openai.resources.beta.realtime.realtime.AsyncRealtimeWithStreamingResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai.resources.beta.realtime.realtime.AsyncRealtime.with_streaming_response", "name": "with_streaming_response", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.resources.beta.realtime.realtime.AsyncRealtime"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "with_streaming_response of AsyncRealtime", "ret_type": "openai.resources.beta.realtime.realtime.AsyncRealtimeWithStreamingResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.resources.beta.realtime.realtime.AsyncRealtime.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "openai.resources.beta.realtime.realtime.AsyncRealtime", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "AsyncRealtimeConnection": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "openai.resources.beta.realtime.realtime.AsyncRealtimeConnection", "name": "AsyncRealtimeConnection", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "openai.resources.beta.realtime.realtime.AsyncRealtimeConnection", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "openai.resources.beta.realtime.realtime", "mro": ["openai.resources.beta.realtime.realtime.AsyncRealtimeConnection", "builtins.object"], "names": {".class": "SymbolTable", "__aiter__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_generator", "is_coroutine", "is_async_generator", "is_trivial_self"], "fullname": "openai.resources.beta.realtime.realtime.AsyncRealtimeConnection.__aiter__", "name": "__aiter__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.resources.beta.realtime.realtime.AsyncRealtimeConnection"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__aiter__ of AsyncRealtimeConnection", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.realtime.realtime_server_event.RealtimeServerEvent"}], "extra_attrs": null, "type_ref": "typing.AsyncIterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "connection"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "openai.resources.beta.realtime.realtime.AsyncRealtimeConnection.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "connection"], "arg_types": ["openai.resources.beta.realtime.realtime.AsyncRealtimeConnection", {".class": "AnyType", "missing_import_name": "openai.resources.beta.realtime.realtime.AsyncWebsocketConnection", "source_any": null, "type_of_any": 3}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of AsyncRealtimeConnection", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_connection": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "openai.resources.beta.realtime.realtime.AsyncRealtimeConnection._connection", "name": "_connection", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "openai.resources.beta.realtime.realtime.AsyncWebsocketConnection", "source_any": null, "type_of_any": 3}}}, "close": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5], "arg_names": ["self", "code", "reason"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "openai.resources.beta.realtime.realtime.AsyncRealtimeConnection.close", "name": "close", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5], "arg_names": ["self", "code", "reason"], "arg_types": ["openai.resources.beta.realtime.realtime.AsyncRealtimeConnection", "builtins.int", "builtins.str"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "close of AsyncRealtimeConnection", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "conversation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "openai.resources.beta.realtime.realtime.AsyncRealtimeConnection.conversation", "name": "conversation", "setter_type": null, "type": "openai.resources.beta.realtime.realtime.AsyncRealtimeConversationResource"}}, "input_audio_buffer": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "openai.resources.beta.realtime.realtime.AsyncRealtimeConnection.input_audio_buffer", "name": "input_audio_buffer", "setter_type": null, "type": "openai.resources.beta.realtime.realtime.AsyncRealtimeInputAudioBufferResource"}}, "output_audio_buffer": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "openai.resources.beta.realtime.realtime.AsyncRealtimeConnection.output_audio_buffer", "name": "output_audio_buffer", "setter_type": null, "type": "openai.resources.beta.realtime.realtime.AsyncRealtimeOutputAudioBufferResource"}}, "parse_event": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "data"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "openai.resources.beta.realtime.realtime.AsyncRealtimeConnection.parse_event", "name": "parse_event", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "data"], "arg_types": ["openai.resources.beta.realtime.realtime.AsyncRealtimeConnection", {".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "parse_event of AsyncRealtimeConnection", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.realtime.realtime_server_event.RealtimeServerEvent"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "recv": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "openai.resources.beta.realtime.realtime.AsyncRealtimeConnection.recv", "name": "recv", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.resources.beta.realtime.realtime.AsyncRealtimeConnection"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "recv of AsyncRealtimeConnection", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.realtime.realtime_server_event.RealtimeServerEvent"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "recv_bytes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "openai.resources.beta.realtime.realtime.AsyncRealtimeConnection.recv_bytes", "name": "recv_bytes", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.resources.beta.realtime.realtime.AsyncRealtimeConnection"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "recv_bytes of AsyncRealtimeConnection", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "builtins.bytes"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "response": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "openai.resources.beta.realtime.realtime.AsyncRealtimeConnection.response", "name": "response", "setter_type": null, "type": "openai.resources.beta.realtime.realtime.AsyncRealtimeResponseResource"}}, "send": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "event"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "openai.resources.beta.realtime.realtime.AsyncRealtimeConnection.send", "name": "send", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "event"], "arg_types": ["openai.resources.beta.realtime.realtime.AsyncRealtimeConnection", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.realtime.realtime_client_event.RealtimeClientEvent"}, {".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.realtime.realtime_client_event_param.RealtimeClientEventParam"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "send of AsyncRealtimeConnection", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "session": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "openai.resources.beta.realtime.realtime.AsyncRealtimeConnection.session", "name": "session", "setter_type": null, "type": "openai.resources.beta.realtime.realtime.AsyncRealtimeSessionResource"}}, "transcription_session": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "openai.resources.beta.realtime.realtime.AsyncRealtimeConnection.transcription_session", "name": "transcription_session", "setter_type": null, "type": "openai.resources.beta.realtime.realtime.AsyncRealtimeTranscriptionSessionResource"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.resources.beta.realtime.realtime.AsyncRealtimeConnection.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "openai.resources.beta.realtime.realtime.AsyncRealtimeConnection", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "AsyncRealtimeConnectionManager": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "openai.resources.beta.realtime.realtime.AsyncRealtimeConnectionManager", "name": "AsyncRealtimeConnectionManager", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "openai.resources.beta.realtime.realtime.AsyncRealtimeConnectionManager", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "openai.resources.beta.realtime.realtime", "mro": ["openai.resources.beta.realtime.realtime.AsyncRealtimeConnectionManager", "builtins.object"], "names": {".class": "SymbolTable", "__aenter__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "openai.resources.beta.realtime.realtime.AsyncRealtimeConnectionManager.__aenter__", "name": "__aenter__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.resources.beta.realtime.realtime.AsyncRealtimeConnectionManager"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__aenter__ of AsyncRealtimeConnectionManager", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "openai.resources.beta.realtime.realtime.AsyncRealtimeConnection"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__aexit__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "exc_type", "exc", "exc_tb"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "openai.resources.beta.realtime.realtime.AsyncRealtimeConnectionManager.__aexit__", "name": "__aexit__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "exc_type", "exc", "exc_tb"], "arg_types": ["openai.resources.beta.realtime.realtime.AsyncRealtimeConnectionManager", {".class": "UnionType", "items": [{".class": "TypeType", "item": "builtins.BaseException"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.BaseException", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["types.TracebackType", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__aexit__ of AsyncRealtimeConnectionManager", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__client": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "openai.resources.beta.realtime.realtime.AsyncRealtimeConnectionManager.__client", "name": "__client", "setter_type": null, "type": "openai._client.AsyncOpenAI"}}, "__connection": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "openai.resources.beta.realtime.realtime.AsyncRealtimeConnectionManager.__connection", "name": "__connection", "setter_type": null, "type": {".class": "UnionType", "items": ["openai.resources.beta.realtime.realtime.AsyncRealtimeConnection", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "__extra_headers": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "openai.resources.beta.realtime.realtime.AsyncRealtimeConnectionManager.__extra_headers", "name": "__extra_headers", "setter_type": null, "type": {".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Headers"}}}, "__extra_query": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "openai.resources.beta.realtime.realtime.AsyncRealtimeConnectionManager.__extra_query", "name": "__extra_query", "setter_type": null, "type": {".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Query"}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 3, 3, 3, 3, 3], "arg_names": ["self", "client", "model", "extra_query", "extra_headers", "websocket_connection_options"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "openai.resources.beta.realtime.realtime.AsyncRealtimeConnectionManager.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 3, 3, 3, 3, 3], "arg_names": ["self", "client", "model", "extra_query", "extra_headers", "websocket_connection_options"], "arg_types": ["openai.resources.beta.realtime.realtime.AsyncRealtimeConnectionManager", "openai._client.AsyncOpenAI", "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Query"}, {".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Headers"}, {".class": "TypeAliasType", "args": [], "type_ref": "openai.types.websocket_connection_options.WebsocketConnectionOptions"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of AsyncRealtimeConnectionManager", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__model": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "openai.resources.beta.realtime.realtime.AsyncRealtimeConnectionManager.__model", "name": "__model", "setter_type": null, "type": "builtins.str"}}, "__websocket_connection_options": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "openai.resources.beta.realtime.realtime.AsyncRealtimeConnectionManager.__websocket_connection_options", "name": "__websocket_connection_options", "setter_type": null, "type": {".class": "TypeAliasType", "args": [], "type_ref": "openai.types.websocket_connection_options.WebsocketConnectionOptions"}}}, "_prepare_url": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "openai.resources.beta.realtime.realtime.AsyncRealtimeConnectionManager._prepare_url", "name": "_prepare_url", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.resources.beta.realtime.realtime.AsyncRealtimeConnectionManager"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_prepare_url of AsyncRealtimeConnectionManager", "ret_type": "httpx._urls.URL", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "enter": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "openai.resources.beta.realtime.realtime.AsyncRealtimeConnectionManager.enter", "name": "enter", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.resources.beta.realtime.realtime.AsyncRealtimeConnectionManager"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "openai.resources.beta.realtime.realtime.AsyncRealtimeConnection"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.resources.beta.realtime.realtime.AsyncRealtimeConnectionManager.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "openai.resources.beta.realtime.realtime.AsyncRealtimeConnectionManager", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "AsyncRealtimeConversationItemResource": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["openai.resources.beta.realtime.realtime.BaseAsyncRealtimeConnectionResource"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "openai.resources.beta.realtime.realtime.AsyncRealtimeConversationItemResource", "name": "AsyncRealtimeConversationItemResource", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "openai.resources.beta.realtime.realtime.AsyncRealtimeConversationItemResource", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "openai.resources.beta.realtime.realtime", "mro": ["openai.resources.beta.realtime.realtime.AsyncRealtimeConversationItemResource", "openai.resources.beta.realtime.realtime.BaseAsyncRealtimeConnectionResource", "builtins.object"], "names": {".class": "SymbolTable", "create": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 3, 5, 5], "arg_names": ["self", "item", "event_id", "previous_item_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "openai.resources.beta.realtime.realtime.AsyncRealtimeConversationItemResource.create", "name": "create", "type": {".class": "CallableType", "arg_kinds": [0, 3, 5, 5], "arg_names": ["self", "item", "event_id", "previous_item_id"], "arg_types": ["openai.resources.beta.realtime.realtime.AsyncRealtimeConversationItemResource", {".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.realtime.conversation_item_param.ConversationItemParam"}, {".class": "UnionType", "items": ["builtins.str", "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", "openai._types.NotGiven"], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create of AsyncRealtimeConversationItemResource", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "delete": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 3, 5], "arg_names": ["self", "item_id", "event_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "openai.resources.beta.realtime.realtime.AsyncRealtimeConversationItemResource.delete", "name": "delete", "type": {".class": "CallableType", "arg_kinds": [0, 3, 5], "arg_names": ["self", "item_id", "event_id"], "arg_types": ["openai.resources.beta.realtime.realtime.AsyncRealtimeConversationItemResource", "builtins.str", {".class": "UnionType", "items": ["builtins.str", "openai._types.NotGiven"], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "delete of AsyncRealtimeConversationItemResource", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "retrieve": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 3, 5], "arg_names": ["self", "item_id", "event_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "openai.resources.beta.realtime.realtime.AsyncRealtimeConversationItemResource.retrieve", "name": "retrieve", "type": {".class": "CallableType", "arg_kinds": [0, 3, 5], "arg_names": ["self", "item_id", "event_id"], "arg_types": ["openai.resources.beta.realtime.realtime.AsyncRealtimeConversationItemResource", "builtins.str", {".class": "UnionType", "items": ["builtins.str", "openai._types.NotGiven"], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "retrieve of AsyncRealtimeConversationItemResource", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "truncate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 3, 3, 3, 5], "arg_names": ["self", "audio_end_ms", "content_index", "item_id", "event_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "openai.resources.beta.realtime.realtime.AsyncRealtimeConversationItemResource.truncate", "name": "truncate", "type": {".class": "CallableType", "arg_kinds": [0, 3, 3, 3, 5], "arg_names": ["self", "audio_end_ms", "content_index", "item_id", "event_id"], "arg_types": ["openai.resources.beta.realtime.realtime.AsyncRealtimeConversationItemResource", "builtins.int", "builtins.int", "builtins.str", {".class": "UnionType", "items": ["builtins.str", "openai._types.NotGiven"], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "truncate of AsyncRealtimeConversationItemResource", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.resources.beta.realtime.realtime.AsyncRealtimeConversationItemResource.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "openai.resources.beta.realtime.realtime.AsyncRealtimeConversationItemResource", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "AsyncRealtimeConversationResource": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["openai.resources.beta.realtime.realtime.BaseAsyncRealtimeConnectionResource"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "openai.resources.beta.realtime.realtime.AsyncRealtimeConversationResource", "name": "AsyncRealtimeConversationResource", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "openai.resources.beta.realtime.realtime.AsyncRealtimeConversationResource", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "openai.resources.beta.realtime.realtime", "mro": ["openai.resources.beta.realtime.realtime.AsyncRealtimeConversationResource", "openai.resources.beta.realtime.realtime.BaseAsyncRealtimeConnectionResource", "builtins.object"], "names": {".class": "SymbolTable", "item": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai.resources.beta.realtime.realtime.AsyncRealtimeConversationResource.item", "name": "item", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.resources.beta.realtime.realtime.AsyncRealtimeConversationResource"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "item of AsyncRealtimeConversationResource", "ret_type": "openai.resources.beta.realtime.realtime.AsyncRealtimeConversationItemResource", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai.resources.beta.realtime.realtime.AsyncRealtimeConversationResource.item", "name": "item", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.resources.beta.realtime.realtime.AsyncRealtimeConversationResource"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "item of AsyncRealtimeConversationResource", "ret_type": "openai.resources.beta.realtime.realtime.AsyncRealtimeConversationItemResource", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.resources.beta.realtime.realtime.AsyncRealtimeConversationResource.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "openai.resources.beta.realtime.realtime.AsyncRealtimeConversationResource", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "AsyncRealtimeInputAudioBufferResource": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["openai.resources.beta.realtime.realtime.BaseAsyncRealtimeConnectionResource"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "openai.resources.beta.realtime.realtime.AsyncRealtimeInputAudioBufferResource", "name": "AsyncRealtimeInputAudioBufferResource", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "openai.resources.beta.realtime.realtime.AsyncRealtimeInputAudioBufferResource", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "openai.resources.beta.realtime.realtime", "mro": ["openai.resources.beta.realtime.realtime.AsyncRealtimeInputAudioBufferResource", "openai.resources.beta.realtime.realtime.BaseAsyncRealtimeConnectionResource", "builtins.object"], "names": {".class": "SymbolTable", "append": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 3, 5], "arg_names": ["self", "audio", "event_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "openai.resources.beta.realtime.realtime.AsyncRealtimeInputAudioBufferResource.append", "name": "append", "type": {".class": "CallableType", "arg_kinds": [0, 3, 5], "arg_names": ["self", "audio", "event_id"], "arg_types": ["openai.resources.beta.realtime.realtime.AsyncRealtimeInputAudioBufferResource", "builtins.str", {".class": "UnionType", "items": ["builtins.str", "openai._types.NotGiven"], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "append of AsyncRealtimeInputAudioBufferResource", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "clear": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5], "arg_names": ["self", "event_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "openai.resources.beta.realtime.realtime.AsyncRealtimeInputAudioBufferResource.clear", "name": "clear", "type": {".class": "CallableType", "arg_kinds": [0, 5], "arg_names": ["self", "event_id"], "arg_types": ["openai.resources.beta.realtime.realtime.AsyncRealtimeInputAudioBufferResource", {".class": "UnionType", "items": ["builtins.str", "openai._types.NotGiven"], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "clear of AsyncRealtimeInputAudioBufferResource", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "commit": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5], "arg_names": ["self", "event_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "openai.resources.beta.realtime.realtime.AsyncRealtimeInputAudioBufferResource.commit", "name": "commit", "type": {".class": "CallableType", "arg_kinds": [0, 5], "arg_names": ["self", "event_id"], "arg_types": ["openai.resources.beta.realtime.realtime.AsyncRealtimeInputAudioBufferResource", {".class": "UnionType", "items": ["builtins.str", "openai._types.NotGiven"], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "commit of AsyncRealtimeInputAudioBufferResource", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.resources.beta.realtime.realtime.AsyncRealtimeInputAudioBufferResource.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "openai.resources.beta.realtime.realtime.AsyncRealtimeInputAudioBufferResource", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "AsyncRealtimeOutputAudioBufferResource": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["openai.resources.beta.realtime.realtime.BaseAsyncRealtimeConnectionResource"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "openai.resources.beta.realtime.realtime.AsyncRealtimeOutputAudioBufferResource", "name": "AsyncRealtimeOutputAudioBufferResource", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "openai.resources.beta.realtime.realtime.AsyncRealtimeOutputAudioBufferResource", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "openai.resources.beta.realtime.realtime", "mro": ["openai.resources.beta.realtime.realtime.AsyncRealtimeOutputAudioBufferResource", "openai.resources.beta.realtime.realtime.BaseAsyncRealtimeConnectionResource", "builtins.object"], "names": {".class": "SymbolTable", "clear": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5], "arg_names": ["self", "event_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "openai.resources.beta.realtime.realtime.AsyncRealtimeOutputAudioBufferResource.clear", "name": "clear", "type": {".class": "CallableType", "arg_kinds": [0, 5], "arg_names": ["self", "event_id"], "arg_types": ["openai.resources.beta.realtime.realtime.AsyncRealtimeOutputAudioBufferResource", {".class": "UnionType", "items": ["builtins.str", "openai._types.NotGiven"], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "clear of AsyncRealtimeOutputAudioBufferResource", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.resources.beta.realtime.realtime.AsyncRealtimeOutputAudioBufferResource.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "openai.resources.beta.realtime.realtime.AsyncRealtimeOutputAudioBufferResource", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "AsyncRealtimeResponseResource": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["openai.resources.beta.realtime.realtime.BaseAsyncRealtimeConnectionResource"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "openai.resources.beta.realtime.realtime.AsyncRealtimeResponseResource", "name": "AsyncRealtimeResponseResource", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "openai.resources.beta.realtime.realtime.AsyncRealtimeResponseResource", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "openai.resources.beta.realtime.realtime", "mro": ["openai.resources.beta.realtime.realtime.AsyncRealtimeResponseResource", "openai.resources.beta.realtime.realtime.BaseAsyncRealtimeConnectionResource", "builtins.object"], "names": {".class": "SymbolTable", "cancel": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5], "arg_names": ["self", "event_id", "response_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "openai.resources.beta.realtime.realtime.AsyncRealtimeResponseResource.cancel", "name": "cancel", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5], "arg_names": ["self", "event_id", "response_id"], "arg_types": ["openai.resources.beta.realtime.realtime.AsyncRealtimeResponseResource", {".class": "UnionType", "items": ["builtins.str", "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", "openai._types.NotGiven"], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "cancel of AsyncRealtimeResponseResource", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "create": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5], "arg_names": ["self", "event_id", "response"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "openai.resources.beta.realtime.realtime.AsyncRealtimeResponseResource.create", "name": "create", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5], "arg_names": ["self", "event_id", "response"], "arg_types": ["openai.resources.beta.realtime.realtime.AsyncRealtimeResponseResource", {".class": "UnionType", "items": ["builtins.str", "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.realtime.response_create_event_param.Response"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create of AsyncRealtimeResponseResource", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.resources.beta.realtime.realtime.AsyncRealtimeResponseResource.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "openai.resources.beta.realtime.realtime.AsyncRealtimeResponseResource", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "AsyncRealtimeSessionResource": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["openai.resources.beta.realtime.realtime.BaseAsyncRealtimeConnectionResource"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "openai.resources.beta.realtime.realtime.AsyncRealtimeSessionResource", "name": "AsyncRealtimeSessionResource", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "openai.resources.beta.realtime.realtime.AsyncRealtimeSessionResource", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "openai.resources.beta.realtime.realtime", "mro": ["openai.resources.beta.realtime.realtime.AsyncRealtimeSessionResource", "openai.resources.beta.realtime.realtime.BaseAsyncRealtimeConnectionResource", "builtins.object"], "names": {".class": "SymbolTable", "update": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 3, 5], "arg_names": ["self", "session", "event_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "openai.resources.beta.realtime.realtime.AsyncRealtimeSessionResource.update", "name": "update", "type": {".class": "CallableType", "arg_kinds": [0, 3, 5], "arg_names": ["self", "session", "event_id"], "arg_types": ["openai.resources.beta.realtime.realtime.AsyncRealtimeSessionResource", {".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.realtime.session_update_event_param.Session"}, {".class": "UnionType", "items": ["builtins.str", "openai._types.NotGiven"], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "update of AsyncRealtimeSessionResource", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.resources.beta.realtime.realtime.AsyncRealtimeSessionResource.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "openai.resources.beta.realtime.realtime.AsyncRealtimeSessionResource", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "AsyncRealtimeTranscriptionSessionResource": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["openai.resources.beta.realtime.realtime.BaseAsyncRealtimeConnectionResource"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "openai.resources.beta.realtime.realtime.AsyncRealtimeTranscriptionSessionResource", "name": "AsyncRealtimeTranscriptionSessionResource", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "openai.resources.beta.realtime.realtime.AsyncRealtimeTranscriptionSessionResource", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "openai.resources.beta.realtime.realtime", "mro": ["openai.resources.beta.realtime.realtime.AsyncRealtimeTranscriptionSessionResource", "openai.resources.beta.realtime.realtime.BaseAsyncRealtimeConnectionResource", "builtins.object"], "names": {".class": "SymbolTable", "update": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 3, 5], "arg_names": ["self", "session", "event_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "openai.resources.beta.realtime.realtime.AsyncRealtimeTranscriptionSessionResource.update", "name": "update", "type": {".class": "CallableType", "arg_kinds": [0, 3, 5], "arg_names": ["self", "session", "event_id"], "arg_types": ["openai.resources.beta.realtime.realtime.AsyncRealtimeTranscriptionSessionResource", {".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.realtime.transcription_session_update_param.Session"}, {".class": "UnionType", "items": ["builtins.str", "openai._types.NotGiven"], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "update of AsyncRealtimeTranscriptionSessionResource", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.resources.beta.realtime.realtime.AsyncRealtimeTranscriptionSessionResource.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "openai.resources.beta.realtime.realtime.AsyncRealtimeTranscriptionSessionResource", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "AsyncRealtimeWithRawResponse": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "openai.resources.beta.realtime.realtime.AsyncRealtimeWithRawResponse", "name": "AsyncRealtimeWithRawResponse", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "openai.resources.beta.realtime.realtime.AsyncRealtimeWithRawResponse", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "openai.resources.beta.realtime.realtime", "mro": ["openai.resources.beta.realtime.realtime.AsyncRealtimeWithRawResponse", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "realtime"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "openai.resources.beta.realtime.realtime.AsyncRealtimeWithRawResponse.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "realtime"], "arg_types": ["openai.resources.beta.realtime.realtime.AsyncRealtimeWithRawResponse", "openai.resources.beta.realtime.realtime.AsyncRealtime"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of AsyncRealtimeWithRawResponse", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_realtime": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "openai.resources.beta.realtime.realtime.AsyncRealtimeWithRawResponse._realtime", "name": "_realtime", "setter_type": null, "type": "openai.resources.beta.realtime.realtime.AsyncRealtime"}}, "sessions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai.resources.beta.realtime.realtime.AsyncRealtimeWithRawResponse.sessions", "name": "sessions", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.resources.beta.realtime.realtime.AsyncRealtimeWithRawResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "sessions of AsyncRealtimeWithRawResponse", "ret_type": "openai.resources.beta.realtime.sessions.AsyncSessionsWithRawResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai.resources.beta.realtime.realtime.AsyncRealtimeWithRawResponse.sessions", "name": "sessions", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.resources.beta.realtime.realtime.AsyncRealtimeWithRawResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "sessions of AsyncRealtimeWithRawResponse", "ret_type": "openai.resources.beta.realtime.sessions.AsyncSessionsWithRawResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "transcription_sessions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai.resources.beta.realtime.realtime.AsyncRealtimeWithRawResponse.transcription_sessions", "name": "transcription_sessions", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.resources.beta.realtime.realtime.AsyncRealtimeWithRawResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "transcription_sessions of AsyncRealtimeWithRawResponse", "ret_type": "openai.resources.beta.realtime.transcription_sessions.AsyncTranscriptionSessionsWithRawResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai.resources.beta.realtime.realtime.AsyncRealtimeWithRawResponse.transcription_sessions", "name": "transcription_sessions", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.resources.beta.realtime.realtime.AsyncRealtimeWithRawResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "transcription_sessions of AsyncRealtimeWithRawResponse", "ret_type": "openai.resources.beta.realtime.transcription_sessions.AsyncTranscriptionSessionsWithRawResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.resources.beta.realtime.realtime.AsyncRealtimeWithRawResponse.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "openai.resources.beta.realtime.realtime.AsyncRealtimeWithRawResponse", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "AsyncRealtimeWithStreamingResponse": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "openai.resources.beta.realtime.realtime.AsyncRealtimeWithStreamingResponse", "name": "AsyncRealtimeWithStreamingResponse", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "openai.resources.beta.realtime.realtime.AsyncRealtimeWithStreamingResponse", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "openai.resources.beta.realtime.realtime", "mro": ["openai.resources.beta.realtime.realtime.AsyncRealtimeWithStreamingResponse", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "realtime"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "openai.resources.beta.realtime.realtime.AsyncRealtimeWithStreamingResponse.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "realtime"], "arg_types": ["openai.resources.beta.realtime.realtime.AsyncRealtimeWithStreamingResponse", "openai.resources.beta.realtime.realtime.AsyncRealtime"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of AsyncRealtimeWithStreamingResponse", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_realtime": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "openai.resources.beta.realtime.realtime.AsyncRealtimeWithStreamingResponse._realtime", "name": "_realtime", "setter_type": null, "type": "openai.resources.beta.realtime.realtime.AsyncRealtime"}}, "sessions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai.resources.beta.realtime.realtime.AsyncRealtimeWithStreamingResponse.sessions", "name": "sessions", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.resources.beta.realtime.realtime.AsyncRealtimeWithStreamingResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "sessions of AsyncRealtimeWithStreamingResponse", "ret_type": "openai.resources.beta.realtime.sessions.AsyncSessionsWithStreamingResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai.resources.beta.realtime.realtime.AsyncRealtimeWithStreamingResponse.sessions", "name": "sessions", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.resources.beta.realtime.realtime.AsyncRealtimeWithStreamingResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "sessions of AsyncRealtimeWithStreamingResponse", "ret_type": "openai.resources.beta.realtime.sessions.AsyncSessionsWithStreamingResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "transcription_sessions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai.resources.beta.realtime.realtime.AsyncRealtimeWithStreamingResponse.transcription_sessions", "name": "transcription_sessions", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.resources.beta.realtime.realtime.AsyncRealtimeWithStreamingResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "transcription_sessions of AsyncRealtimeWithStreamingResponse", "ret_type": "openai.resources.beta.realtime.transcription_sessions.AsyncTranscriptionSessionsWithStreamingResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai.resources.beta.realtime.realtime.AsyncRealtimeWithStreamingResponse.transcription_sessions", "name": "transcription_sessions", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.resources.beta.realtime.realtime.AsyncRealtimeWithStreamingResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "transcription_sessions of AsyncRealtimeWithStreamingResponse", "ret_type": "openai.resources.beta.realtime.transcription_sessions.AsyncTranscriptionSessionsWithStreamingResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.resources.beta.realtime.realtime.AsyncRealtimeWithStreamingResponse.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "openai.resources.beta.realtime.realtime.AsyncRealtimeWithStreamingResponse", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "AsyncSessions": {".class": "SymbolTableNode", "cross_ref": "openai.resources.beta.realtime.sessions.AsyncSessions", "kind": "Gdef", "module_public": false}, "AsyncSessionsWithRawResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.beta.realtime.sessions.AsyncSessionsWithRawResponse", "kind": "Gdef", "module_public": false}, "AsyncSessionsWithStreamingResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.beta.realtime.sessions.AsyncSessionsWithStreamingResponse", "kind": "Gdef", "module_public": false}, "AsyncTranscriptionSessions": {".class": "SymbolTableNode", "cross_ref": "openai.resources.beta.realtime.transcription_sessions.AsyncTranscriptionSessions", "kind": "Gdef", "module_public": false}, "AsyncTranscriptionSessionsWithRawResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.beta.realtime.transcription_sessions.AsyncTranscriptionSessionsWithRawResponse", "kind": "Gdef", "module_public": false}, "AsyncTranscriptionSessionsWithStreamingResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.beta.realtime.transcription_sessions.AsyncTranscriptionSessionsWithStreamingResponse", "kind": "Gdef", "module_public": false}, "AsyncWebsocketConnection": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "openai.resources.beta.realtime.realtime.AsyncWebsocketConnection", "name": "AsyncWebsocketConnection", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "openai.resources.beta.realtime.realtime.AsyncWebsocketConnection", "source_any": null, "type_of_any": 3}}}, "BaseAsyncRealtimeConnectionResource": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "openai.resources.beta.realtime.realtime.BaseAsyncRealtimeConnectionResource", "name": "BaseAsyncRealtimeConnectionResource", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "openai.resources.beta.realtime.realtime.BaseAsyncRealtimeConnectionResource", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "openai.resources.beta.realtime.realtime", "mro": ["openai.resources.beta.realtime.realtime.BaseAsyncRealtimeConnectionResource", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "connection"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "openai.resources.beta.realtime.realtime.BaseAsyncRealtimeConnectionResource.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "connection"], "arg_types": ["openai.resources.beta.realtime.realtime.BaseAsyncRealtimeConnectionResource", "openai.resources.beta.realtime.realtime.AsyncRealtimeConnection"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of BaseAsyncRealtimeConnectionResource", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_connection": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "openai.resources.beta.realtime.realtime.BaseAsyncRealtimeConnectionResource._connection", "name": "_connection", "setter_type": null, "type": "openai.resources.beta.realtime.realtime.AsyncRealtimeConnection"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.resources.beta.realtime.realtime.BaseAsyncRealtimeConnectionResource.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "openai.resources.beta.realtime.realtime.BaseAsyncRealtimeConnectionResource", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "BaseModel": {".class": "SymbolTableNode", "cross_ref": "pydantic.main.BaseModel", "kind": "Gdef", "module_public": false}, "BaseRealtimeConnectionResource": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "openai.resources.beta.realtime.realtime.BaseRealtimeConnectionResource", "name": "BaseRealtimeConnectionResource", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "openai.resources.beta.realtime.realtime.BaseRealtimeConnectionResource", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "openai.resources.beta.realtime.realtime", "mro": ["openai.resources.beta.realtime.realtime.BaseRealtimeConnectionResource", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "connection"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "openai.resources.beta.realtime.realtime.BaseRealtimeConnectionResource.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "connection"], "arg_types": ["openai.resources.beta.realtime.realtime.BaseRealtimeConnectionResource", "openai.resources.beta.realtime.realtime.RealtimeConnection"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of BaseRealtimeConnectionResource", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_connection": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "openai.resources.beta.realtime.realtime.BaseRealtimeConnectionResource._connection", "name": "_connection", "setter_type": null, "type": "openai.resources.beta.realtime.realtime.RealtimeConnection"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.resources.beta.realtime.realtime.BaseRealtimeConnectionResource.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "openai.resources.beta.realtime.realtime.BaseRealtimeConnectionResource", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ConversationItemParam": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.realtime.conversation_item_param.ConversationItemParam", "kind": "Gdef", "module_public": false}, "Headers": {".class": "SymbolTableNode", "cross_ref": "openai._types.Headers", "kind": "Gdef", "module_public": false}, "Iterator": {".class": "SymbolTableNode", "cross_ref": "typing.Iterator", "kind": "Gdef", "module_public": false}, "NOT_GIVEN": {".class": "SymbolTableNode", "cross_ref": "openai._types.NOT_GIVEN", "kind": "Gdef", "module_public": false}, "NotGiven": {".class": "SymbolTableNode", "cross_ref": "openai._types.NotGiven", "kind": "Gdef", "module_public": false}, "OpenAI": {".class": "SymbolTableNode", "cross_ref": "openai._client.OpenAI", "kind": "Gdef", "module_public": false}, "OpenAIError": {".class": "SymbolTableNode", "cross_ref": "openai._exceptions.OpenAIError", "kind": "Gdef", "module_public": false}, "Query": {".class": "SymbolTableNode", "cross_ref": "openai._types.Query", "kind": "Gdef", "module_public": false}, "Realtime": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["openai._resource.SyncAPIResource"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "openai.resources.beta.realtime.realtime.Realtime", "name": "Realtime", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "openai.resources.beta.realtime.realtime.Realtime", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "openai.resources.beta.realtime.realtime", "mro": ["openai.resources.beta.realtime.realtime.Realtime", "openai._resource.SyncAPIResource", "builtins.object"], "names": {".class": "SymbolTable", "connect": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 3, 5, 5, 5], "arg_names": ["self", "model", "extra_query", "extra_headers", "websocket_connection_options"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "openai.resources.beta.realtime.realtime.Realtime.connect", "name": "connect", "type": {".class": "CallableType", "arg_kinds": [0, 3, 5, 5, 5], "arg_names": ["self", "model", "extra_query", "extra_headers", "websocket_connection_options"], "arg_types": ["openai.resources.beta.realtime.realtime.Realtime", "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Query"}, {".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Headers"}, {".class": "TypeAliasType", "args": [], "type_ref": "openai.types.websocket_connection_options.WebsocketConnectionOptions"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "connect of Realtime", "ret_type": "openai.resources.beta.realtime.realtime.RealtimeConnectionManager", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "sessions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai.resources.beta.realtime.realtime.Realtime.sessions", "name": "sessions", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.resources.beta.realtime.realtime.Realtime"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "sessions of Realtime", "ret_type": "openai.resources.beta.realtime.sessions.Sessions", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai.resources.beta.realtime.realtime.Realtime.sessions", "name": "sessions", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.resources.beta.realtime.realtime.Realtime"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "sessions of Realtime", "ret_type": "openai.resources.beta.realtime.sessions.Sessions", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "transcription_sessions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai.resources.beta.realtime.realtime.Realtime.transcription_sessions", "name": "transcription_sessions", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.resources.beta.realtime.realtime.Realtime"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "transcription_sessions of Realtime", "ret_type": "openai.resources.beta.realtime.transcription_sessions.TranscriptionSessions", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai.resources.beta.realtime.realtime.Realtime.transcription_sessions", "name": "transcription_sessions", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.resources.beta.realtime.realtime.Realtime"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "transcription_sessions of Realtime", "ret_type": "openai.resources.beta.realtime.transcription_sessions.TranscriptionSessions", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "with_raw_response": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai.resources.beta.realtime.realtime.Realtime.with_raw_response", "name": "with_raw_response", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.resources.beta.realtime.realtime.Realtime"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "with_raw_response of Realtime", "ret_type": "openai.resources.beta.realtime.realtime.RealtimeWithRawResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai.resources.beta.realtime.realtime.Realtime.with_raw_response", "name": "with_raw_response", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.resources.beta.realtime.realtime.Realtime"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "with_raw_response of Realtime", "ret_type": "openai.resources.beta.realtime.realtime.RealtimeWithRawResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "with_streaming_response": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai.resources.beta.realtime.realtime.Realtime.with_streaming_response", "name": "with_streaming_response", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.resources.beta.realtime.realtime.Realtime"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "with_streaming_response of Realtime", "ret_type": "openai.resources.beta.realtime.realtime.RealtimeWithStreamingResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai.resources.beta.realtime.realtime.Realtime.with_streaming_response", "name": "with_streaming_response", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.resources.beta.realtime.realtime.Realtime"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "with_streaming_response of Realtime", "ret_type": "openai.resources.beta.realtime.realtime.RealtimeWithStreamingResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.resources.beta.realtime.realtime.Realtime.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "openai.resources.beta.realtime.realtime.Realtime", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "RealtimeClientEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.realtime.realtime_client_event.RealtimeClientEvent", "kind": "Gdef", "module_public": false}, "RealtimeClientEventParam": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.realtime.realtime_client_event_param.RealtimeClientEventParam", "kind": "Gdef", "module_public": false}, "RealtimeConnection": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "openai.resources.beta.realtime.realtime.RealtimeConnection", "name": "RealtimeConnection", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "openai.resources.beta.realtime.realtime.RealtimeConnection", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "openai.resources.beta.realtime.realtime", "mro": ["openai.resources.beta.realtime.realtime.RealtimeConnection", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "connection"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "openai.resources.beta.realtime.realtime.RealtimeConnection.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "connection"], "arg_types": ["openai.resources.beta.realtime.realtime.RealtimeConnection", {".class": "AnyType", "missing_import_name": "openai.resources.beta.realtime.realtime.WebsocketConnection", "source_any": null, "type_of_any": 3}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of RealtimeConnection", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__iter__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "openai.resources.beta.realtime.realtime.RealtimeConnection.__iter__", "name": "__iter__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["openai.resources.beta.realtime.realtime.RealtimeConnection"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__iter__ of RealtimeConnection", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.realtime.realtime_server_event.RealtimeServerEvent"}], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_connection": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "openai.resources.beta.realtime.realtime.RealtimeConnection._connection", "name": "_connection", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "openai.resources.beta.realtime.realtime.WebsocketConnection", "source_any": null, "type_of_any": 3}}}, "close": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5], "arg_names": ["self", "code", "reason"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "openai.resources.beta.realtime.realtime.RealtimeConnection.close", "name": "close", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5], "arg_names": ["self", "code", "reason"], "arg_types": ["openai.resources.beta.realtime.realtime.RealtimeConnection", "builtins.int", "builtins.str"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "close of RealtimeConnection", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "conversation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "openai.resources.beta.realtime.realtime.RealtimeConnection.conversation", "name": "conversation", "setter_type": null, "type": "openai.resources.beta.realtime.realtime.RealtimeConversationResource"}}, "input_audio_buffer": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "openai.resources.beta.realtime.realtime.RealtimeConnection.input_audio_buffer", "name": "input_audio_buffer", "setter_type": null, "type": "openai.resources.beta.realtime.realtime.RealtimeInputAudioBufferResource"}}, "output_audio_buffer": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "openai.resources.beta.realtime.realtime.RealtimeConnection.output_audio_buffer", "name": "output_audio_buffer", "setter_type": null, "type": "openai.resources.beta.realtime.realtime.RealtimeOutputAudioBufferResource"}}, "parse_event": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "data"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "openai.resources.beta.realtime.realtime.RealtimeConnection.parse_event", "name": "parse_event", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "data"], "arg_types": ["openai.resources.beta.realtime.realtime.RealtimeConnection", {".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "parse_event of RealtimeConnection", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.realtime.realtime_server_event.RealtimeServerEvent"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "recv": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "openai.resources.beta.realtime.realtime.RealtimeConnection.recv", "name": "recv", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.resources.beta.realtime.realtime.RealtimeConnection"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "recv of RealtimeConnection", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.realtime.realtime_server_event.RealtimeServerEvent"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "recv_bytes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "openai.resources.beta.realtime.realtime.RealtimeConnection.recv_bytes", "name": "recv_bytes", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.resources.beta.realtime.realtime.RealtimeConnection"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "recv_bytes of RealtimeConnection", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "response": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "openai.resources.beta.realtime.realtime.RealtimeConnection.response", "name": "response", "setter_type": null, "type": "openai.resources.beta.realtime.realtime.RealtimeResponseResource"}}, "send": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "event"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "openai.resources.beta.realtime.realtime.RealtimeConnection.send", "name": "send", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "event"], "arg_types": ["openai.resources.beta.realtime.realtime.RealtimeConnection", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.realtime.realtime_client_event.RealtimeClientEvent"}, {".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.realtime.realtime_client_event_param.RealtimeClientEventParam"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "send of RealtimeConnection", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "session": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "openai.resources.beta.realtime.realtime.RealtimeConnection.session", "name": "session", "setter_type": null, "type": "openai.resources.beta.realtime.realtime.RealtimeSessionResource"}}, "transcription_session": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "openai.resources.beta.realtime.realtime.RealtimeConnection.transcription_session", "name": "transcription_session", "setter_type": null, "type": "openai.resources.beta.realtime.realtime.RealtimeTranscriptionSessionResource"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.resources.beta.realtime.realtime.RealtimeConnection.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "openai.resources.beta.realtime.realtime.RealtimeConnection", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "RealtimeConnectionManager": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "openai.resources.beta.realtime.realtime.RealtimeConnectionManager", "name": "RealtimeConnectionManager", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "openai.resources.beta.realtime.realtime.RealtimeConnectionManager", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "openai.resources.beta.realtime.realtime", "mro": ["openai.resources.beta.realtime.realtime.RealtimeConnectionManager", "builtins.object"], "names": {".class": "SymbolTable", "__client": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "openai.resources.beta.realtime.realtime.RealtimeConnectionManager.__client", "name": "__client", "setter_type": null, "type": "openai._client.OpenAI"}}, "__connection": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "openai.resources.beta.realtime.realtime.RealtimeConnectionManager.__connection", "name": "__connection", "setter_type": null, "type": {".class": "UnionType", "items": ["openai.resources.beta.realtime.realtime.RealtimeConnection", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "__enter__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "openai.resources.beta.realtime.realtime.RealtimeConnectionManager.__enter__", "name": "__enter__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["openai.resources.beta.realtime.realtime.RealtimeConnectionManager"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__enter__ of RealtimeConnectionManager", "ret_type": "openai.resources.beta.realtime.realtime.RealtimeConnection", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__exit__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": [null, null, null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "openai.resources.beta.realtime.realtime.RealtimeConnectionManager.__exit__", "name": "__exit__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": [null, null, null, null], "arg_types": ["openai.resources.beta.realtime.realtime.RealtimeConnectionManager", {".class": "UnionType", "items": [{".class": "TypeType", "item": "builtins.BaseException"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.BaseException", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["types.TracebackType", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__exit__ of RealtimeConnectionManager", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__extra_headers": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "openai.resources.beta.realtime.realtime.RealtimeConnectionManager.__extra_headers", "name": "__extra_headers", "setter_type": null, "type": {".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Headers"}}}, "__extra_query": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "openai.resources.beta.realtime.realtime.RealtimeConnectionManager.__extra_query", "name": "__extra_query", "setter_type": null, "type": {".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Query"}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 3, 3, 3, 3, 3], "arg_names": ["self", "client", "model", "extra_query", "extra_headers", "websocket_connection_options"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "openai.resources.beta.realtime.realtime.RealtimeConnectionManager.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 3, 3, 3, 3, 3], "arg_names": ["self", "client", "model", "extra_query", "extra_headers", "websocket_connection_options"], "arg_types": ["openai.resources.beta.realtime.realtime.RealtimeConnectionManager", "openai._client.OpenAI", "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Query"}, {".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Headers"}, {".class": "TypeAliasType", "args": [], "type_ref": "openai.types.websocket_connection_options.WebsocketConnectionOptions"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of RealtimeConnectionManager", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__model": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "openai.resources.beta.realtime.realtime.RealtimeConnectionManager.__model", "name": "__model", "setter_type": null, "type": "builtins.str"}}, "__websocket_connection_options": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "openai.resources.beta.realtime.realtime.RealtimeConnectionManager.__websocket_connection_options", "name": "__websocket_connection_options", "setter_type": null, "type": {".class": "TypeAliasType", "args": [], "type_ref": "openai.types.websocket_connection_options.WebsocketConnectionOptions"}}}, "_prepare_url": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "openai.resources.beta.realtime.realtime.RealtimeConnectionManager._prepare_url", "name": "_prepare_url", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.resources.beta.realtime.realtime.RealtimeConnectionManager"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_prepare_url of RealtimeConnectionManager", "ret_type": "httpx._urls.URL", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "enter": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "openai.resources.beta.realtime.realtime.RealtimeConnectionManager.enter", "name": "enter", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["openai.resources.beta.realtime.realtime.RealtimeConnectionManager"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": "openai.resources.beta.realtime.realtime.RealtimeConnection", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.resources.beta.realtime.realtime.RealtimeConnectionManager.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "openai.resources.beta.realtime.realtime.RealtimeConnectionManager", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "RealtimeConversationItemResource": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["openai.resources.beta.realtime.realtime.BaseRealtimeConnectionResource"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "openai.resources.beta.realtime.realtime.RealtimeConversationItemResource", "name": "RealtimeConversationItemResource", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "openai.resources.beta.realtime.realtime.RealtimeConversationItemResource", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "openai.resources.beta.realtime.realtime", "mro": ["openai.resources.beta.realtime.realtime.RealtimeConversationItemResource", "openai.resources.beta.realtime.realtime.BaseRealtimeConnectionResource", "builtins.object"], "names": {".class": "SymbolTable", "create": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 3, 5, 5], "arg_names": ["self", "item", "event_id", "previous_item_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "openai.resources.beta.realtime.realtime.RealtimeConversationItemResource.create", "name": "create", "type": {".class": "CallableType", "arg_kinds": [0, 3, 5, 5], "arg_names": ["self", "item", "event_id", "previous_item_id"], "arg_types": ["openai.resources.beta.realtime.realtime.RealtimeConversationItemResource", {".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.realtime.conversation_item_param.ConversationItemParam"}, {".class": "UnionType", "items": ["builtins.str", "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", "openai._types.NotGiven"], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create of RealtimeConversationItemResource", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "delete": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 3, 5], "arg_names": ["self", "item_id", "event_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "openai.resources.beta.realtime.realtime.RealtimeConversationItemResource.delete", "name": "delete", "type": {".class": "CallableType", "arg_kinds": [0, 3, 5], "arg_names": ["self", "item_id", "event_id"], "arg_types": ["openai.resources.beta.realtime.realtime.RealtimeConversationItemResource", "builtins.str", {".class": "UnionType", "items": ["builtins.str", "openai._types.NotGiven"], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "delete of RealtimeConversationItemResource", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "retrieve": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 3, 5], "arg_names": ["self", "item_id", "event_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "openai.resources.beta.realtime.realtime.RealtimeConversationItemResource.retrieve", "name": "retrieve", "type": {".class": "CallableType", "arg_kinds": [0, 3, 5], "arg_names": ["self", "item_id", "event_id"], "arg_types": ["openai.resources.beta.realtime.realtime.RealtimeConversationItemResource", "builtins.str", {".class": "UnionType", "items": ["builtins.str", "openai._types.NotGiven"], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "retrieve of RealtimeConversationItemResource", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "truncate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 3, 3, 3, 5], "arg_names": ["self", "audio_end_ms", "content_index", "item_id", "event_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "openai.resources.beta.realtime.realtime.RealtimeConversationItemResource.truncate", "name": "truncate", "type": {".class": "CallableType", "arg_kinds": [0, 3, 3, 3, 5], "arg_names": ["self", "audio_end_ms", "content_index", "item_id", "event_id"], "arg_types": ["openai.resources.beta.realtime.realtime.RealtimeConversationItemResource", "builtins.int", "builtins.int", "builtins.str", {".class": "UnionType", "items": ["builtins.str", "openai._types.NotGiven"], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "truncate of RealtimeConversationItemResource", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.resources.beta.realtime.realtime.RealtimeConversationItemResource.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "openai.resources.beta.realtime.realtime.RealtimeConversationItemResource", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "RealtimeConversationResource": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["openai.resources.beta.realtime.realtime.BaseRealtimeConnectionResource"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "openai.resources.beta.realtime.realtime.RealtimeConversationResource", "name": "RealtimeConversationResource", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "openai.resources.beta.realtime.realtime.RealtimeConversationResource", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "openai.resources.beta.realtime.realtime", "mro": ["openai.resources.beta.realtime.realtime.RealtimeConversationResource", "openai.resources.beta.realtime.realtime.BaseRealtimeConnectionResource", "builtins.object"], "names": {".class": "SymbolTable", "item": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai.resources.beta.realtime.realtime.RealtimeConversationResource.item", "name": "item", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.resources.beta.realtime.realtime.RealtimeConversationResource"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "item of RealtimeConversationResource", "ret_type": "openai.resources.beta.realtime.realtime.RealtimeConversationItemResource", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai.resources.beta.realtime.realtime.RealtimeConversationResource.item", "name": "item", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.resources.beta.realtime.realtime.RealtimeConversationResource"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "item of RealtimeConversationResource", "ret_type": "openai.resources.beta.realtime.realtime.RealtimeConversationItemResource", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.resources.beta.realtime.realtime.RealtimeConversationResource.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "openai.resources.beta.realtime.realtime.RealtimeConversationResource", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "RealtimeInputAudioBufferResource": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["openai.resources.beta.realtime.realtime.BaseRealtimeConnectionResource"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "openai.resources.beta.realtime.realtime.RealtimeInputAudioBufferResource", "name": "RealtimeInputAudioBufferResource", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "openai.resources.beta.realtime.realtime.RealtimeInputAudioBufferResource", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "openai.resources.beta.realtime.realtime", "mro": ["openai.resources.beta.realtime.realtime.RealtimeInputAudioBufferResource", "openai.resources.beta.realtime.realtime.BaseRealtimeConnectionResource", "builtins.object"], "names": {".class": "SymbolTable", "append": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 3, 5], "arg_names": ["self", "audio", "event_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "openai.resources.beta.realtime.realtime.RealtimeInputAudioBufferResource.append", "name": "append", "type": {".class": "CallableType", "arg_kinds": [0, 3, 5], "arg_names": ["self", "audio", "event_id"], "arg_types": ["openai.resources.beta.realtime.realtime.RealtimeInputAudioBufferResource", "builtins.str", {".class": "UnionType", "items": ["builtins.str", "openai._types.NotGiven"], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "append of RealtimeInputAudioBufferResource", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "clear": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5], "arg_names": ["self", "event_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "openai.resources.beta.realtime.realtime.RealtimeInputAudioBufferResource.clear", "name": "clear", "type": {".class": "CallableType", "arg_kinds": [0, 5], "arg_names": ["self", "event_id"], "arg_types": ["openai.resources.beta.realtime.realtime.RealtimeInputAudioBufferResource", {".class": "UnionType", "items": ["builtins.str", "openai._types.NotGiven"], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "clear of RealtimeInputAudioBufferResource", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "commit": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5], "arg_names": ["self", "event_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "openai.resources.beta.realtime.realtime.RealtimeInputAudioBufferResource.commit", "name": "commit", "type": {".class": "CallableType", "arg_kinds": [0, 5], "arg_names": ["self", "event_id"], "arg_types": ["openai.resources.beta.realtime.realtime.RealtimeInputAudioBufferResource", {".class": "UnionType", "items": ["builtins.str", "openai._types.NotGiven"], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "commit of RealtimeInputAudioBufferResource", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.resources.beta.realtime.realtime.RealtimeInputAudioBufferResource.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "openai.resources.beta.realtime.realtime.RealtimeInputAudioBufferResource", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "RealtimeOutputAudioBufferResource": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["openai.resources.beta.realtime.realtime.BaseRealtimeConnectionResource"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "openai.resources.beta.realtime.realtime.RealtimeOutputAudioBufferResource", "name": "RealtimeOutputAudioBufferResource", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "openai.resources.beta.realtime.realtime.RealtimeOutputAudioBufferResource", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "openai.resources.beta.realtime.realtime", "mro": ["openai.resources.beta.realtime.realtime.RealtimeOutputAudioBufferResource", "openai.resources.beta.realtime.realtime.BaseRealtimeConnectionResource", "builtins.object"], "names": {".class": "SymbolTable", "clear": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5], "arg_names": ["self", "event_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "openai.resources.beta.realtime.realtime.RealtimeOutputAudioBufferResource.clear", "name": "clear", "type": {".class": "CallableType", "arg_kinds": [0, 5], "arg_names": ["self", "event_id"], "arg_types": ["openai.resources.beta.realtime.realtime.RealtimeOutputAudioBufferResource", {".class": "UnionType", "items": ["builtins.str", "openai._types.NotGiven"], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "clear of RealtimeOutputAudioBufferResource", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.resources.beta.realtime.realtime.RealtimeOutputAudioBufferResource.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "openai.resources.beta.realtime.realtime.RealtimeOutputAudioBufferResource", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "RealtimeResponseResource": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["openai.resources.beta.realtime.realtime.BaseRealtimeConnectionResource"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "openai.resources.beta.realtime.realtime.RealtimeResponseResource", "name": "RealtimeResponseResource", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "openai.resources.beta.realtime.realtime.RealtimeResponseResource", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "openai.resources.beta.realtime.realtime", "mro": ["openai.resources.beta.realtime.realtime.RealtimeResponseResource", "openai.resources.beta.realtime.realtime.BaseRealtimeConnectionResource", "builtins.object"], "names": {".class": "SymbolTable", "cancel": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5], "arg_names": ["self", "event_id", "response_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "openai.resources.beta.realtime.realtime.RealtimeResponseResource.cancel", "name": "cancel", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5], "arg_names": ["self", "event_id", "response_id"], "arg_types": ["openai.resources.beta.realtime.realtime.RealtimeResponseResource", {".class": "UnionType", "items": ["builtins.str", "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", "openai._types.NotGiven"], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "cancel of RealtimeResponseResource", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "create": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5], "arg_names": ["self", "event_id", "response"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "openai.resources.beta.realtime.realtime.RealtimeResponseResource.create", "name": "create", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5], "arg_names": ["self", "event_id", "response"], "arg_types": ["openai.resources.beta.realtime.realtime.RealtimeResponseResource", {".class": "UnionType", "items": ["builtins.str", "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.realtime.response_create_event_param.Response"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create of RealtimeResponseResource", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.resources.beta.realtime.realtime.RealtimeResponseResource.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "openai.resources.beta.realtime.realtime.RealtimeResponseResource", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "RealtimeServerEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.realtime.realtime_server_event.RealtimeServerEvent", "kind": "Gdef", "module_public": false}, "RealtimeSessionResource": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["openai.resources.beta.realtime.realtime.BaseRealtimeConnectionResource"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "openai.resources.beta.realtime.realtime.RealtimeSessionResource", "name": "RealtimeSessionResource", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "openai.resources.beta.realtime.realtime.RealtimeSessionResource", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "openai.resources.beta.realtime.realtime", "mro": ["openai.resources.beta.realtime.realtime.RealtimeSessionResource", "openai.resources.beta.realtime.realtime.BaseRealtimeConnectionResource", "builtins.object"], "names": {".class": "SymbolTable", "update": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 3, 5], "arg_names": ["self", "session", "event_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "openai.resources.beta.realtime.realtime.RealtimeSessionResource.update", "name": "update", "type": {".class": "CallableType", "arg_kinds": [0, 3, 5], "arg_names": ["self", "session", "event_id"], "arg_types": ["openai.resources.beta.realtime.realtime.RealtimeSessionResource", {".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.realtime.session_update_event_param.Session"}, {".class": "UnionType", "items": ["builtins.str", "openai._types.NotGiven"], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "update of RealtimeSessionResource", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.resources.beta.realtime.realtime.RealtimeSessionResource.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "openai.resources.beta.realtime.realtime.RealtimeSessionResource", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "RealtimeTranscriptionSessionResource": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["openai.resources.beta.realtime.realtime.BaseRealtimeConnectionResource"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "openai.resources.beta.realtime.realtime.RealtimeTranscriptionSessionResource", "name": "RealtimeTranscriptionSessionResource", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "openai.resources.beta.realtime.realtime.RealtimeTranscriptionSessionResource", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "openai.resources.beta.realtime.realtime", "mro": ["openai.resources.beta.realtime.realtime.RealtimeTranscriptionSessionResource", "openai.resources.beta.realtime.realtime.BaseRealtimeConnectionResource", "builtins.object"], "names": {".class": "SymbolTable", "update": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 3, 5], "arg_names": ["self", "session", "event_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "openai.resources.beta.realtime.realtime.RealtimeTranscriptionSessionResource.update", "name": "update", "type": {".class": "CallableType", "arg_kinds": [0, 3, 5], "arg_names": ["self", "session", "event_id"], "arg_types": ["openai.resources.beta.realtime.realtime.RealtimeTranscriptionSessionResource", {".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.realtime.transcription_session_update_param.Session"}, {".class": "UnionType", "items": ["builtins.str", "openai._types.NotGiven"], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "update of RealtimeTranscriptionSessionResource", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.resources.beta.realtime.realtime.RealtimeTranscriptionSessionResource.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "openai.resources.beta.realtime.realtime.RealtimeTranscriptionSessionResource", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "RealtimeWithRawResponse": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "openai.resources.beta.realtime.realtime.RealtimeWithRawResponse", "name": "RealtimeWithRawResponse", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "openai.resources.beta.realtime.realtime.RealtimeWithRawResponse", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "openai.resources.beta.realtime.realtime", "mro": ["openai.resources.beta.realtime.realtime.RealtimeWithRawResponse", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "realtime"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "openai.resources.beta.realtime.realtime.RealtimeWithRawResponse.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "realtime"], "arg_types": ["openai.resources.beta.realtime.realtime.RealtimeWithRawResponse", "openai.resources.beta.realtime.realtime.Realtime"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of RealtimeWithRawResponse", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_realtime": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "openai.resources.beta.realtime.realtime.RealtimeWithRawResponse._realtime", "name": "_realtime", "setter_type": null, "type": "openai.resources.beta.realtime.realtime.Realtime"}}, "sessions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai.resources.beta.realtime.realtime.RealtimeWithRawResponse.sessions", "name": "sessions", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.resources.beta.realtime.realtime.RealtimeWithRawResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "sessions of RealtimeWithRawResponse", "ret_type": "openai.resources.beta.realtime.sessions.SessionsWithRawResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai.resources.beta.realtime.realtime.RealtimeWithRawResponse.sessions", "name": "sessions", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.resources.beta.realtime.realtime.RealtimeWithRawResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "sessions of RealtimeWithRawResponse", "ret_type": "openai.resources.beta.realtime.sessions.SessionsWithRawResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "transcription_sessions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai.resources.beta.realtime.realtime.RealtimeWithRawResponse.transcription_sessions", "name": "transcription_sessions", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.resources.beta.realtime.realtime.RealtimeWithRawResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "transcription_sessions of RealtimeWithRawResponse", "ret_type": "openai.resources.beta.realtime.transcription_sessions.TranscriptionSessionsWithRawResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai.resources.beta.realtime.realtime.RealtimeWithRawResponse.transcription_sessions", "name": "transcription_sessions", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.resources.beta.realtime.realtime.RealtimeWithRawResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "transcription_sessions of RealtimeWithRawResponse", "ret_type": "openai.resources.beta.realtime.transcription_sessions.TranscriptionSessionsWithRawResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.resources.beta.realtime.realtime.RealtimeWithRawResponse.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "openai.resources.beta.realtime.realtime.RealtimeWithRawResponse", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "RealtimeWithStreamingResponse": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "openai.resources.beta.realtime.realtime.RealtimeWithStreamingResponse", "name": "RealtimeWithStreamingResponse", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "openai.resources.beta.realtime.realtime.RealtimeWithStreamingResponse", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "openai.resources.beta.realtime.realtime", "mro": ["openai.resources.beta.realtime.realtime.RealtimeWithStreamingResponse", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "realtime"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "openai.resources.beta.realtime.realtime.RealtimeWithStreamingResponse.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "realtime"], "arg_types": ["openai.resources.beta.realtime.realtime.RealtimeWithStreamingResponse", "openai.resources.beta.realtime.realtime.Realtime"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of RealtimeWithStreamingResponse", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_realtime": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "openai.resources.beta.realtime.realtime.RealtimeWithStreamingResponse._realtime", "name": "_realtime", "setter_type": null, "type": "openai.resources.beta.realtime.realtime.Realtime"}}, "sessions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai.resources.beta.realtime.realtime.RealtimeWithStreamingResponse.sessions", "name": "sessions", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.resources.beta.realtime.realtime.RealtimeWithStreamingResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "sessions of RealtimeWithStreamingResponse", "ret_type": "openai.resources.beta.realtime.sessions.SessionsWithStreamingResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai.resources.beta.realtime.realtime.RealtimeWithStreamingResponse.sessions", "name": "sessions", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.resources.beta.realtime.realtime.RealtimeWithStreamingResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "sessions of RealtimeWithStreamingResponse", "ret_type": "openai.resources.beta.realtime.sessions.SessionsWithStreamingResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "transcription_sessions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai.resources.beta.realtime.realtime.RealtimeWithStreamingResponse.transcription_sessions", "name": "transcription_sessions", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.resources.beta.realtime.realtime.RealtimeWithStreamingResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "transcription_sessions of RealtimeWithStreamingResponse", "ret_type": "openai.resources.beta.realtime.transcription_sessions.TranscriptionSessionsWithStreamingResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai.resources.beta.realtime.realtime.RealtimeWithStreamingResponse.transcription_sessions", "name": "transcription_sessions", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.resources.beta.realtime.realtime.RealtimeWithStreamingResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "transcription_sessions of RealtimeWithStreamingResponse", "ret_type": "openai.resources.beta.realtime.transcription_sessions.TranscriptionSessionsWithStreamingResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.resources.beta.realtime.realtime.RealtimeWithStreamingResponse.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "openai.resources.beta.realtime.realtime.RealtimeWithStreamingResponse", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Sessions": {".class": "SymbolTableNode", "cross_ref": "openai.resources.beta.realtime.sessions.Sessions", "kind": "Gdef", "module_public": false}, "SessionsWithRawResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.beta.realtime.sessions.SessionsWithRawResponse", "kind": "Gdef", "module_public": false}, "SessionsWithStreamingResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.beta.realtime.sessions.SessionsWithStreamingResponse", "kind": "Gdef", "module_public": false}, "SyncAPIResource": {".class": "SymbolTableNode", "cross_ref": "openai._resource.SyncAPIResource", "kind": "Gdef", "module_public": false}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef", "module_public": false}, "TracebackType": {".class": "SymbolTableNode", "cross_ref": "types.TracebackType", "kind": "Gdef", "module_public": false}, "TranscriptionSessions": {".class": "SymbolTableNode", "cross_ref": "openai.resources.beta.realtime.transcription_sessions.TranscriptionSessions", "kind": "Gdef", "module_public": false}, "TranscriptionSessionsWithRawResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.beta.realtime.transcription_sessions.TranscriptionSessionsWithRawResponse", "kind": "Gdef", "module_public": false}, "TranscriptionSessionsWithStreamingResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.beta.realtime.transcription_sessions.TranscriptionSessionsWithStreamingResponse", "kind": "Gdef", "module_public": false}, "WebsocketConnection": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "openai.resources.beta.realtime.realtime.WebsocketConnection", "name": "WebsocketConnection", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "openai.resources.beta.realtime.realtime.WebsocketConnection", "source_any": null, "type_of_any": 3}}}, "WebsocketConnectionOptions": {".class": "SymbolTableNode", "cross_ref": "openai.types.websocket_connection_options.WebsocketConnectionOptions", "kind": "Gdef", "module_public": false}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "openai.resources.beta.realtime.realtime.__all__", "name": "__all__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.resources.beta.realtime.realtime.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.resources.beta.realtime.realtime.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.resources.beta.realtime.realtime.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.resources.beta.realtime.realtime.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.resources.beta.realtime.realtime.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.resources.beta.realtime.realtime.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "_merge_mappings": {".class": "SymbolTableNode", "cross_ref": "openai._base_client._merge_mappings", "kind": "Gdef", "module_public": false}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef", "module_public": false}, "async_maybe_transform": {".class": "SymbolTableNode", "cross_ref": "openai._utils._transform.async_maybe_transform", "kind": "Gdef", "module_public": false}, "cached_property": {".class": "SymbolTableNode", "cross_ref": "openai._compat.cached_property", "kind": "Gdef", "module_public": false}, "cast": {".class": "SymbolTableNode", "cross_ref": "typing.cast", "kind": "Gdef", "module_public": false}, "construct_type_unchecked": {".class": "SymbolTableNode", "cross_ref": "openai._models.construct_type_unchecked", "kind": "Gdef", "module_public": false}, "httpx": {".class": "SymbolTableNode", "cross_ref": "httpx", "kind": "Gdef", "module_public": false}, "is_async_azure_client": {".class": "SymbolTableNode", "cross_ref": "openai._utils._utils.is_async_azure_client", "kind": "Gdef", "module_public": false}, "is_azure_client": {".class": "SymbolTableNode", "cross_ref": "openai._utils._utils.is_azure_client", "kind": "Gdef", "module_public": false}, "json": {".class": "SymbolTableNode", "cross_ref": "json", "kind": "Gdef", "module_public": false}, "log": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "openai.resources.beta.realtime.realtime.log", "name": "log", "setter_type": null, "type": "logging.Logger"}}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef", "module_public": false}, "maybe_transform": {".class": "SymbolTableNode", "cross_ref": "openai._utils._transform.maybe_transform", "kind": "Gdef", "module_public": false}, "response_create_event_param": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.realtime.response_create_event_param", "kind": "Gdef", "module_public": false}, "session_update_event_param": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.realtime.session_update_event_param", "kind": "Gdef", "module_public": false}, "strip_not_given": {".class": "SymbolTableNode", "cross_ref": "openai._utils._utils.strip_not_given", "kind": "Gdef", "module_public": false}, "transcription_session_update_param": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.realtime.transcription_session_update_param", "kind": "Gdef", "module_public": false}}, "path": "/home/<USER>/.pyenv/versions/3.12.5/lib/python3.12/site-packages/openai/resources/beta/realtime/realtime.py"}