{"data_mtime": 1750696463, "dep_lines": [13, 54, 42, 42, 42, 42, 48, 42, 49, 50, 51, 52, 53, 55, 56, 34, 12, 21, 22, 28, 29, 30, 31, 32, 33, 3, 5, 6, 7, 10, 12, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 10, 10, 10, 10, 5, 20, 5, 5, 5, 5, 5, 5, 5, 5, 10, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 10, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["openai.resources.beta.threads.runs.steps", "openai.types.beta.threads.runs.run_step_include", "openai.types.beta.threads.run_list_params", "openai.types.beta.threads.run_create_params", "openai.types.beta.threads.run_update_params", "openai.types.beta.threads.run_submit_tool_outputs_params", "openai.types.beta.threads.run", "openai.types.beta.threads", "openai.types.shared.chat_model", "openai.types.shared_params.metadata", "openai.types.shared.reasoning_effort", "openai.types.beta.assistant_tool_param", "openai.types.beta.assistant_stream_event", "openai.types.beta.assistant_tool_choice_option_param", "openai.types.beta.assistant_response_format_option_param", "openai.lib.streaming", "openai._legacy_response", "openai._types", "openai._utils", "openai._compat", "openai._resource", "openai._response", "openai._streaming", "openai.pagination", "openai._base_client", "__future__", "typing_extensions", "typing", "functools", "httpx", "openai", "builtins", "_frozen_importlib", "abc", "httpx._config", "openai._models", "openai._utils._utils", "openai.lib", "openai.lib.streaming._assistants", "openai.types", "openai.types.beta", "openai.types.beta.assistant_tool_choice_function_param", "openai.types.beta.assistant_tool_choice_param", "openai.types.beta.code_interpreter_tool_param", "openai.types.beta.file_search_tool_param", "openai.types.beta.function_tool_param", "openai.types.beta.threads.image_file_content_block_param", "openai.types.beta.threads.image_file_param", "openai.types.beta.threads.image_url_content_block_param", "openai.types.beta.threads.image_url_param", "openai.types.beta.threads.text_content_block_param", "openai.types.shared_params", "openai.types.shared_params.function_definition", "openai.types.shared_params.response_format_json_object", "openai.types.shared_params.response_format_json_schema", "openai.types.shared_params.response_format_text", "pydantic", "pydantic._internal", "pydantic._internal._model_construction", "pydantic.main", "types"], "hash": "10eff4aa7d65b85abd9e4f1e3d7aa147640c08f8", "id": "openai.resources.beta.threads.runs.runs", "ignore_all": true, "interface_hash": "2b6327d88f4307cdf9586dce55e3e88bc1c0377c", "mtime": 1747857566, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "/home/<USER>/.pyenv/versions/3.12.5/lib/python3.12/site-packages/openai/resources/beta/threads/runs/runs.py", "plugin_data": null, "size": 147978, "suppressed": [], "version_id": "1.16.1"}