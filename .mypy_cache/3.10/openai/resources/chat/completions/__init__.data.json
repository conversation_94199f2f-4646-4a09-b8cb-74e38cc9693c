{".class": "MypyFile", "_fullname": "openai.resources.chat.completions", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AsyncCompletions": {".class": "SymbolTableNode", "cross_ref": "openai.resources.chat.completions.completions.AsyncCompletions", "kind": "Gdef"}, "AsyncCompletionsWithRawResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.chat.completions.completions.AsyncCompletionsWithRawResponse", "kind": "Gdef"}, "AsyncCompletionsWithStreamingResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.chat.completions.completions.AsyncCompletionsWithStreamingResponse", "kind": "Gdef"}, "AsyncMessages": {".class": "SymbolTableNode", "cross_ref": "openai.resources.chat.completions.messages.AsyncMessages", "kind": "Gdef"}, "AsyncMessagesWithRawResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.chat.completions.messages.AsyncMessagesWithRawResponse", "kind": "Gdef"}, "AsyncMessagesWithStreamingResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.chat.completions.messages.AsyncMessagesWithStreamingResponse", "kind": "Gdef"}, "Completions": {".class": "SymbolTableNode", "cross_ref": "openai.resources.chat.completions.completions.Completions", "kind": "Gdef"}, "CompletionsWithRawResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.chat.completions.completions.CompletionsWithRawResponse", "kind": "Gdef"}, "CompletionsWithStreamingResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.chat.completions.completions.CompletionsWithStreamingResponse", "kind": "Gdef"}, "Messages": {".class": "SymbolTableNode", "cross_ref": "openai.resources.chat.completions.messages.Messages", "kind": "Gdef"}, "MessagesWithRawResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.chat.completions.messages.MessagesWithRawResponse", "kind": "Gdef"}, "MessagesWithStreamingResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.chat.completions.messages.MessagesWithStreamingResponse", "kind": "Gdef"}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "openai.resources.chat.completions.__all__", "name": "__all__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.resources.chat.completions.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.resources.chat.completions.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.resources.chat.completions.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.resources.chat.completions.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.resources.chat.completions.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.resources.chat.completions.__path__", "name": "__path__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.resources.chat.completions.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}}, "path": "/home/<USER>/.pyenv/versions/3.12.5/lib/python3.12/site-packages/openai/resources/chat/completions/__init__.py"}