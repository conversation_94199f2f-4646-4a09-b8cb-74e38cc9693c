{"data_mtime": 1750696463, "dep_lines": [40, 17, 28, 28, 29, 34, 35, 36, 37, 38, 39, 41, 42, 43, 44, 45, 26, 28, 11, 12, 13, 14, 15, 16, 25, 27, 3, 5, 6, 7, 9, 11, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 10, 10, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 20, 10, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 10, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["openai.lib.streaming.responses._responses", "openai.resources.responses.input_items", "openai.types.responses.response_create_params", "openai.types.responses.response_retrieve_params", "openai.lib._parsing._responses", "openai.types.shared.chat_model", "openai.types.responses.response", "openai.types.responses.tool_param", "openai.types.shared_params.metadata", "openai.types.shared_params.reasoning", "openai.types.responses.parsed_response", "openai.types.responses.response_includable", "openai.types.shared_params.responses_model", "openai.types.responses.response_input_param", "openai.types.responses.response_stream_event", "openai.types.responses.response_text_config_param", "openai.lib._tools", "openai.types.responses", "openai._legacy_response", "openai._types", "openai._utils", "openai._compat", "openai._resource", "openai._response", "openai._streaming", "openai._base_client", "__future__", "typing", "functools", "typing_extensions", "httpx", "openai", "builtins", "_frozen_importlib", "abc", "httpx._config", "openai._models", "openai._utils._utils", "openai.lib", "openai.lib.streaming", "openai.lib.streaming.responses", "openai.types", "openai.types.chat", "openai.types.chat.chat_completion_tool_param", "openai.types.responses.computer_tool_param", "openai.types.responses.easy_input_message_param", "openai.types.responses.file_search_tool_param", "openai.types.responses.function_tool_param", "openai.types.responses.response_audio_delta_event", "openai.types.responses.response_audio_done_event", "openai.types.responses.response_audio_transcript_delta_event", "openai.types.responses.response_audio_transcript_done_event", "openai.types.responses.response_code_interpreter_call_code_delta_event", "openai.types.responses.response_code_interpreter_call_code_done_event", "openai.types.responses.response_code_interpreter_call_completed_event", "openai.types.responses.response_code_interpreter_call_in_progress_event", "openai.types.responses.response_code_interpreter_call_interpreting_event", "openai.types.responses.response_code_interpreter_tool_call_param", "openai.types.responses.response_completed_event", "openai.types.responses.response_computer_tool_call_output_screenshot_param", "openai.types.responses.response_computer_tool_call_param", "openai.types.responses.response_content_part_added_event", "openai.types.responses.response_content_part_done_event", "openai.types.responses.response_created_event", "openai.types.responses.response_error_event", "openai.types.responses.response_failed_event", "openai.types.responses.response_file_search_call_completed_event", "openai.types.responses.response_file_search_call_in_progress_event", "openai.types.responses.response_file_search_call_searching_event", "openai.types.responses.response_file_search_tool_call_param", "openai.types.responses.response_format_text_json_schema_config_param", "openai.types.responses.response_function_call_arguments_delta_event", "openai.types.responses.response_function_call_arguments_done_event", "openai.types.responses.response_function_tool_call_param", "openai.types.responses.response_function_web_search_param", "openai.types.responses.response_image_gen_call_completed_event", "openai.types.responses.response_image_gen_call_generating_event", "openai.types.responses.response_image_gen_call_in_progress_event", "openai.types.responses.response_image_gen_call_partial_image_event", "openai.types.responses.response_in_progress_event", "openai.types.responses.response_incomplete_event", "openai.types.responses.response_input_file_param", "openai.types.responses.response_input_image_param", "openai.types.responses.response_input_text_param", "openai.types.responses.response_mcp_call_arguments_delta_event", "openai.types.responses.response_mcp_call_arguments_done_event", "openai.types.responses.response_mcp_call_completed_event", "openai.types.responses.response_mcp_call_failed_event", "openai.types.responses.response_mcp_call_in_progress_event", "openai.types.responses.response_mcp_list_tools_completed_event", "openai.types.responses.response_mcp_list_tools_failed_event", "openai.types.responses.response_mcp_list_tools_in_progress_event", "openai.types.responses.response_output_item_added_event", "openai.types.responses.response_output_item_done_event", "openai.types.responses.response_output_message_param", "openai.types.responses.response_output_refusal_param", "openai.types.responses.response_output_text_annotation_added_event", "openai.types.responses.response_output_text_param", "openai.types.responses.response_queued_event", "openai.types.responses.response_reasoning_delta_event", "openai.types.responses.response_reasoning_done_event", "openai.types.responses.response_reasoning_item_param", "openai.types.responses.response_reasoning_summary_delta_event", "openai.types.responses.response_reasoning_summary_done_event", "openai.types.responses.response_reasoning_summary_part_added_event", "openai.types.responses.response_reasoning_summary_part_done_event", "openai.types.responses.response_reasoning_summary_text_delta_event", "openai.types.responses.response_reasoning_summary_text_done_event", "openai.types.responses.response_refusal_delta_event", "openai.types.responses.response_refusal_done_event", "openai.types.responses.response_text_annotation_delta_event", "openai.types.responses.response_text_delta_event", "openai.types.responses.response_text_done_event", "openai.types.responses.response_web_search_call_completed_event", "openai.types.responses.response_web_search_call_in_progress_event", "openai.types.responses.response_web_search_call_searching_event", "openai.types.responses.tool_choice_function_param", "openai.types.responses.tool_choice_types_param", "openai.types.responses.web_search_tool_param", "openai.types.shared_params", "openai.types.shared_params.comparison_filter", "openai.types.shared_params.compound_filter", "openai.types.shared_params.function_definition", "openai.types.shared_params.response_format_json_object", "openai.types.shared_params.response_format_text", "pydantic", "pydantic._internal", "pydantic._internal._model_construction", "pydantic.main", "types"], "hash": "d5c433f12195c0110dd9d143327be3c14e9506f0", "id": "openai.resources.responses.responses", "ignore_all": true, "interface_hash": "4abf517233ccbba7dbd9f5868174fdf1277db592", "mtime": 1747857566, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "/home/<USER>/.pyenv/versions/3.12.5/lib/python3.12/site-packages/openai/resources/responses/responses.py", "plugin_data": null, "size": 102586, "suppressed": [], "version_id": "1.16.1"}