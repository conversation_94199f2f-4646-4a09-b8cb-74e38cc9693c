{".class": "MypyFile", "_fullname": "openai.resources.images", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AsyncAPIResource": {".class": "SymbolTableNode", "cross_ref": "openai._resource.AsyncAPIResource", "kind": "Gdef", "module_public": false}, "AsyncImages": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["openai._resource.AsyncAPIResource"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "openai.resources.images.AsyncImages", "name": "AsyncImages", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "openai.resources.images.AsyncImages", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "openai.resources.images", "mro": ["openai.resources.images.AsyncImages", "openai._resource.AsyncAPIResource", "builtins.object"], "names": {".class": "SymbolTable", "create_variation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 3, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "image", "model", "n", "response_format", "size", "user", "extra_headers", "extra_query", "extra_body", "timeout"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "openai.resources.images.AsyncImages.create_variation", "name": "create_variation", "type": {".class": "CallableType", "arg_kinds": [0, 3, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "image", "model", "n", "response_format", "size", "user", "extra_headers", "extra_query", "extra_body", "timeout"], "arg_types": ["openai.resources.images.AsyncImages", {".class": "TypeAliasType", "args": [], "type_ref": "openai._types.FileTypes"}, {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "openai.types.image_model.ImageModel"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "url"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "b64_json"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "256x256"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "512x512"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "1024x1024"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Headers"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Query"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "httpx._config.Timeout", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create_variation of AsyncImages", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "openai.types.images_response.ImagesResponse"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "edit": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 3, 3, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "image", "prompt", "background", "mask", "model", "n", "quality", "response_format", "size", "user", "extra_headers", "extra_query", "extra_body", "timeout"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "openai.resources.images.AsyncImages.edit", "name": "edit", "type": {".class": "CallableType", "arg_kinds": [0, 3, 3, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "image", "prompt", "background", "mask", "model", "n", "quality", "response_format", "size", "user", "extra_headers", "extra_query", "extra_body", "timeout"], "arg_types": ["openai.resources.images.AsyncImages", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.FileTypes"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.FileTypes"}], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": false}, "builtins.str", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "transparent"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "opaque"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "auto"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.FileTypes"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "openai.types.image_model.ImageModel"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "standard"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "low"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "medium"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "high"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "auto"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "url"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "b64_json"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "256x256"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "512x512"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "1024x1024"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "1536x1024"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "1024x1536"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "auto"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Headers"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Query"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "httpx._config.Timeout", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "edit of AsyncImages", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "openai.types.images_response.ImagesResponse"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "generate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 3, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "prompt", "background", "model", "moderation", "n", "output_compression", "output_format", "quality", "response_format", "size", "style", "user", "extra_headers", "extra_query", "extra_body", "timeout"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "openai.resources.images.AsyncImages.generate", "name": "generate", "type": {".class": "CallableType", "arg_kinds": [0, 3, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "prompt", "background", "model", "moderation", "n", "output_compression", "output_format", "quality", "response_format", "size", "style", "user", "extra_headers", "extra_query", "extra_body", "timeout"], "arg_types": ["openai.resources.images.AsyncImages", "builtins.str", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "transparent"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "opaque"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "auto"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "openai.types.image_model.ImageModel"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "low"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "auto"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "png"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "jpeg"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "webp"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "standard"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "hd"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "low"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "medium"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "high"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "auto"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "url"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "b64_json"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "auto"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "1024x1024"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "1536x1024"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "1024x1536"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "256x256"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "512x512"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "1792x1024"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "1024x1792"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "vivid"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "natural"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Headers"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Query"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "httpx._config.Timeout", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "generate of AsyncImages", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "openai.types.images_response.ImagesResponse"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "with_raw_response": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai.resources.images.AsyncImages.with_raw_response", "name": "with_raw_response", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.resources.images.AsyncImages"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "with_raw_response of AsyncImages", "ret_type": "openai.resources.images.AsyncImagesWithRawResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai.resources.images.AsyncImages.with_raw_response", "name": "with_raw_response", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.resources.images.AsyncImages"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "with_raw_response of AsyncImages", "ret_type": "openai.resources.images.AsyncImagesWithRawResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "with_streaming_response": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai.resources.images.AsyncImages.with_streaming_response", "name": "with_streaming_response", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.resources.images.AsyncImages"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "with_streaming_response of AsyncImages", "ret_type": "openai.resources.images.AsyncImagesWithStreamingResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai.resources.images.AsyncImages.with_streaming_response", "name": "with_streaming_response", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.resources.images.AsyncImages"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "with_streaming_response of AsyncImages", "ret_type": "openai.resources.images.AsyncImagesWithStreamingResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.resources.images.AsyncImages.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "openai.resources.images.AsyncImages", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "AsyncImagesWithRawResponse": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "openai.resources.images.AsyncImagesWithRawResponse", "name": "AsyncImagesWithRawResponse", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "openai.resources.images.AsyncImagesWithRawResponse", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "openai.resources.images", "mro": ["openai.resources.images.AsyncImagesWithRawResponse", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "images"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "openai.resources.images.AsyncImagesWithRawResponse.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "images"], "arg_types": ["openai.resources.images.AsyncImagesWithRawResponse", "openai.resources.images.AsyncImages"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of AsyncImagesWithRawResponse", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_images": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "openai.resources.images.AsyncImagesWithRawResponse._images", "name": "_images", "setter_type": null, "type": "openai.resources.images.AsyncImages"}}, "create_variation": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "openai.resources.images.AsyncImagesWithRawResponse.create_variation", "name": "create_variation", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [3, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["image", "model", "n", "response_format", "size", "user", "extra_headers", "extra_query", "extra_body", "timeout"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.FileTypes"}, {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "openai.types.image_model.ImageModel"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "url"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "b64_json"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "256x256"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "512x512"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "1024x1024"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Headers"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Query"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "httpx._config.Timeout", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["openai.types.images_response.ImagesResponse"], "extra_attrs": null, "type_ref": "openai._legacy_response.LegacyAPIResponse"}], "extra_attrs": null, "type_ref": "typing.Awaitable"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "edit": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "openai.resources.images.AsyncImagesWithRawResponse.edit", "name": "edit", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [3, 3, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["image", "prompt", "background", "mask", "model", "n", "quality", "response_format", "size", "user", "extra_headers", "extra_query", "extra_body", "timeout"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.FileTypes"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.FileTypes"}], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": false}, "builtins.str", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "transparent"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "opaque"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "auto"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.FileTypes"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "openai.types.image_model.ImageModel"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "standard"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "low"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "medium"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "high"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "auto"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "url"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "b64_json"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "256x256"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "512x512"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "1024x1024"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "1536x1024"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "1024x1536"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "auto"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Headers"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Query"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "httpx._config.Timeout", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["openai.types.images_response.ImagesResponse"], "extra_attrs": null, "type_ref": "openai._legacy_response.LegacyAPIResponse"}], "extra_attrs": null, "type_ref": "typing.Awaitable"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "generate": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "openai.resources.images.AsyncImagesWithRawResponse.generate", "name": "generate", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [3, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["prompt", "background", "model", "moderation", "n", "output_compression", "output_format", "quality", "response_format", "size", "style", "user", "extra_headers", "extra_query", "extra_body", "timeout"], "arg_types": ["builtins.str", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "transparent"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "opaque"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "auto"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "openai.types.image_model.ImageModel"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "low"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "auto"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "png"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "jpeg"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "webp"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "standard"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "hd"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "low"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "medium"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "high"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "auto"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "url"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "b64_json"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "auto"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "1024x1024"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "1536x1024"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "1024x1536"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "256x256"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "512x512"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "1792x1024"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "1024x1792"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "vivid"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "natural"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Headers"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Query"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "httpx._config.Timeout", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["openai.types.images_response.ImagesResponse"], "extra_attrs": null, "type_ref": "openai._legacy_response.LegacyAPIResponse"}], "extra_attrs": null, "type_ref": "typing.Awaitable"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.resources.images.AsyncImagesWithRawResponse.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "openai.resources.images.AsyncImagesWithRawResponse", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "AsyncImagesWithStreamingResponse": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "openai.resources.images.AsyncImagesWithStreamingResponse", "name": "AsyncImagesWithStreamingResponse", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "openai.resources.images.AsyncImagesWithStreamingResponse", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "openai.resources.images", "mro": ["openai.resources.images.AsyncImagesWithStreamingResponse", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "images"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "openai.resources.images.AsyncImagesWithStreamingResponse.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "images"], "arg_types": ["openai.resources.images.AsyncImagesWithStreamingResponse", "openai.resources.images.AsyncImages"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of AsyncImagesWithStreamingResponse", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_images": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "openai.resources.images.AsyncImagesWithStreamingResponse._images", "name": "_images", "setter_type": null, "type": "openai.resources.images.AsyncImages"}}, "create_variation": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "openai.resources.images.AsyncImagesWithStreamingResponse.create_variation", "name": "create_variation", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [3, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["image", "model", "n", "response_format", "size", "user", "extra_headers", "extra_query", "extra_body", "timeout"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.FileTypes"}, {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "openai.types.image_model.ImageModel"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "url"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "b64_json"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "256x256"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "512x512"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "1024x1024"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Headers"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Query"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "httpx._config.Timeout", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["openai.types.images_response.ImagesResponse"], "extra_attrs": null, "type_ref": "openai._response.AsyncAPIResponse"}], "extra_attrs": null, "type_ref": "openai._response.AsyncResponseContextManager"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "edit": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "openai.resources.images.AsyncImagesWithStreamingResponse.edit", "name": "edit", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [3, 3, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["image", "prompt", "background", "mask", "model", "n", "quality", "response_format", "size", "user", "extra_headers", "extra_query", "extra_body", "timeout"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.FileTypes"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.FileTypes"}], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": false}, "builtins.str", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "transparent"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "opaque"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "auto"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.FileTypes"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "openai.types.image_model.ImageModel"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "standard"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "low"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "medium"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "high"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "auto"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "url"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "b64_json"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "256x256"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "512x512"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "1024x1024"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "1536x1024"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "1024x1536"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "auto"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Headers"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Query"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "httpx._config.Timeout", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["openai.types.images_response.ImagesResponse"], "extra_attrs": null, "type_ref": "openai._response.AsyncAPIResponse"}], "extra_attrs": null, "type_ref": "openai._response.AsyncResponseContextManager"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "generate": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "openai.resources.images.AsyncImagesWithStreamingResponse.generate", "name": "generate", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [3, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["prompt", "background", "model", "moderation", "n", "output_compression", "output_format", "quality", "response_format", "size", "style", "user", "extra_headers", "extra_query", "extra_body", "timeout"], "arg_types": ["builtins.str", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "transparent"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "opaque"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "auto"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "openai.types.image_model.ImageModel"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "low"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "auto"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "png"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "jpeg"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "webp"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "standard"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "hd"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "low"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "medium"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "high"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "auto"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "url"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "b64_json"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "auto"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "1024x1024"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "1536x1024"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "1024x1536"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "256x256"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "512x512"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "1792x1024"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "1024x1792"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "vivid"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "natural"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Headers"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Query"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "httpx._config.Timeout", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["openai.types.images_response.ImagesResponse"], "extra_attrs": null, "type_ref": "openai._response.AsyncAPIResponse"}], "extra_attrs": null, "type_ref": "openai._response.AsyncResponseContextManager"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.resources.images.AsyncImagesWithStreamingResponse.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "openai.resources.images.AsyncImagesWithStreamingResponse", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Body": {".class": "SymbolTableNode", "cross_ref": "openai._types.Body", "kind": "Gdef", "module_public": false}, "FileTypes": {".class": "SymbolTableNode", "cross_ref": "openai._types.FileTypes", "kind": "Gdef", "module_public": false}, "Headers": {".class": "SymbolTableNode", "cross_ref": "openai._types.Headers", "kind": "Gdef", "module_public": false}, "ImageModel": {".class": "SymbolTableNode", "cross_ref": "openai.types.image_model.ImageModel", "kind": "Gdef", "module_public": false}, "Images": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["openai._resource.SyncAPIResource"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "openai.resources.images.Images", "name": "Images", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "openai.resources.images.Images", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "openai.resources.images", "mro": ["openai.resources.images.Images", "openai._resource.SyncAPIResource", "builtins.object"], "names": {".class": "SymbolTable", "create_variation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 3, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "image", "model", "n", "response_format", "size", "user", "extra_headers", "extra_query", "extra_body", "timeout"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "openai.resources.images.Images.create_variation", "name": "create_variation", "type": {".class": "CallableType", "arg_kinds": [0, 3, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "image", "model", "n", "response_format", "size", "user", "extra_headers", "extra_query", "extra_body", "timeout"], "arg_types": ["openai.resources.images.Images", {".class": "TypeAliasType", "args": [], "type_ref": "openai._types.FileTypes"}, {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "openai.types.image_model.ImageModel"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "url"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "b64_json"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "256x256"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "512x512"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "1024x1024"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Headers"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Query"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "httpx._config.Timeout", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create_variation of Images", "ret_type": "openai.types.images_response.ImagesResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "edit": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 3, 3, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "image", "prompt", "background", "mask", "model", "n", "quality", "response_format", "size", "user", "extra_headers", "extra_query", "extra_body", "timeout"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "openai.resources.images.Images.edit", "name": "edit", "type": {".class": "CallableType", "arg_kinds": [0, 3, 3, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "image", "prompt", "background", "mask", "model", "n", "quality", "response_format", "size", "user", "extra_headers", "extra_query", "extra_body", "timeout"], "arg_types": ["openai.resources.images.Images", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.FileTypes"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.FileTypes"}], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": false}, "builtins.str", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "transparent"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "opaque"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "auto"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.FileTypes"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "openai.types.image_model.ImageModel"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "standard"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "low"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "medium"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "high"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "auto"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "url"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "b64_json"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "256x256"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "512x512"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "1024x1024"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "1536x1024"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "1024x1536"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "auto"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Headers"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Query"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "httpx._config.Timeout", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "edit of Images", "ret_type": "openai.types.images_response.ImagesResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "generate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 3, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "prompt", "background", "model", "moderation", "n", "output_compression", "output_format", "quality", "response_format", "size", "style", "user", "extra_headers", "extra_query", "extra_body", "timeout"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "openai.resources.images.Images.generate", "name": "generate", "type": {".class": "CallableType", "arg_kinds": [0, 3, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "prompt", "background", "model", "moderation", "n", "output_compression", "output_format", "quality", "response_format", "size", "style", "user", "extra_headers", "extra_query", "extra_body", "timeout"], "arg_types": ["openai.resources.images.Images", "builtins.str", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "transparent"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "opaque"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "auto"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "openai.types.image_model.ImageModel"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "low"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "auto"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "png"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "jpeg"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "webp"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "standard"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "hd"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "low"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "medium"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "high"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "auto"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "url"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "b64_json"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "auto"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "1024x1024"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "1536x1024"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "1024x1536"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "256x256"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "512x512"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "1792x1024"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "1024x1792"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "vivid"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "natural"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Headers"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Query"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "httpx._config.Timeout", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "generate of Images", "ret_type": "openai.types.images_response.ImagesResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "with_raw_response": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai.resources.images.Images.with_raw_response", "name": "with_raw_response", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.resources.images.Images"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "with_raw_response of Images", "ret_type": "openai.resources.images.ImagesWithRawResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai.resources.images.Images.with_raw_response", "name": "with_raw_response", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.resources.images.Images"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "with_raw_response of Images", "ret_type": "openai.resources.images.ImagesWithRawResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "with_streaming_response": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai.resources.images.Images.with_streaming_response", "name": "with_streaming_response", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.resources.images.Images"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "with_streaming_response of Images", "ret_type": "openai.resources.images.ImagesWithStreamingResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai.resources.images.Images.with_streaming_response", "name": "with_streaming_response", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.resources.images.Images"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "with_streaming_response of Images", "ret_type": "openai.resources.images.ImagesWithStreamingResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.resources.images.Images.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "openai.resources.images.Images", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ImagesResponse": {".class": "SymbolTableNode", "cross_ref": "openai.types.images_response.ImagesResponse", "kind": "Gdef", "module_public": false}, "ImagesWithRawResponse": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "openai.resources.images.ImagesWithRawResponse", "name": "ImagesWithRawResponse", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "openai.resources.images.ImagesWithRawResponse", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "openai.resources.images", "mro": ["openai.resources.images.ImagesWithRawResponse", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "images"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "openai.resources.images.ImagesWithRawResponse.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "images"], "arg_types": ["openai.resources.images.ImagesWithRawResponse", "openai.resources.images.Images"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of ImagesWithRawResponse", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_images": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "openai.resources.images.ImagesWithRawResponse._images", "name": "_images", "setter_type": null, "type": "openai.resources.images.Images"}}, "create_variation": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "openai.resources.images.ImagesWithRawResponse.create_variation", "name": "create_variation", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [3, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["image", "model", "n", "response_format", "size", "user", "extra_headers", "extra_query", "extra_body", "timeout"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.FileTypes"}, {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "openai.types.image_model.ImageModel"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "url"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "b64_json"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "256x256"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "512x512"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "1024x1024"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Headers"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Query"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "httpx._config.Timeout", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": ["openai.types.images_response.ImagesResponse"], "extra_attrs": null, "type_ref": "openai._legacy_response.LegacyAPIResponse"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "edit": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "openai.resources.images.ImagesWithRawResponse.edit", "name": "edit", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [3, 3, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["image", "prompt", "background", "mask", "model", "n", "quality", "response_format", "size", "user", "extra_headers", "extra_query", "extra_body", "timeout"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.FileTypes"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.FileTypes"}], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": false}, "builtins.str", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "transparent"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "opaque"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "auto"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.FileTypes"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "openai.types.image_model.ImageModel"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "standard"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "low"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "medium"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "high"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "auto"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "url"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "b64_json"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "256x256"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "512x512"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "1024x1024"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "1536x1024"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "1024x1536"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "auto"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Headers"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Query"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "httpx._config.Timeout", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": ["openai.types.images_response.ImagesResponse"], "extra_attrs": null, "type_ref": "openai._legacy_response.LegacyAPIResponse"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "generate": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "openai.resources.images.ImagesWithRawResponse.generate", "name": "generate", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [3, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["prompt", "background", "model", "moderation", "n", "output_compression", "output_format", "quality", "response_format", "size", "style", "user", "extra_headers", "extra_query", "extra_body", "timeout"], "arg_types": ["builtins.str", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "transparent"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "opaque"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "auto"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "openai.types.image_model.ImageModel"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "low"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "auto"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "png"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "jpeg"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "webp"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "standard"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "hd"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "low"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "medium"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "high"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "auto"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "url"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "b64_json"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "auto"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "1024x1024"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "1536x1024"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "1024x1536"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "256x256"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "512x512"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "1792x1024"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "1024x1792"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "vivid"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "natural"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Headers"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Query"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "httpx._config.Timeout", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": ["openai.types.images_response.ImagesResponse"], "extra_attrs": null, "type_ref": "openai._legacy_response.LegacyAPIResponse"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.resources.images.ImagesWithRawResponse.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "openai.resources.images.ImagesWithRawResponse", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ImagesWithStreamingResponse": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "openai.resources.images.ImagesWithStreamingResponse", "name": "ImagesWithStreamingResponse", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "openai.resources.images.ImagesWithStreamingResponse", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "openai.resources.images", "mro": ["openai.resources.images.ImagesWithStreamingResponse", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "images"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "openai.resources.images.ImagesWithStreamingResponse.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "images"], "arg_types": ["openai.resources.images.ImagesWithStreamingResponse", "openai.resources.images.Images"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of ImagesWithStreamingResponse", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_images": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "openai.resources.images.ImagesWithStreamingResponse._images", "name": "_images", "setter_type": null, "type": "openai.resources.images.Images"}}, "create_variation": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "openai.resources.images.ImagesWithStreamingResponse.create_variation", "name": "create_variation", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [3, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["image", "model", "n", "response_format", "size", "user", "extra_headers", "extra_query", "extra_body", "timeout"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.FileTypes"}, {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "openai.types.image_model.ImageModel"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "url"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "b64_json"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "256x256"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "512x512"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "1024x1024"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Headers"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Query"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "httpx._config.Timeout", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["openai.types.images_response.ImagesResponse"], "extra_attrs": null, "type_ref": "openai._response.APIResponse"}], "extra_attrs": null, "type_ref": "openai._response.ResponseContextManager"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "edit": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "openai.resources.images.ImagesWithStreamingResponse.edit", "name": "edit", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [3, 3, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["image", "prompt", "background", "mask", "model", "n", "quality", "response_format", "size", "user", "extra_headers", "extra_query", "extra_body", "timeout"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.FileTypes"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.FileTypes"}], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": false}, "builtins.str", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "transparent"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "opaque"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "auto"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.FileTypes"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "openai.types.image_model.ImageModel"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "standard"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "low"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "medium"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "high"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "auto"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "url"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "b64_json"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "256x256"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "512x512"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "1024x1024"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "1536x1024"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "1024x1536"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "auto"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Headers"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Query"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "httpx._config.Timeout", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["openai.types.images_response.ImagesResponse"], "extra_attrs": null, "type_ref": "openai._response.APIResponse"}], "extra_attrs": null, "type_ref": "openai._response.ResponseContextManager"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "generate": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "openai.resources.images.ImagesWithStreamingResponse.generate", "name": "generate", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [3, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["prompt", "background", "model", "moderation", "n", "output_compression", "output_format", "quality", "response_format", "size", "style", "user", "extra_headers", "extra_query", "extra_body", "timeout"], "arg_types": ["builtins.str", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "transparent"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "opaque"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "auto"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "openai.types.image_model.ImageModel"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "low"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "auto"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "png"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "jpeg"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "webp"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "standard"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "hd"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "low"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "medium"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "high"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "auto"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "url"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "b64_json"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "auto"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "1024x1024"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "1536x1024"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "1024x1536"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "256x256"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "512x512"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "1792x1024"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "1024x1792"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "vivid"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "natural"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Headers"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Query"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "httpx._config.Timeout", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["openai.types.images_response.ImagesResponse"], "extra_attrs": null, "type_ref": "openai._response.APIResponse"}], "extra_attrs": null, "type_ref": "openai._response.ResponseContextManager"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.resources.images.ImagesWithStreamingResponse.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "openai.resources.images.ImagesWithStreamingResponse", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef", "module_public": false}, "Literal": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.Literal", "kind": "Gdef", "module_public": false}, "Mapping": {".class": "SymbolTableNode", "cross_ref": "typing.Mapping", "kind": "Gdef", "module_public": false}, "NOT_GIVEN": {".class": "SymbolTableNode", "cross_ref": "openai._types.NOT_GIVEN", "kind": "Gdef", "module_public": false}, "NotGiven": {".class": "SymbolTableNode", "cross_ref": "openai._types.NotGiven", "kind": "Gdef", "module_public": false}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef", "module_public": false}, "Query": {".class": "SymbolTableNode", "cross_ref": "openai._types.Query", "kind": "Gdef", "module_public": false}, "SyncAPIResource": {".class": "SymbolTableNode", "cross_ref": "openai._resource.SyncAPIResource", "kind": "Gdef", "module_public": false}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef", "module_public": false}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "openai.resources.images.__all__", "name": "__all__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.resources.images.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.resources.images.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.resources.images.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.resources.images.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.resources.images.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.resources.images.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "_legacy_response": {".class": "SymbolTableNode", "cross_ref": "openai._legacy_response", "kind": "Gdef", "module_public": false}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef", "module_public": false}, "async_maybe_transform": {".class": "SymbolTableNode", "cross_ref": "openai._utils._transform.async_maybe_transform", "kind": "Gdef", "module_public": false}, "async_to_streamed_response_wrapper": {".class": "SymbolTableNode", "cross_ref": "openai._response.async_to_streamed_response_wrapper", "kind": "Gdef", "module_public": false}, "cached_property": {".class": "SymbolTableNode", "cross_ref": "openai._compat.cached_property", "kind": "Gdef", "module_public": false}, "cast": {".class": "SymbolTableNode", "cross_ref": "typing.cast", "kind": "Gdef", "module_public": false}, "deepcopy_minimal": {".class": "SymbolTableNode", "cross_ref": "openai._utils._utils.deepcopy_minimal", "kind": "Gdef", "module_public": false}, "extract_files": {".class": "SymbolTableNode", "cross_ref": "openai._utils._utils.extract_files", "kind": "Gdef", "module_public": false}, "httpx": {".class": "SymbolTableNode", "cross_ref": "httpx", "kind": "Gdef", "module_public": false}, "image_create_variation_params": {".class": "SymbolTableNode", "cross_ref": "openai.types.image_create_variation_params", "kind": "Gdef", "module_public": false}, "image_edit_params": {".class": "SymbolTableNode", "cross_ref": "openai.types.image_edit_params", "kind": "Gdef", "module_public": false}, "image_generate_params": {".class": "SymbolTableNode", "cross_ref": "openai.types.image_generate_params", "kind": "Gdef", "module_public": false}, "make_request_options": {".class": "SymbolTableNode", "cross_ref": "openai._base_client.make_request_options", "kind": "Gdef", "module_public": false}, "maybe_transform": {".class": "SymbolTableNode", "cross_ref": "openai._utils._transform.maybe_transform", "kind": "Gdef", "module_public": false}, "to_streamed_response_wrapper": {".class": "SymbolTableNode", "cross_ref": "openai._response.to_streamed_response_wrapper", "kind": "Gdef", "module_public": false}}, "path": "/home/<USER>/.pyenv/versions/3.12.5/lib/python3.12/site-packages/openai/resources/images.py"}