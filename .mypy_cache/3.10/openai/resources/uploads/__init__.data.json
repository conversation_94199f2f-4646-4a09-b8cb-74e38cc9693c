{".class": "MypyFile", "_fullname": "openai.resources.uploads", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AsyncParts": {".class": "SymbolTableNode", "cross_ref": "openai.resources.uploads.parts.AsyncParts", "kind": "Gdef"}, "AsyncPartsWithRawResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.uploads.parts.AsyncPartsWithRawResponse", "kind": "Gdef"}, "AsyncPartsWithStreamingResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.uploads.parts.AsyncPartsWithStreamingResponse", "kind": "Gdef"}, "AsyncUploads": {".class": "SymbolTableNode", "cross_ref": "openai.resources.uploads.uploads.AsyncUploads", "kind": "Gdef"}, "AsyncUploadsWithRawResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.uploads.uploads.AsyncUploadsWithRawResponse", "kind": "Gdef"}, "AsyncUploadsWithStreamingResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.uploads.uploads.AsyncUploadsWithStreamingResponse", "kind": "Gdef"}, "Parts": {".class": "SymbolTableNode", "cross_ref": "openai.resources.uploads.parts.Parts", "kind": "Gdef"}, "PartsWithRawResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.uploads.parts.PartsWithRawResponse", "kind": "Gdef"}, "PartsWithStreamingResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.uploads.parts.PartsWithStreamingResponse", "kind": "Gdef"}, "Uploads": {".class": "SymbolTableNode", "cross_ref": "openai.resources.uploads.uploads.Uploads", "kind": "Gdef"}, "UploadsWithRawResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.uploads.uploads.UploadsWithRawResponse", "kind": "Gdef"}, "UploadsWithStreamingResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.uploads.uploads.UploadsWithStreamingResponse", "kind": "Gdef"}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "openai.resources.uploads.__all__", "name": "__all__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.resources.uploads.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.resources.uploads.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.resources.uploads.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.resources.uploads.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.resources.uploads.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.resources.uploads.__path__", "name": "__path__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.resources.uploads.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}}, "path": "/home/<USER>/.pyenv/versions/3.12.5/lib/python3.12/site-packages/openai/resources/uploads/__init__.py"}