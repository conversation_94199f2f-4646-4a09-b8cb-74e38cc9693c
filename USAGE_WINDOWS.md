# FlowRAG Library - Windows Usage Guide

This guide provides Windows-specific instructions for setting up and using FlowRAG, with detailed installation steps for optimal performance.

## Table of Contents

1. [Development Environment Setup](#1-development-environment-setup)
   - [1.1. Install and Use WSL (Windows Subsystem for Linux)](#11-install-and-use-wsl-windows-subsystem-for-linux)
   - [1.2. Install Python 3.12 in WSL](#12-install-python-312-in-wsl)
   - [1.3. Create an Alias for Python 3.12](#13-create-an-alias-for-python-312)
2. [Project Configuration](#2-project-configuration)
   - [2.1. Create and Activate Virtual Environment](#21-create-and-activate-virtual-environment)
   - [2.2. Install Dependencies](#22-install-dependencies)
3. [Alternative: Native Windows Installation](#3-alternative-native-windows-installation)
4. [Usage Instructions](#4-usage-instructions)
5. [Troubleshooting](#5-troubleshooting)

---

## 1. Development Environment Setup

### 1.1. Install and Use WSL (Windows Subsystem for Linux)

**⭐ Recommended Approach**: WSL allows you to run a GNU/Linux environment directly on Windows without the overhead of a traditional virtual machine, providing better compatibility with FlowRAG dependencies.

**Prerequisites:**

- Windows 10 version 2004 and higher (Build 19041 and higher) or Windows 11

**Installation Steps:**

1. **Open PowerShell or Command Prompt as Administrator:**

   - Search for "PowerShell" or "CMD" in the Start menu
   - Right-click the result and select "Run as administrator"

2. **Enable required features:**
   Execute the following commands one after another:

   ```bash
   dism.exe /online /enable-feature /featurename:Microsoft-Windows-Subsystem-Linux /all /norestart
   dism.exe /online /enable-feature /featurename:VirtualMachinePlatform /all /norestart
   ```

3. **Restart your computer:**
   This step is crucial for the changes to take effect.

4. **Download and install the Linux kernel update package:**

   - Visit Microsoft's documentation page: [Install WSL](https://learn.microsoft.com/en-us/windows/wsl/install-manual#step-4---download-the-linux-kernel-update-package)
   - Download and run the latest Linux kernel update package installer

5. **Set WSL 2 as your default version:**
   Open PowerShell (can be as normal user this time) and execute:

   ```bash
   wsl --set-default-version 2
   ```

   > **Note:** If you encounter any errors, you may need to enable virtualization in your computer's BIOS/UEFI.

6. **Install a Linux distribution of your choice:**
   - Open the Microsoft Store
   - Search for a Linux distribution (e.g., "Ubuntu", "Debian", "Fedora"). We recommend **Ubuntu**
   - Click "Get" or "Install" for the desired distribution
   - After installation, launch the distribution from the Start menu. The first time you start it, you'll be prompted to create a username and password for the Linux environment

**Using WSL:**

- After setup, you can start your Linux distribution directly from the Start menu (e.g., "Ubuntu")
- This will open a Linux terminal where you can execute Linux commands

### 1.2. Install Python 3.12 in WSL

With WSL and your Linux distribution (e.g., Ubuntu) installed, install Python 3.12.

**Installation Steps (Example with Ubuntu/Debian):**

1. **Open your Linux distribution terminal in WSL**

2. **Update the package list:**

   ```bash
   sudo apt update
   ```

3. **Install Python 3.12:**

   - For Ubuntu/Debian-based distributions, Python 3.12 can be installed through the `deadsnakes` PPA (Personal Package Archive)
   - Install `software-properties-common` (if not already installed):
     ```bash
     sudo apt install software-properties-common -y
     ```
   - Add the `deadsnakes` PPA:
     ```bash
     sudo add-apt-repository ppa:deadsnakes/ppa -y
     ```
   - Update the package list again:
     ```bash
     sudo apt update
     ```
   - Install Python 3.12, PIP for Python 3.12, and the `venv` package (for virtual environments):
     ```bash
     sudo apt install python3.12 python3.12-pip python3.12-venv -y
     ```
   - If you're using another distribution (e.g., Fedora), check its documentation for specific commands (e.g., `sudo dnf install python3.12`)

4. **Verify Python 3.12 installation:**

   ```bash
   python3.12 --version
   ```

   The output should be something like: `Python 3.12.x`

5. **Verify PIP installation for Python 3.12:**
   ```bash
   pip3.12 --version
   ```
   This will show the PIP version and installation path

### 1.3. Create an Alias for Python 3.12

To use the `python` command instead of `python3.12`, create a symbolic link.

**Steps:**

1. **In the WSL terminal, identify the Python 3.12 path:**

   ```bash
   which python3.12
   ```

   This should return the path (e.g., `/usr/bin/python3.12`)

2. **Create the symbolic link:**
   Use the path returned in the previous command:

   ```bash
   # Replace /usr/bin/python3.12 with the exact path returned by 'which python3.12' if different
   sudo ln -s $(which python3.12) /usr/local/bin/python
   ```

   Alternatively, if `$(which python3.12)` doesn't work or to be explicit (assuming the path is `/usr/bin/python3.12`):

   ```bash
   sudo ln -s /usr/bin/python3.12 /usr/local/bin/python
   ```

3. **Verify the alias:**
   Close and reopen your WSL terminal, or execute `hash -r`. Then:
   ```bash
   python --version
   ```
   The output should be: `Python 3.12.x`

---

## 2. Project Configuration

Now that your WSL environment is configured with Python 3.12, and you're already in the project directory, configure it.

### 2.1. Create and Activate Virtual Environment

It's a good practice to use virtual environments to isolate project dependencies.

1. **Navigate to the project root folder in the WSL terminal (if not already there)**
2. **Create a virtual environment (if it doesn't exist yet):**
   We recommend naming it `venv`:
   ```bash
   python -m venv venv
   ```
3. **Activate the virtual environment:**
   ```bash
   source venv/bin/activate
   ```
   Your terminal prompt should change to indicate that the virtual environment is active (e.g., `(venv) your_user@machine:...$`)

### 2.2. Install Dependencies

With the virtual environment active, install the necessary Python libraries listed in the `requirements.txt` file.

```bash
# Install PyTorch CPU-only first (to avoid CUDA downloads)
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cpu

# Install FlowRAG dependencies
pip install -r requirements.txt
```

---

## 3. Alternative: Native Windows Installation

If you prefer not to use WSL, you can install FlowRAG directly on Windows (at your own risk):

### Prerequisites

- **Python 3.10 or higher** - Download from [python.org](https://www.python.org/downloads/windows/)
  - ⚠️ **Important**: During installation, check "Add Python to PATH"
- **Git for Windows** - Download from [git-scm.com](https://git-scm.com/download/win)
- **Microsoft C++ Build Tools** - Download from [Visual Studio Downloads](https://visualstudio.microsoft.com/visual-cpp-build-tools/)

### Installation Steps

```cmd
# Clone the repository
git clone https://bitbucket.org/ciandt_it/pyflow_rag.git
cd pyflow_rag

# Create virtual environment
python -m venv venv

# Activate virtual environment
venv\Scripts\activate

# Install PyTorch CPU-only first
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cpu

# Install dependencies
pip install -r requirements.txt
```

## 4. Usage Instructions

### Environment Variables Setup

Regardless of whether you use WSL or native Windows, configure the required environment variables:

```bash
# In WSL or .env file
JINA_API_KEY=your-jina-api-key
CLIENT_ID=your-client-id
CLIENT_SECRET=your-client-secret
APP_TO_ACCESS=your-app-to-access
FLOW_TENANT=your-flow-tenant
STORE_BASE_PATH=/path/to/your/vector_store  # WSL paths
LOGS_BASE_PATH=/path/to/your/logs
```

⚠️ **Windows Path Note**: Use forward slashes `/` or double backslashes `\\` in paths:

- ✅ Good: `C:/Users/<USER>/flowrag_data`
- ✅ Good: `C:\\Users\\<USER>\\flowrag_data`
- ❌ Bad: `C:\Users\<USER>\flowrag_data`

---

### Running FlowRAG

**📚 For complete usage instructions, examples, and API documentation, please refer to [USAGE.md](USAGE.md).**

The main usage guide covers:

- ✅ **Chat Interface and Command-Line Interface**
- ✅ **Library Usage with Examples**
- ✅ **Multiple Output Formats**
- ✅ **Advanced Options and Configuration**
- ✅ **Supported Languages and Special Files**

### Quick Example

```python
import flow_rag

# Initialize the codebase (indexes if needed)
flow_rag.initialize("/path/to/your/codebase")

# Query with AI processing
response = flow_rag.query(
    "How does the authentication flow work?",
    "/path/to/your/codebase"
)
print(response)
```

---

## 5. Troubleshooting

### WSL-Specific Issues

1. **WSL installation fails:**

   - Ensure Windows version compatibility (Windows 10 2004+ or Windows 11)
   - Enable virtualization in BIOS/UEFI
   - Run PowerShell as Administrator

2. **Python 3.12 not found:**

   - Verify the `deadsnakes` PPA was added correctly
   - Try: `sudo apt update && sudo apt install python3.12`

3. **Permission issues in WSL:**
   - Use `sudo` for system-wide installations
   - Check file permissions: `ls -la`

### Native Windows Issues

1. **"Python not found" error:**

   - Ensure Python is added to PATH during installation
   - Restart Command Prompt after Python installation

2. **"Microsoft Visual C++ 14.0 is required" error:**

   - Install Microsoft C++ Build Tools

3. **Path too long errors:**
   - Enable long path support in Windows
   - Use shorter directory names

### General Issues

1. **Virtual environment activation:**

   - WSL: `source venv/bin/activate`
   - Windows: `venv\Scripts\activate`

2. **Package installation errors:**

   - Update pip: `pip install --upgrade pip`
   - Use trusted hosts if SSL issues occur

3. **FlowRAG import errors:**
   - Ensure virtual environment is activated
   - Verify all dependencies are installed
   - Check Python version: `python --version`

### Getting Help

If you encounter issues:

1. **Check logs:** Look in your configured logs directory
2. **Verify environment:** Ensure all environment variables are set
3. **Test Python:** Run `python --version` (should be 3.10+)
4. **Check virtual environment:** Ensure it's activated and working

For more detailed troubleshooting and usage examples, see [USAGE.md](USAGE.md).
