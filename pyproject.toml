[build-system]
requires = ["setuptools>=42", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "flow_rag"
version = "1.0.0"
description = "A powerful library for semantic code search and natural language querying of codebases with hybrid search capabilities"
authors = [
    {name = "<PERSON><PERSON><PERSON>", email = "<EMAIL>"}
]
readme = "README.md"
requires-python = ">=3.10"
classifiers = [
    "Development Status :: 3 - Alpha",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.10",
]
dependencies = [
    "flask>=2.0.0",
    "markdown>=3.0.0",
    "numpy<2.0.0",
    "openai>=1.0.0",
    "python-dotenv>=1.0.0",
    "requests>=2.28.0",
    "tenacity>=8.0.0",
    "tiktoken>=0.5.0",
    "tree-sitter==0.21.3",
    "tree_sitter_languages>=1.0.0",
    "tqdm>=4.64.0",
]

[project.urls]
Homepage = "https://bitbucket.org/ciandt_it/pyflow_rag"

[project.optional-dependencies]
dev = [
    "pytest>=7.0.0",
    "black>=22.0.0",
    "isort>=5.0.0",
    "mypy>=1.0.0",
    "flake8>=4.0.0",
]

[tool.black]
line-length = 88
target-version = ["py38"]
include = '\.pyi?$'

[tool.isort]
profile = "black"
multi_line_output = 3

[tool.mypy]
python_version = "3.8"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py"]
addopts = "-v" 